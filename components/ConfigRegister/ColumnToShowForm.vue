<script setup lang="ts">

import { CONFIG_REGISTER_NEW_COLUMN_TO_SHOW } from '~/constants/default-values';
import type { Option } from '~/types';
import { RegisterColumnOrder, RegisterColumnType, type RegisterColumn } from '~/types/register';
const props = defineProps({
    modelValue: {
        type: Object as PropType<RegisterColumn[]>,
        default: () => []
    },
    fieldNameOptions: {
        type: Array as PropType<Option[]>,
        default: []
    }
})


const emit = defineEmits(['update:modelValue'])

const columnTypeOptions = computed(() =>
{
    return Object.values(RegisterColumnType).map((value) => ({
        label: value,
        value
    }))
})

const columnOrderOptions = computed(() =>
{
    return Object.values(RegisterColumnOrder).map((value) => ({
        label: value,
        value
    }))
})

function clickHandlerDelete(index: number)
{
    const newModelValue = useCloneDeep(props.modelValue)
    newModelValue.splice(index, 1)
    emit('update:modelValue', newModelValue)
}

function clickHandlerAdd()
{
    const newModelValue = useCloneDeep(props.modelValue)
    newModelValue.push(CONFIG_REGISTER_NEW_COLUMN_TO_SHOW)
    emit('update:modelValue', newModelValue)
}

function updateHandlerType(type: RegisterColumnType, index: number)
{
    const newModelValue = useCloneDeep(props.modelValue)
    newModelValue[index].type = type
    emit('update:modelValue', newModelValue)
}

function updateHandlerShow(showFieldName: string, index: number)
{
    const newModelValue = useCloneDeep(props.modelValue)
    newModelValue[index].show = showFieldName
    emit('update:modelValue', newModelValue)
}

function updateHandlerOrder(order: RegisterColumnOrder, index: number)
{
    const newModelValue = useCloneDeep(props.modelValue)
    newModelValue[index].order = order
    emit('update:modelValue', newModelValue)
}

const isFiedlNamenotInList = (fieldName: string) =>
{
    return props.fieldNameOptions.some((option) => option.value === fieldName)
}

const getWarningMessage = (fieldName: string) =>
{
    return `${fieldName} not in list`
}
</script>

<template>
    <FormBuilderSpecificOptionHeader label="Column To Show" @add="clickHandlerAdd" />
    <Form>
        <BaseLayoutRowFormHeader grid-layout="1.75fr 1fr 0.5fr 65px 50px 85px 50px">
            <span>Field Name</span>
            <span>Display Name</span>
            <span>Type</span>
            <span>Span</span>
            <span>Sortable</span>
            <span>Default Sort</span>
            <span>Actions</span>
        </BaseLayoutRowFormHeader>
        <BaseLayoutRowForm grid-layout="1.75fr 1fr 0.5fr 65px 50px 85px 50px" v-for="column, index in modelValue">
            <div class="fieldName">
                <FieldInput class="field">
                    <Select :model-value="modelValue[index].show" @update:model-value="updateHandlerShow($event, index)"
                        clearable transfer class="select">
                        <Option v-for="option in fieldNameOptions" :value="option.value"
                            :key="option.value + option.description" :label="option.label">
                            <span>
                                {{ option.label }}</span>
                            <span style="float: right; color: #ccc;">{{ option.description }}
                            </span>
                        </Option>
                    </Select>
                </FieldInput>
                <BaseWarningIcon v-if="!isFiedlNamenotInList(modelValue[index].show)"
                    :text="getWarningMessage(modelValue[index].show)" />
            </div>
            <FieldInputText v-model="modelValue[index].label" />
            <FieldInput class="field">
                <Select :model-value="modelValue[index].type" @update:model-value="updateHandlerType($event, index)"
                    clearable transfer class="select">
                    <Option v-for="option in columnTypeOptions" :value="option.value" :key="option.value"
                        :label="option.label">
                        <span>
                            {{ option.label }}</span>
                    </Option>
                </Select>
            </FieldInput>
            <FieldInputNumber v-model="modelValue[index].span" />
            <FieldInputSwitch v-model="modelValue[index].sortable" />
            <FieldInput class="field">
                <Select :model-value="modelValue[index].order" @update:model-value="updateHandlerOrder($event, index)"
                    clearable transfer class="select">
                    <Option v-for="option in columnOrderOptions" :value="option.value" :key="option.value"
                        :label="option.label">
                        <span>
                            {{ option.label }}</span>
                    </Option>
                </Select>
            </FieldInput>
            <BaseButtonDelete @click="clickHandlerDelete(index)" class="formButton" />
        </BaseLayoutRowForm>
    </Form>
</template>

<style lang="scss" scoped>
.formButton {
    margin-bottom: 24px;
}

.fieldName {
    display: flex;
    justify-content: space-between;
    gap: $unit;
}

.field {
    width: 100%;
    max-width: 100%;


    .select {
        overflow: auto;
        text-overflow: ellipsis;
    }
}
</style>