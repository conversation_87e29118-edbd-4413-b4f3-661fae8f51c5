<script setup lang="ts">
const { newDocumentTableField } = useFormBuilderFieldSetting()
</script>

<template>
    <FormBuilderFieldSettingLabel>New Document Table Settings</FormBuilderFieldSettingLabel>
    <template v-if="newDocumentTableField">
        <Form label-position="top">
            <FieldInputSwitch v-model="newDocumentTableField.departmentDocumentOwner"
                label="Select Document Owner from Department" />
            <FormBuilderSpecificOptionSettings v-model="newDocumentTableField.options"
                header-label="New Document Names" />
        </Form>
    </template>
</template>