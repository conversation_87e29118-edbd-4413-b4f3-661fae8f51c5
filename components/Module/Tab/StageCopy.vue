<script setup lang="ts">
  import type { OptionGroup } from '@/types'
  import type { Stage } from '~/types/data'

  const stagesStore = useStagesStore()
  const { workflows } = storeToRefs(useWorkflowsStore())
  const workflowStages = ref([] as Stage[])
  const uiStore = useUiStore()

  const groups = computed(() => {
    const workflowsByTenant = useGroupBy(workflows.value, 'tenant')
    const groupedTenantOptions: OptionGroup = {}
    Object.keys(workflowsByTenant).forEach((tenant) => {
      groupedTenantOptions[tenant] = workflowsByTenant[tenant].map((workflow) => {
        return {
          label: `${workflow.tenant}: ${workflow.name} ${workflow.version}`,
          value: workflow.key,
        }
      })
    })
    return groupedTenantOptions
  })

  const selectedWorkflowKey = ref('')
  const isWorkflowSelected = computed(() => !!selectedWorkflowKey.value)
  watch(selectedWorkflowKey, (newValue) => {
    workflowStages.value = stagesStore.getWorkflowStagesByWorkflowKey(newValue)
  })

  async function clickHandlerCopy() {
    uiStore.setLoading(true, 'Copying workflow stages...')
    await stagesStore.copyWorkflowStages(selectedWorkflowKey.value)
    uiStore.setLoading(false)
  }
</script>

<template>
  <div class="moduleTabStagesCopy">
    <Form label-position="top">
      <FieldInputSelectGroup
        label="Select Workflow to Copy"
        :groups="groups"
        v-model="selectedWorkflowKey"
        readonly
        clearable />
    </Form>
    <div class="stages" v-if="isWorkflowSelected">
      <div class="stage" v-for="stage in workflowStages" :key="stage.key">
        {{ stage.name }}
      </div>
      <BaseButton appearance="accent" @click="clickHandlerCopy" class="button">Copy</BaseButton>
    </div>
  </div>
</template>

<style lang="scss" scoped>
  .button {
    margin-top: $margin;
  }

  .moduleTabStagesCopy {
    margin-top: $margin * 2;
  }

  .stages {
    display: flex;
    flex-direction: column;
    gap: $unit;
  }

  .stage {
    border: 1px solid rgb(190, 190, 190);
    padding: $unit;
  }
</style>
