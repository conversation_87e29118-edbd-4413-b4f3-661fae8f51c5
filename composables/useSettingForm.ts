export function useSettingForm<T>({
  form,
  activeData,
  updateHandler,
}: {
  form: Ref<T>;
  activeData: Ref<T>;
  updateHandler: () => Promise<void>;
}) {
  const uiStore = useUiStore();

  onMounted(() => {
    form.value = useCloneDeep(activeData.value);
  });

  watch(activeData, (newValue) => {
    form.value = useCloneDeep(newValue);
  });

  function resetHandler() {
    form.value = useCloneDeep(activeData.value);
  }

  async function submitHandler() {
    uiStore.setLoading(true, "Saving Setting...");
    await updateHandler();
    uiStore.setLoading(false);
  }

  return {
    resetHandler,
    submitHandler,
  };
}
