import { useSharepointAPIUtils } from '~/server/utils/sharepointAPI'

export default defineEventHandler(async (event) => {
  const { fetchSharepointAPIWithTenant } = useSharepointAPIUtils()
  try {
    const query = getQuery(event)
    const { tenantUrl, tenantId, siteUrl } = query
    console.log('Query parameters:', query)
    if (!tenantUrl || !siteUrl) {
      return {
        ok: false,
        errorMessage: `Missing required query parameters: 'tenant' or 'siteUrl'.`,
      }
    }

    const url = `${siteUrl}/_api/web/siteusers`
    const response = await fetchSharepointAPIWithTenant(
      url,
      tenantUrl as string,
      tenantId as string,
    )

    const data = await response.json()
    return {
      ok: true,
      data: data.d.results,
      message: `Fetched ${siteUrl} users successfully.`,
    }
  } catch (error: any) {
    console.error('Failed to retrieve user from site collection:', error)
    return {
      ok: false,
      errorMessage: `${error}`,
    }
  }
})
