import type { Core, CoreSiteColumn, CoreList } from '~/types/core'
import { useStoreUtils } from './utils/storeFetchUtils'
import { CORE_NEW_SITECOLUMN } from '~/constants/default-values'
import { useApiMutation } from '~/stores/utils/apiMutation'

export const useCoresStore = defineStore('cores', () => {
  const { convertFirebaseDataToArray } = useStoreUtils()

  // ----------------- State -----------------
  const cores = ref<Core[]>([])
  const activeCoreKey = ref<string>('')
  const coresByKey = computed(() => useKeyBy(cores.value, 'key'))
  const activeCore = computed(() => coresByKey.value[activeCoreKey.value] || ({} as Core))
  const activeCoreLists = ref<CoreList[]>([])

  // ----------------- Computed -----------------
  const activeCoreSiteColumns = computed((): CoreSiteColumn[] => {
    return activeCore.value?.siteColumns?.map((col, i) => ({ ...col, id: i })) ?? []
  })

  const activeCoreSiteColumnsJSON = computed((): string => {
    return activeCore.value?.siteColumns
      ? JSON.stringify(activeCore.value.siteColumns, null, 2)
      : ''
  })

  // ----------------- callabacks -----------------
  /**
   * Function ran every time there's changes on `cores`
   * @param firebaseCoreData
   */

  const coresCallback = (firebaseCoreData: FirebaseData<Partial<Core>>): void => {
    cores.value = convertFirebaseDataToArray(firebaseCoreData)
  }

  const updateCore = async (coreKey: string, coreData: Partial<Core>): Promise<void> =>
    await useApiMutation('/api/cores', {
      method: 'PATCH',
      body: { coreKey, coreData },
    })

  const getCoreName = (coreKey: string): string => coresByKey.value[coreKey]?.name ?? ''

  const setCore = (coreKey: string): void => {
    activeCoreKey.value = useCloneDeep(coreKey)
  }

  // ----------------- Site Columns -----------------
  const addCoreSiteColumn = (): void => {
    if (!activeCore.value) return

    activeCore.value.siteColumns.push({
      ...CORE_NEW_SITECOLUMN,
      id: activeCore.value.siteColumns.length,
    })
  }

  const deleteCoreSiteColumn = (index: number): void => {
    activeCore.value?.siteColumns?.splice(index, 1)
  }

  const updateCoreSiteColumns = async (
    coreKey: string,
    siteColumns: CoreSiteColumn[],
  ): Promise<unknown> =>
    await useApiMutation('/api/cores/siteColumns', {
      method: 'POST',
      body: { coreKey, siteColumns },
    })

  // ----------------- Lists -----------------
  const deleteCoreList = (index: number): void => {
    activeCore.value?.lists?.splice(index, 1)
  }

  const updateCoreLists = async (coreKey: string, lists: CoreList[]): Promise<unknown> =>
    await useApiMutation('/api/cores/lists', {
      method: 'POST',
      body: { coreKey, lists },
    })

  const updateCoreListSiteColumns = async (
    coreKey: string,
    listId: number,
    siteColumns: CoreSiteColumn[],
  ): Promise<unknown> => {
    const result = await useApiMutation('/api/cores/lists/siteColumns', {
      method: 'POST',
      body: { coreKey, id: listId, siteColumns },
    })

    if (activeCore.value?.lists?.[listId]) {
      activeCore.value.lists[listId].SiteColumns = siteColumns
    }

    return result
  }

  return {
    // callbacks

    coresCallback,
    // state
    cores,
    coresByKey,
    activeCore,
    activeCoreLists,
    activeCoreSiteColumns,
    activeCoreSiteColumnsJSON,

    // actions
    updateCore,
    setCore,
    getCoreName,

    // site columns
    addCoreSiteColumn,
    deleteCoreSiteColumn,
    updateCoreSiteColumns,

    // lists
    deleteCoreList,
    updateCoreLists,
    updateCoreListSiteColumns,
  }
})
