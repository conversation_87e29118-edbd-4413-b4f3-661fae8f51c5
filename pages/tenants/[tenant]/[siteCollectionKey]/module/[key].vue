<script setup lang="ts">
  const route = useRoute()
  const siteCollectionsStore = useSiteCollectionsStore()
  const modulesStore = useModulesStore()
  const workflowsStore = useWorkflowsStore()
  const { activeWorkflowKey } = storeToRefs(workflowsStore)
  const isWorkflowFound = computed(() => {
    return activeWorkflowKey.value !== ''
  })
  const stagesStore = useStagesStore()
  const uiStore = useUiStore()
  const tenantStore = useTenantsStore()
  const sharepointAPIStore = useSharepointAPIStore()
  const upVersionStore = useUpVersionStore()
  const showVersionDrawer = ref(false)
  const moduleKey = route.params.key as string
  const tenant = route.params.tenant as string
  const siteCollectionKey = route.params.siteCollectionKey as string
  const activeTenant = computed(() => {
    return tenantStore.getTenantFromName(tenant)
  })
  const activeSite = computed(() => {
    return siteCollectionsStore.getSiteByKey(siteCollectionKey)
  })
  useRealtimeData('/workflows', workflowsStore.workflowsCallback)
  useRealtimeData('/stages', stagesStore.stagesCallback)

  onMounted(() => {
    modulesStore.setActiveModuleKey(moduleKey)
    siteCollectionsStore.setTenant(tenant)
    siteCollectionsStore.setActiveSite(siteCollectionKey)
  })

  watch(
    [activeTenant, activeSite],
    async ([activeTenant, activeSite]) => {
      if (activeTenant && activeSite) {
        sharepointAPIStore.resetSharepointAPIState()
        sharepointAPIStore.setSharepointAPIState(
          activeTenant.tenant,
          activeTenant.tenantId,
          activeSite.url,
        )
        uiStore.setLoading(true, `Fetching Sharepoint API...`)
        await Promise.all([
          sharepointAPIStore.fetchLists(),
          sharepointAPIStore.fetchSiteGroups(),
          sharepointAPIStore.fetchUsers(),
        ])
        uiStore.setLoading(false)
      }
    },
    { immediate: true },
  )

  async function clickHandlerInitWorkflow() {
    uiStore.setLoading(true, `Creating new workflow for module...`)
    await workflowsStore.createNewWorkflow(moduleKey)
    uiStore.setLoading(false)
  }

  async function clickHandlerDeleteModule() {
    uiStore.setLoading(true, `Deleting module...`)
    await modulesStore.deleteModule(moduleKey)
    await siteCollectionsStore.removeModuleFromSiteCollection(siteCollectionKey, moduleKey)
    navigateTo(`/tenants/${tenant}/${siteCollectionKey}`)
    uiStore.setLoading(false)
  }

  function clickHandlerVersion() {
    upVersionStore.initialiseUpVersionForWorkflow()
    showVersionDrawer.value = true
  }

  function closeHandler() {
    showVersionDrawer.value = false
  }
</script>

<template>
  <template v-if="isWorkflowFound">
    <ModuleModalStageUpdate />
    <ModuleDrawerWorkflowVersion v-if="showVersionDrawer" @close="closeHandler" />
    <BaseLayoutPage>
      <template #header>
        <ModuleLayoutHeader @click="clickHandlerVersion" />
      </template>
      <template #content>
        <ModuleTabs />
      </template>
    </BaseLayoutPage>
  </template>
  <template v-else>
    <AppBreadCrumbs />
    <BaseNotFoundPage text="Workflow not found">
      <BaseButtonAdd @click="clickHandlerInitWorkflow" text="Workflow" />
      <BaseButtonDeleteText text="Delete Module" @click="clickHandlerDeleteModule" />
    </BaseNotFoundPage>
  </template>
</template>
