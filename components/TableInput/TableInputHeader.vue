<script setup lang="ts">
import { computed, type PropType } from 'vue'
import { type CustomOptionData } from '~/types/form'

const props = defineProps({
  options: {
    type: Array as PropType<CustomOptionData[]>,
    required: true
  },
  autoSerialize: {
    type: Boolean,
    default: false
  }
})

// Computed column list excluding hidden options
const columns = computed(() =>
{
  return props.options.filter(opt => !opt.hidden)
})

// Get column span based on option settings
function getSpan(opt: CustomOptionData): number
{
  if (opt && opt.customColSpan) return opt.colSpan ?? 1
  if (opt.span) return opt.span
  const max = 23
  return Math.round(max / props.options.length)
}
</script>

<template>
  <Row class="tableInputHeader">
    <Col v-if="props.autoSerialize" :span="1" class="column--serial">
    <label>No.</label>
    </Col>
    <Col :span="21">
    <Row>
      <Col v-for="(opt, index) in columns" :key="index" class="column" :span="getSpan(opt)">
      <label>{{ opt.name }}</label>
      </Col>
    </Row>
    </Col>

    <Col :span="2" class="column">
    <label>Actions</label>
    </Col>
  </Row>
</template>

<style lang="scss" scoped>
.tableInputHeader {
  border-bottom: 1px solid $primary-charcoal-50;
  text-align: left;
  font-weight: bold;
  padding: $unit;
}

.column {
  padding: 5px 10px;

  label {
    font-size: 0.9rem;
  }

  &--serial {
    padding: 5px 0px;
    font-weight: 600;
  }
}
</style>
