<script setup type="ts">
import { Message } from 'view-ui-plus'
const { loginWithEmail } = useAuth()
const email = ref('')
const password = ref('')
const uiStore = useUiStore()

async function clickHandlerSignIn()
{
  uiStore.setLoading(true, `Logging in with ${email.value}`)
  const user = await loginWithEmail(email.value, password.value)
  if (!user)
    Message.error('Login Failed: incorrect credentials')
  else
  {
    Message.success(`${email.value} logged in: redirecting to tenant page`)
    navigateTo('/tenants')
  }
  uiStore.setLoading(false)
}
</script>

<template>
  <div class="loginForm">
    <fluent-text-field class="field" appearance="outline" placeholder="email" v-model="email" id="email" />
    <fluent-text-field class="field" appearance="outline" placeholder="*******" type="password" name="password"
      v-model="password" id="password" />
    <BaseButton class="button" @click="clickHandlerSignIn" appearance='accent'>Sign In</BaseButton>
  </div>
</template>

<style lang="scss" scoped>
.loginForm {
  display: flex;
  flex-direction: column;
  gap: $unit;
  width: 300px;
}

.field {
  flex-grow: 1
}

.button {
  flex-grow: 1
}
</style>