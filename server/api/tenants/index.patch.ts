// UPDATE Tenant
// body { tenantKey: string, tenantData: Tenant }

import { Tenant } from 'firebase-admin/auth'
import { useAuthenticatedFirebase } from '~/server/utils/firebaseDatabase'

export default defineEventHandler(async (event) => {
  const { tenantKey, tenantData } = await readBody(event)

  try {
    const { firebaseUpdate } = useAuthenticatedFirebase(event)
    await firebaseUpdate(`/tenants/${tenantKey}`, tenantData)
    return {
      ok: true,
      message: `Tenant with key: ${tenantKey} updated successfully`,
    }
  } catch (error) {
    return {
      ok: false,
      errorMessage: error,
    }
  }
})
