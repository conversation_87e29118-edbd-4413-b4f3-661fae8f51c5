<script setup lang="ts">
  import { CORE_NEW_LIST } from '~/constants/default-values'
  import { type CoreList } from '~/types/core'

  const props = defineProps({
    modelValue: {
      type: Object as PropType<CoreList[]>,
      required: true,
      default: () => [],
    },
  })

  const emit = defineEmits(['update:modelValue'])
  const { coreTemplateOptions } = useSelectOptionsUtils()
  const DEFAULT_LIST = { ...CORE_NEW_LIST, id: props.modelValue.length }

  function addHandler() {
    const newModelValue = useCloneDeep(props.modelValue)
    newModelValue.push(DEFAULT_LIST)
    emit('update:modelValue', newModelValue)
  }

  function deleteHandler(index: number) {
    const newModelValue = useCloneDeep(props.modelValue)
    newModelValue.splice(index, 1)
    emit('update:modelValue', newModelValue)
  }
</script>

<template>
  <Form>
    <template v-for="(list, index) in modelValue">
      <BaseLayoutRowForm grid-layout="20px 1fr 1fr 1fr 50px">
        <BaseNumberTag class="formTag"> {{ index + 1 }} </BaseNumberTag>
        <FieldInputText v-model="modelValue[index].DisplayName" />
        <FieldInputText v-model="modelValue[index].InternalName" />
        <FieldInput>
          <Select v-model="modelValue[index].Template" style="width: 100%" transfer>
            <Option v-for="opt in coreTemplateOptions" :value="opt.value" :label="opt.label">
              {{ opt.label }}
            </Option>
          </Select>
        </FieldInput>
        <BaseButtonDelete @click="deleteHandler(index)" class="formButton" />
      </BaseLayoutRowForm>
    </template>
  </Form>
  <BaseButtonAdd @click="addHandler" text="List" />
</template>
