
export const useStoreUtils = () =>
{
  /**
   *
   * @param data
   * @param keyValue
   * @returns
   */
  const convertFirebaseDataToArray = (
    data: { [key: string]: any },
    keyValue: string = "key"
  ) =>
  {
    return Object.keys(data).map((key) =>
    {
      return {
        ...data[key],
        [keyValue]: key,
      };
    });
  };

  return {
    convertFirebaseDataToArray,
  };
};
