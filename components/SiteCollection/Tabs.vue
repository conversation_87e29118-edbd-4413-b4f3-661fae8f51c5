<script setup lang="ts">
  import { SiteCollectionTabNames } from '~/constants/tabs'

  const route = useRoute()
  const { tab } = route.query
  const tabName = tab as string

  const sharepointAPIStore = useSharepointAPIStore()
  const { tenantId } = storeToRefs(sharepointAPIStore)
  const isTenantIdExist = () => {
    return !!tenantId.value
  }
</script>

<template>
  <BaseTabs v-model="tabName">
    <BaseTabPane label="Home" :name="SiteCollectionTabNames.HOME">
      <SiteCollectionTabHome />
    </BaseTabPane>
    <BaseTabPane label="Settings" :name="SiteCollectionTabNames.SETTINGS">
      <SiteCollectionTabSettings />
    </BaseTabPane>
    <BaseTabPane label="Permissions" :name="SiteCollectionTabNames.PERMISSIONS">
      <SiteCollectionTabPermissions />
    </BaseTabPane>
    <BaseTabPane label="Theme" :name="SiteCollectionTabNames.THEME">
      <SiteCollectionTabTheme />
    </BaseTabPane>
    <BaseTabPane label="Custom Navigation" :name="SiteCollectionTabNames.CUSTOM_NAVIGATION">
      <SiteCollectionTabCustomNav />
    </BaseTabPane>
    <BaseTabPane label="DCC" :name="SiteCollectionTabNames.DCC">
      <template v-if="isTenantIdExist()">
        <SiteCollectionTabDCC />
      </template>
      <template v-else>
        <BaseTenantIdNotFound text="Tenant ID not found" />
      </template>
    </BaseTabPane>
    <BaseTabPane label="RCC" :name="SiteCollectionTabNames.RCC">
      <template v-if="isTenantIdExist()">
        <SiteCollectionTabRCC />
      </template>
      <template v-else>
        <BaseTenantIdNotFound text="Tenant ID not found" />
      </template>
    </BaseTabPane>
    <BaseTabPane label="Groups" :name="SiteCollectionTabNames.GROUPS">
      <template v-if="isTenantIdExist()">
        <SiteCollectionTabGroup />
      </template>
      <template v-else>
        <BaseTenantIdNotFound text="Tenant ID not found" />
      </template>
    </BaseTabPane>
    <BaseTabPane label="Deployments" :name="SiteCollectionTabNames.DEPLOYMENTS">
      <template v-if="isTenantIdExist()">
        <SiteCollectionTabDeployment />
      </template>
      <template v-else>
        <BaseTenantIdNotFound text="Tenant ID not found" />
      </template>
    </BaseTabPane>
  </BaseTabs>
</template>
