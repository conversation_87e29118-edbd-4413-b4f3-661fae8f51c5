<script setup lang="ts">
import type { Option } from '~/types';
import fontSizeOptions from '~/content/formSettingFontSizeOption.json'
const fontSizeOption: Option[] = fontSizeOptions.options
const { textField } = useFormBuilderFieldSetting()
</script>

<template>
    <FormBuilderFieldSettingLabel>Text Settings</FormBuilderFieldSettingLabel>
    <template v-if="textField">
        <Form label-position="top">
            <FieldInputSelect v-model="textField.fontSize" label="Font Size" :options="fontSizeOption"
                v-slot="{ option }" value-tag="value">{{ option.label }}</FieldInputSelect>
        </Form>
    </template>
</template>