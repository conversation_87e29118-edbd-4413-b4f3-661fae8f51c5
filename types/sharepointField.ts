// SOURCE
// https://learn.microsoft.com/en-us/previous-versions/office/developer/sharepoint-rest-reference/dn600182(v=office.15)

export type SharepointMetadata = {
  __metadata: {
    id: string;
    uri: string;
    type: string;
  };
};

export type SharepointField = SharepointMetadata & {
  CanBeDeleted: boolean; // Specifies if the field can be deleted.
  DefaultValue?: string; // Default value for the field.
  Description?: string; // Field description.
  Direction?: string; // Reading order.
  EnforceUniqueValues?: boolean; // Requires unique values.
  EntityPropertyName: string; // Entity property name.
  FieldTypeKind: number; // Type of the field (FieldType value).
  Filterable: boolean; // Specifies if filtering is allowed.
  FromBaseType: boolean; // Indicates if the field derives from a base type.
  Group?: string; // Field group.
  Hidden?: boolean; // Specifies if the field is hidden.
  Id: string; // Unique field identifier (GUID).
  Indexed?: boolean; // Specifies if the field is indexed.
  InternalName: string; // Internal name of the field.
  JSLink?: string; // External JS file for rendering logic.
  ReadOnlyField?: boolean; // Specifies if the field is read-only.
  Required?: boolean; // Specifies if the field requires a value.
  SchemaXml?: string; // XML schema defining the field.
  SchemaXmlWithResourceTokens?: string; // Schema with resource tokens.
  Scope?: string; // Server-relative URL of the list/site.
  Sealed?: boolean; // Prevents property changes/deletion.
  Sortable?: boolean; // Specifies if sorting is allowed.
  StaticName?: string; // Customizable field identifier.
  Title?: string; // Display name of the field.
  TypeAsString?: string; // Type of the field (string format).
  TypeDisplayName: string; // Display name of the field type.
  TypeShortDescription: string; // Short description of the field type.
  ValidationFormula?: string; // Data validation criteria.
  ValidationMessage?: string; // Error message for failed validation.
};

// SP.FieldCalculated
export type FieldCalculatedProperty = {
  DateFormat: number; // Date format.
  Formula: string; // Formula for the field.
  OutputType: number; // Output type.
};

// SP.FieldCollection
export type FieldCollectionProperty = {
  SchmeaXml: string; // XML schema defining the field.
};

// SP.FieldComputed
export type FieldComputedProperty = {
  EnableLookup: boolean; // Specifies if lookup is enabled.
};

export type FieldDateTimeProperty = {
  DateTimeCalendarType: number; // Calendar type.
  DisplayFormat: number; // Date display format.
  FriendlyDisplayFormat: number; // Friendly date display format.
};

// for SP.FieldUser and SP.FieldLookup
export type FieldLookupProperty = {
  AllowMultipleValues: boolean; // Specifies if multiple values are allowed.
  isRelationship: boolean; // Specifies if the field is a relationship.
  LookupField: string; // Lookup field.
  LookupList: string; // Lookup list.
  LookupWebId: string; // Lookup web identifier.
  PrimaryFieldId: string; // Primary field identifier.
  RelationshipDeleteBehavior: number; // Relationship delete behavior.
};

export type FieldUserProperty = FieldLookupProperty & {
  AllowDisplay: boolean;
  Presence: boolean; // Specifies if presence is enabled.
  SelectionGroup: number; // Selection group.
  SelectionMode: number; // Selection mode.
};

// for SP.FieldMultiChoice, SP.FieldChoice, SP.FieldRatingScale
export type FieldMultiChoiceRatingProperty = {
  Choices: {
    __metadata: {
      type: string;
    };
    results: string[];
  };
  EditFormat: number;
  FillInChoice: boolean;
  Mappings: string;
};

export type FieldChoiceProperty = FieldMultiChoiceRatingProperty & {
  EditFormat: number;
};

export type FieldRatingScaleProperty = FieldMultiChoiceRatingProperty & {
  GridEndNumber: number;
  GridNAOptionText: string;
  GridStartNumber: number;
  GridTextRangeAverage: string;
  GridTextRangeHigh: string;
  GridTextRangeLow: string;
  RangeCount: number;
};

export type FieldMultiLineTextProperty = {
  AllowHyperlink: boolean;
  AppendOnly: boolean;
  NumberOfLines: number;
  RestrictedMode: boolean;
  RichText: boolean;
  WikiLinking: boolean;
};

// For SP.FieldNumber, SP.FieldCurrency
export type FieldNumberProperty = {
  MaximumValue: number;
  MinimumValue: number;
};

export type FieldCurrencyProperty = FieldNumberProperty & {
  CurrencyLocaleId: number;
};

export type FieldTextProperty = {
  MaxLength: number;
};

export type FieldUrlProperty = {
  DisplayFormat: number;
};
