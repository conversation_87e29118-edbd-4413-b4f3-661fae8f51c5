// GET ALL FormConfig
import { useAuthenticatedFirebase } from '~/server/utils/firebaseDatabase'

export default defineEventHandler(async (event) => {
  try {
    const { firebaseFetch } = useAuthenticatedFirebase(event)
    const formConfigs = await firebaseFetch('/formConfigs')
    return {
      ok: true,
      data: formConfigs,
      message: 'Form Configs fetched successfully',
    }
  } catch (error) {
    return {
      ok: false,
      errorMessage: `Database Error: ${error}`,
    }
  }
})
