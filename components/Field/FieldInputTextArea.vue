<script setup lang="ts">

defineProps({
  label: {
    type: String
  },
  modelValue: {
    type: String
  },
  rows: {
    type: Number
  },
  placeholder: {
    type: String
  }
})
const emit = defineEmits(['update:modelValue'])

function inputHandler(event: Event) {
  const target = event.target as HTMLTextAreaElement
  emit('update:modelValue', target.value)
}
</script>

<template>
  <FieldInput :label="label">
    <fluent-text-area :value="modelValue" @input="inputHandler" class="field" :placeholder="placeholder" />
  </FieldInput>

</template>

<style lang="scss" scoped>
.field {
  width: 100%;
}
</style>