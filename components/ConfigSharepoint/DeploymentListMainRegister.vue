<script setup lang="ts">
  import { isFieldTableInput } from '~/composables/useSharepointFieldUtils'
  import { MAINREGISTER_REQUIRED_FIELD } from '~/constants/default-values'
  import { FormFieldType } from '~/types/form'
  import type { SharepointList, SiteColumnField } from '~/types/sharepointConfig'

  const props = defineProps({
    list: {
      type: Object as PropType<SharepointList>,
      required: true,
    },
  })
  const sharepointAPIStore = useSharepointAPIStore()
  const sharepointConfigStore = useConfigSharepointStore()
  const { sharepointConfigFormFields } = storeToRefs(sharepointConfigStore)
  const { checkFieldCanBeAdded } = useSharepointFieldUtils()

  const siteColumns = ref<SiteColumnField[]>([])
  const formFields = ref<SharepointConfigField[]>([])
  const requiredFields = MAINREGISTER_REQUIRED_FIELD
  const isLoading = ref(false)
  type fieldCount = {
    stages: string[]
    count: number
  }
  const fieldNameCount = ref<Record<string, fieldCount>>({})

  const isFieldNameInList = (fieldName: string) => {
    return siteColumns.value.some((siteColumn) => siteColumn.InternalName === fieldName)
  }

  const canBeAdded = (fieldName: string, type: FormFieldType) => {
    if (!isFieldNameInList(fieldName)) {
      return checkFieldCanBeAdded(type) && !isFieldNameNotSpecified(fieldName)
    }
    return false
  }

  const isFieldNameNotSpecified = (fieldName: string) => {
    return fieldName.includes('FIELDNAME not found')
  }

  const isSiteColumnInitiated = computed(() => {
    return siteColumns.value.length > 0
  })

  async function fetchSiteColumns() {
    siteColumns.value = await sharepointAPIStore.fetchSiteColumnsInList(props.list.DisplayName)
  }

  async function initialiseDeploymentList() {
    await fetchSiteColumns()
    formFields.value = []
    fieldNameCount.value = {}
    sharepointConfigFormFields.value.forEach((field) => {
      // remove tableinput
      if (isFieldTableInput(field)) {
        return
      }
      let newField = useCloneDeep(field)
      if (!newField.fieldName) {
        newField.fieldName = `FIELDNAME not found, Label: ${field.label}`
      }

      if (!fieldNameCount.value[newField.fieldName]) {
        fieldNameCount.value[newField.fieldName] = {
          count: 1,
          stages: [newField.stageName!],
        }
      } else {
        fieldNameCount.value[newField.fieldName].count += 1
        fieldNameCount.value[newField.fieldName].stages.push(newField.stageName!)
        return
      }
      formFields.value.push(newField)
      sortSiteColumnsToAlphabeticalOrder()
      sortFormFieldsToAlphabeticalOrder()
    })
  }

  async function clickHandlerDeployField(field: SharepointConfigField) {
    isLoading.value = true
    const newField = useCloneDeep(field)
    newField.stageName = null as any
    if (sharepointAPIStore.isFieldCanBeAdded(newField)) {
      await sharepointAPIStore.addFieldToList(
        newField as AVAILABLEFIELDTYPES,
        props.list.DisplayName,
      )
      console.log(`Field ${field.fieldName} add to List: ${props.list.DisplayName}`)
    }

    await initialiseDeploymentList()
    isLoading.value = false
  }

  async function clickHandlerDeployRequiredField(fieldName: string) {
    isLoading.value = true
    await sharepointAPIStore.addSystemFieldToList(fieldName, props.list.DisplayName)

    await initialiseDeploymentList()
    isLoading.value = false
  }

  function sortSiteColumnsToAlphabeticalOrder() {
    siteColumns.value.sort((a, b) => a.InternalName.localeCompare(b.InternalName))
  }

  function sortFormFieldsToAlphabeticalOrder() {
    formFields.value.sort((a, b) => a.fieldName.localeCompare(b.fieldName))
  }

  async function clickHandlerDeployAllField() {
    isLoading.value = true

    const deployFields = [] as SharepointConfigField[]
    const deploySystemFields = [] as string[]
    requiredFields.forEach((field) => {
      if (!isFieldNameInList(field.fieldName)) {
        deploySystemFields.push(field.fieldName)
      }
    })
    formFields.value.forEach((field) => {
      if (canBeAdded(field.fieldName, field.type)) {
        if (!isFieldNameInList(field.fieldName)) {
          deployFields.push(field)
        }
      }
    })
    for (const field of deploySystemFields) {
      await sharepointAPIStore.addSystemFieldToList(field, props.list.DisplayName)
    }
    for (const field of deployFields) {
      if (sharepointAPIStore.isFieldCanBeAdded(field)) {
        if (canBeAdded(field.fieldName, field.type)) {
          console.log(`Adding Field: ${field.fieldName}`)
          await sharepointAPIStore.addFieldToList(
            field as AVAILABLEFIELDTYPES,
            props.list.DisplayName,
          )
        }
      }
    }
    console.log(deployFields, deploySystemFields)
    await initialiseDeploymentList()
    isLoading.value = false
  }

  onMounted(async () => {
    isLoading.value = true
    await initialiseDeploymentList()
    isLoading.value = false
  })

  function getTooltipText(stageNames: string[]) {
    return `${stageNames.join(', ')}`
  }
</script>

<template>
  <BaseSiteColumnCard>
    <template #header>
      <h6>{{ props.list.DisplayName }}</h6>
      <Button @click="clickHandlerDeployAllField" :disabled="!isSiteColumnInitiated">Deploy</Button>
    </template>
    <div v-if="isLoading">Loading...</div>
    <div v-else-if="!isSiteColumnInitiated">Site columns not found</div>
    <div v-else class="siteColumns">
      <!-- required fields -->
      <div v-for="field in requiredFields" class="field__deployment" :key="field.fieldName">
        <span><span class="required__indicator">*</span>{{ field.fieldName }}</span>
        <div class="icon--check" v-if="isFieldNameInList(field.fieldName)">
          <BaseIconCheck />
        </div>
        <div class="icon--add" v-if="!isFieldNameInList(field.fieldName)">
          <BaseIconAdd @click="clickHandlerDeployRequiredField(field.fieldName)" />
        </div>
      </div>
      <!-- form fields -->
      <div v-for="field in formFields" class="field__deployment">
        <span>
          {{ field.fieldName }}
          <Tooltip :content="getTooltipText(fieldNameCount[field.fieldName].stages)">
            <span v-if="fieldNameCount[field.fieldName].count > 1" class="count">{{
              fieldNameCount[field.fieldName].count
            }}</span>
          </Tooltip>
        </span>
        <div class="icon--check" v-if="isFieldNameInList(field.fieldName)">
          <BaseIconCheck />
        </div>
        <div class="icon--add" v-if="canBeAdded(field.fieldName, field.type)">
          <BaseIconAdd @click="clickHandlerDeployField(field)" />
        </div>
      </div>
    </div>
  </BaseSiteColumnCard>
</template>

<style lang="scss" scoped>
  .siteColumns {
    display: flex;
    flex-direction: column;
    gap: $unit;

    .field__deployment {
      max-lines: 1;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .count {
        background-color: $primary-gray;
        padding: 0 4px;
      }

      .required__indicator {
        margin-right: calc($unit/2);
        color: red;
      }
    }
    .icon--check {
      width: 1rem;
      color: green;
    }
    .icon--add {
      cursor: pointer;
      width: 1rem;
      color: red;
    }
  }
</style>
