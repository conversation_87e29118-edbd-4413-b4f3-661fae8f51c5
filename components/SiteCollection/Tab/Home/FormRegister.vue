<script lang="ts" setup>
  const siteCollectionStore = useSiteCollectionsStore()
  const { activeSiteCollection, tenant } = storeToRefs(siteCollectionStore)
  const formStore = useFormStore()
  const siteForms = computed(() => {
    return formStore.getFormFromKeyArray(activeSiteCollection.value?.forms || [])
  })
  const showAddFormDrawer = ref(false)

  const columns = [
    { title: 'Name', key: 'name' },
    { title: 'Action', slot: 'action', width: 150, align: 'center' },
  ]

  function clickHandlerSetting(formKey: string) {
    navigateTo(`/tenants/${tenant.value}/${activeSiteCollection.value.key}/form/${formKey}`)
  }
</script>

<template>
  <SiteCollectionDrawerAddForm @close="showAddFormDrawer = false" v-if="showAddFormDrawer" />
  <div>
    <div class="form-header">
      <span>Forms</span>
      <BaseButtonAdd text="Form" appearance="accent" @click="showAddFormDrawer = true" />
    </div>

    <BaseRegisterList :list="siteForms" :columns="columns">
      <template #action="{ row }">
        <BaseButton @click="clickHandlerSetting(row.key)">
          <Icon type="md-briefcase" />
        </BaseButton>
      </template>
    </BaseRegisterList>
  </div>
</template>

<style lang="scss" scoped>
  .form-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: $padding;
  }
</style>
