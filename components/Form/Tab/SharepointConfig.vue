<script setup lang="ts">
import { FormTabNames } from '~/constants/tabs'

const sharepointConfigStore = useConfigSharepointStore()
const formConfigStore = useFormConfigStore()
const { activeFormConfig } = storeToRefs(formConfigStore)
const router = useRouter()
const route = useRoute()

onMounted(() =>
{
    router.replace({
        query: {
            ...route.query,
            tab: FormTabNames.SHAREPOINT_CONFIG
        }
    })
    sharepointConfigStore.initialiseSharepointConfigForForm()
})

watch(() => activeFormConfig.value, () =>
{
    sharepointConfigStore.initialiseSharepointConfigForForm()
})
</script>

<template>
    <ConfigSharepointPage />
</template>