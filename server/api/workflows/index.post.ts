// CREATE Workflow
// body: { workflowData: Workflow }

import { useAuthenticatedFirebase } from '~/server/utils/firebaseDatabase'

export default defineEventHandler(async (event) => {
  const { workflowData } = await readBody(event)
  if (!workflowData) {
    return {
      ok: false,
      errorMessage: `workflowData is required`,
    }
  }

  try {
    const { firebasePush } = useAuthenticatedFirebase(event)
    const newWorkflowKey = await firebasePush(`/workflows`, workflowData)
    return {
      ok: true,
      data: newWorkflowKey,
      message: `Sucessfully add new workflow`,
    }
  } catch (error) {
    return {
      ok: false,
      errorMessage: error,
    }
  }
})
