<script setup lang="ts">
  import type { ModuleDrawerWorkflowVersionField } from '#build/components'
  import type { UpVersionSetting } from '~/types/workflow'

  const uiStore = useUiStore()
  const emit = defineEmits(['close-drawer'])

  const upVersionStore = useUpVersionStore()
  const { activeUpVersionSetting } = storeToRefs(upVersionStore)

  const workflowVersionField = ref<InstanceType<typeof ModuleDrawerWorkflowVersionField> | null>(
    null,
  )
  const upVersionSetting = ref({} as UpVersionSetting)
  const isTypeSelected = computed(() => workflowVersionField.value?.isTypeSelected())

  watch(
    activeUpVersionSetting,
    () => {
      upVersionSetting.value = useClone(activeUpVersionSetting.value)
    },
    { immediate: true },
  )

  async function clickHandlerUpVersion() {
    uiStore.setLoading(true, 'Duplicating and updating workflow version...')
    emit('close-drawer')
    updateVersion()
    await upVersionStore.upVersionHandler(upVersionSetting.value)
    uiStore.setLoading(false)
  }

  function updateVersion() {
    const version = workflowVersionField.value?.getNewVersion()
    if (!version) {
      throw new Error('Component Error, please refresh page')
    }
    upVersionSetting.value.version = version
  }
</script>

<template>
  <div class="upVersion">
    <span class="header">UP Version</span>
    <ModuleDrawerWorkflowVersionField
      v-model="upVersionSetting.version"
      ref="workflowVersionField" />
    <Form label-position="left">
      <FieldInputTextArea v-model="upVersionSetting.changeLog" label="Change Log" />
    </Form>
    <BaseButton @click="clickHandlerUpVersion" appearance="accent" :disabled="!isTypeSelected">
      Duplicate and update workflow version
    </BaseButton>
  </div>
</template>

<style lang="scss" scoped>
  .upVersion {
    padding: $padding $padding;
    user-select: none;
    width: 100%;
    border-bottom: 1px solid $primary-gray;
  }

  .header {
    font-weight: 500;
    margin-bottom: $margin;
  }
</style>
