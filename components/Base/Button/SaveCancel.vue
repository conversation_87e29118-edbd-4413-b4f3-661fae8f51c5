<script setup lang="ts">
const props = defineProps({
    disableSubmit: {
        type: Boolean,
        default: false
    }
})
const emit = defineEmits(['cancel', 'save'])

function clickHandlerCancel() {
    emit('cancel')
}
function clickHandlerSave() {
    emit('save')
}
</script>

<template>
    <div class="buttonWrapper">
        <BaseButton @click="clickHandlerCancel">Cancel</BaseButton>
        <BaseButton appearance="accent" :disabled="disableSubmit" @click="clickHandlerSave">Submit</BaseButton>
    </div>
</template>

<style lang="scss" scoped>
.buttonWrapper {
    display: flex;
    gap: $padding;
    margin-top: $margin;
    margin-bottom: $margin;
}
</style>