<script setup lang="ts">
  const route = useRoute()

  const tenant = route.params.tenant as string
  const siteCollectionKey = route.params.siteCollectionKey as string
  const formKey = route.params.key as string

  const uiStore = useUiStore()
  const tenantStore = useTenantsStore()
  const siteCollectionsStore = useSiteCollectionsStore()
  const formStore = useFormStore()
  const { activeForm } = storeToRefs(formStore)
  const formConfigStore = useFormConfigStore()
  const { activeFormConfigKey } = storeToRefs(formConfigStore)
  const sharepointAPIStore = useSharepointAPIStore()
  const upVersionStore = useUpVersionStore()

  const isFormConfigFound = computed(() => {
    return activeFormConfigKey.value !== ''
  })
  const showVersionDrawer = ref(false)

  const activeTenant = computed(() => {
    return tenantStore.getTenantFromName(tenant)
  })
  const activeSite = computed(() => {
    return siteCollectionsStore.getSiteByKey(siteCollectionKey)
  })

  useRealtimeData('/formConfigs', formConfigStore.formConfigsCallback)

  onMounted(() => {
    formStore.setActiveFormKey(formKey)
    siteCollectionsStore.setTenant(tenant)
    siteCollectionsStore.setActiveSite(siteCollectionKey)
  })

  watch([activeTenant, activeSite], async ([activeTenant, activeSite]) => {
    if (activeTenant && activeSite) {
      sharepointAPIStore.resetSharepointAPIState()
      sharepointAPIStore.setSharepointAPIState(
        activeTenant.tenant,
        activeTenant.tenantId,
        activeSite.url,
      )
      uiStore.setLoading(true, `Fetching Sharepoint API...`)
      await Promise.all([
        sharepointAPIStore.fetchLists(),
        sharepointAPIStore.fetchSiteGroups(),
        sharepointAPIStore.fetchUsers(),
      ])
      uiStore.setLoading(false)
    }
  })

  async function clickHandlerInitFormConfig() {
    uiStore.setLoading(true, `Creating new formConfig for form...`)
    await formConfigStore.createNewFormConfig(formKey, activeForm.value.name)
    uiStore.setLoading(false)
  }

  async function clickHandlerDeleteForm() {
    uiStore.setLoading(true, `Deleting form...`)
    await formStore.deleteForm(formKey)
    await siteCollectionsStore.removeFormFromSiteCollection(siteCollectionKey, formKey)
    navigateTo(`/tenants/${tenant}/${siteCollectionKey}`)
    uiStore.setLoading(false)
  }

  function clickHandlerVersion() {
    upVersionStore.initialiseUpVersionForForm()
    showVersionDrawer.value = true
  }

  function closeHandler() {
    showVersionDrawer.value = false
  }
</script>

<template>
  <template v-if="isFormConfigFound">
    <FormDrawerFormConfigVersion v-if="showVersionDrawer" @close="closeHandler" />
    <BaseLayoutPage>
      <template #header>
        <FormLayoutHeader @click="clickHandlerVersion" />
      </template>
      <template #content>
        <FormTabs />
      </template>
    </BaseLayoutPage>
  </template>
  <template v-else>
    <AppBreadCrumbs />
    <BaseNotFoundPage text="Form Config not found">
      <BaseButtonAdd @click="clickHandlerInitFormConfig" text="Form" />
      <BaseButtonDeleteText text="Delete Form" @click="clickHandlerDeleteForm" />
    </BaseNotFoundPage>
  </template>
</template>
