<script setup lang="ts">
import { SiteCollectionTabNames } from '~/constants/tabs';


const siteCollectionsStore = useSiteCollectionsStore();
const { activeSiteCollection } = storeToRefs(siteCollectionsStore);
const coresStore = useCoresStore();
const { cores } = storeToRefs(coresStore);

const activeCores = computed(() =>
{
  const siteCores = activeSiteCollection.value.cores
  if (!siteCores) return []
  return cores.value.filter(core => siteCores.includes(core.key))
})

const router = useRouter()
const route = useRoute()

onMounted(() =>
{
  router.replace({
    query: {
      ...route.query,
      tab: SiteCollectionTabNames.DEPLOYMENTS
    }
  })

})
</script>

<template>
  <div class="siteCollectionTabDeployment">
    <template v-for="core in activeCores">
      <SiteCollectionTabDeploymentCore :core="core" />
    </template>
  </div>
</template>

<style lang="scss" scoped>
.siteCollectionTabDeployment {
  display: flex;
  flex-direction: column;
  gap: $margin;
}
</style>