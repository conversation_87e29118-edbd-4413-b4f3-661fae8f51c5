<template>
  <div class="fieldCard">
    <div class="header">
      <slot name="icon" />
      <h3>
        <slot name="title" />
      </h3>
    </div>
    <div class="container">
      <slot name="content" />
    </div>
  </div>
</template>

<style lang="scss" scoped>
$box-shadow: rgba(0, 0, 0, 0.15) 0px 2px 7px;
$primary-charcoal-10: #f7f7f7;

.fieldCard {
  box-shadow: $box-shadow;

  h3 {
    font-size: 0.9rem;
    font-weight: 300;
  }

  .header {
    display: inline-flex;
    gap: $margin;
    align-items: center;
    padding: $unit;
    background: $primary-charcoal-10;
    border-radius: 0 50px 50px 0;
  }

  .container {
    padding: $margin $margin * 2;
  }
}
</style>
