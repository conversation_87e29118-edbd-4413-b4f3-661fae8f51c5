<script setup lang="ts">
import { FormTabNames } from '~/constants/tabs'

const permissionStore = useConfigPermissionStore()
const formConfigStore = useFormConfigStore()
const { activeFormConfig } = storeToRefs(formConfigStore)
const router = useRouter()
const route = useRoute()
onMounted(() =>
{
    router.replace({
        query: {
            ...route.query,
            tab: FormTabNames.PERMISSIONS
        }
    })
    permissionStore.initialisePermissionStoreForForm()
})

watch(activeFormConfig, () =>
{
    permissionStore.initialisePermissionStoreForForm()
})
</script>

<template>
    <ConfigPermissionPage />
</template>
