<script setup lang="ts">
  import type { Column } from '@/types/data'

  const props = defineProps({
    list: {
      type: Array<Object>,
    },
    columns: {
      type: Array<Column>,
    },
  })

  const emit = defineEmits(['click'])

  function rowClassName() {
    return 'fluent-ui-row'
  }
</script>

<template>
  <div class="registerList">
    <Table
      size="small"
      row-key="key"
      :columns="props.columns"
      :data="props.list"
      :row-class-name="rowClassName">
      <slot />
      <template #action="{ row, index }">
        <slot name="action" :row="row" :index="index" />
      </template>
      <template #environment="{ row }">
        <slot name="environment" :row="row" />
      </template>
    </Table>
  </div>
</template>

<style lang="scss" scoped>
  .registerList {
    padding: $padding;
  }
</style>

<style>
  .ivu-table-header thead tr th div span {
    font-weight: bold;
  }
</style>
