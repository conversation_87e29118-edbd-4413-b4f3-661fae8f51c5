<script setup lang="ts">
  enum ModuleTabNames {
    LISTS = 'module-lists',
    CUSTOMLISTS = 'module-custom-lists',
    SITECOLUMNSDEPLOYMENT = 'module-site-columns-deployment',
  }
</script>

<template>
  <BaseTabs>
    <BaseTabPane label="Lists" :name="ModuleTabNames.LISTS">
      <ConfigSharepointPageLists />
    </BaseTabPane>
    <BaseTabPane label="Site Columns Deployment" :name="ModuleTabNames.SITECOLUMNSDEPLOYMENT">
      <ConfigSharepointPageSiteColumnsDeployment />
    </BaseTabPane>
    <BaseTabPane label="Custom Lists" :name="ModuleTabNames.CUSTOMLISTS">
      <ConfigSharepointPageCustomLists />
    </BaseTabPane>
  </BaseTabs>
</template>
