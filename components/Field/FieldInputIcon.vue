<script setup lang="ts">
  import availableIcons from '../../content/ionicon.json'
  defineProps({
    label: {
      type: String,
    },
    modelValue: {
      type: String,
    },
  })

  const emit = defineEmits(['update:modelValue'])

  const handleChange = (value: string) => {
    emit('update:modelValue', value)
  }

  const AVAILABLE_ICONS = availableIcons.AVAILABLE_ICONS
</script>

<template>
  <FieldInput :label="label">
    <div class="select-container">
      <Select :modelValue="modelValue" @update:modelValue="handleChange" filterable transfer>
        <Option :value="icon" :label="icon" v-for="icon in AVAILABLE_ICONS" :key="icon">
          <span>{{ icon }}</span>
          <span style="float: right; color: gray">
            <Icon :type="icon" />
          </span>
        </Option>
      </Select>
      <Icon :type="modelValue" />
    </div>
  </FieldInput>
</template>

<style lang="scss" scoped>
  .select-container {
    display: grid;
    grid-template-columns: 1fr 50px;
    column-gap: $margin;
    align-items: center;
  }
</style>
