// composables/useFetchStoreData.ts
import { useUiStore } from '@/stores/ui'

export function useFetchStoreData<T>(
  fetchMethod: () => Promise<T>,
  key: string,
  watchTarget?: Ref<any>,
  loadingMessage = 'Loading...',
) {
  const uiStore = useUiStore()
  uiStore.setLoading(true, loadingMessage)

  const { data, status, refresh } = useAsyncData(
    key,
    async () => {
      const result = await fetchMethod()
      uiStore.setLoading(false)
      return result
    },
    {
      default: () => [],
      watch: watchTarget ? [watchTarget] : undefined,
      server: false,
    },
  )

  const isLoading = computed(() => status.value === 'pending')

  return { data, status, isLoading, refresh }
}
