// UPDATE Module
// body { moduleKey: string, moduleData: Module }
import { useAuthenticatedFirebase } from '~/server/utils/firebaseDatabase'

export default defineEventHandler(async (event) => {
  const { moduleKey, moduleData } = await readBody(event)

  if (!moduleKey || !moduleData) {
    return {
      ok: false,
      errorMessage: 'moduleKey and moduleData are required',
    }
  }

  try {
    const { firebaseUpdate } = useAuthenticatedFirebase(event)
    await firebaseUpdate(`/modules/${moduleKey}`, moduleData)
    return {
      ok: true,
      message: `Module with key: ${moduleKey} updated successfully`,
    }
  } catch (error) {
    return {
      ok: false,
      errorMessage: error,
    }
  }
})
