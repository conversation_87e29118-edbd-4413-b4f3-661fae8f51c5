import { getAccessToken } from '../../../utils/accessToken'

export default defineEventHandler(async (event) => {
  const query = getQuery(event)
  const { group } = await readBody(event)

  const { tenantUrl, tenantId, siteUrl } = query

  const accessToken = await getAccessToken(tenantUrl as string, tenantId as string)
  const headers = {
    Accept: 'application/json;odata=verbose',
    'Content-Type': 'application/json;odata=verbose',
    Authorization: `Bearer ${accessToken}`, // Add this if using OAuth
  }

  const url = `${siteUrl}/_api/web/sitegroups`

  try {
    const response = await fetch(url, {
      method: 'POST',
      headers,
      body: JSON.stringify(group),
    })
    if (!response.ok) {
      const error = await response.json()
      return {
        ok: false,
        errorMessage: `Failed to add group to ${siteUrl}: ${error}`,
      }
    }
    const data = await response.json()
    console.log('Group added successfully:', data)
    await new Promise((resolve) => setTimeout(resolve, 500))
    return {
      ok: true,
      data: data,
      message: `Group added to ${siteUrl}.`,
    }
  } catch (error) {
    return {
      ok: false,
      errorMessage: `Failed to add group to ${siteUrl}: ${error}`,
    }
  }
})
