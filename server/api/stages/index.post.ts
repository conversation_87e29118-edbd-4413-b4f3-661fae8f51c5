// CREATE Stage
// body { stageData: Stage }
import { useAuthenticatedFirebase } from '~/server/utils/firebaseDatabase'

export default defineEventHandler(async (event) => {
  const { stageData } = await readBody(event)

  if (!stageData) {
    return {
      ok: false,
      errorMessage: `stageData is required`,
    }
  }

  try {
    const { firebasePush } = useAuthenticatedFirebase(event)
    const stageKey = await firebasePush(`/stages`, stageData)
    return {
      ok: true,
      data: stageKey,
      message: `Successfully added stage`,
    }
  } catch (error) {
    return {
      ok: false,
      errorMessage: error,
    }
  }
  // - ref.push(stageData)
  // - Call firebase for new site collection on ref siteCollections/[newKey]
})
