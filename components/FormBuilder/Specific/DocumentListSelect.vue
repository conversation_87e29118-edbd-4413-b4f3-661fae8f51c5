<script setup lang="ts">
const { documentListSelectField } = useFormBuilderFieldSetting()
</script>

<template>
    <FormBuilderFieldSettingLabel>Document List Select Setting</FormBuilderFieldSettingLabel>
    <template v-if="documentListSelectField">
        <Form label-position="top">
            <FieldInputText v-model="documentListSelectField.folders" label="Folders (comma seperated)" />
            <FormBuilderSpecificOptionSettings v-model="documentListSelectField.options"
                header-label="Document List Names" />
        </Form>
    </template>
</template>