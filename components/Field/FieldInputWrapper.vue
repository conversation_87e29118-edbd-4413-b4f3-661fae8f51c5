<script setup lang="ts">
defineProps({
  noMargin: {
    type: Boolean,
    default: false
  }
})
</script>

<template>
  <div class="fieldInputWrapper" :class="{ 'no-margin': noMargin }">
    <slot name="label" />
    <slot />
  </div>
</template>

<style lang="scss" scoped>
.fieldInputWrapper {
  display: grid;
  justify-items: start;
  align-items: center;
  gap: $unit;
  width: 100%;
  box-sizing: border-box;
  margin-bottom: 24px;
}

.no-margin {
  margin-bottom: 0;
}
</style>
