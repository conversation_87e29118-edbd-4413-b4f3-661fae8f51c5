<script setup lang="ts">
  const uiStore = useUiStore()
  const { isLoading } = storeToRefs(uiStore)

  useHead({
    title: `Ibiqs Workbench`,
  })

  const modulesStore = useModulesStore()
  const formStore = useFormStore()
  const siteCollectionsStore = useSiteCollectionsStore()
  const tenantStore = useTenantsStore()
  const coresStore = useCoresStore()
  const modulesData = useRealtimeData('/modules', modulesStore.modulesCallback)
  const formData = useRealtimeData('/forms', formStore.formsCallback)
  const siteCollectionsData = useRealtimeData(
    '/siteCollections',
    siteCollectionsStore.siteCollectionsCallback,
  )
  const tenantData = useRealtimeData('/tenants', tenantStore.tenantsPageCallback)
  const coreData = useRealtimeData('/cores', coresStore.coresCallback)

  const dataIsLoading = computed(() => {
    return (
      modulesData.loading.value ||
      formData.loading.value ||
      siteCollectionsData.loading.value ||
      tenantData.loading.value ||
      coreData.loading.value ||
      isLoading.value
    )
  })
</script>

<template>
  <Layout class="main">
    <AppLayoutHeader />
    <AppLoading v-if="dataIsLoading" />
    <Layout class="ivu-layout-has-sider">
      <AppLayoutSider />
      <Content class="content">
        <slot />
      </Content>
    </Layout>
  </Layout>
</template>

<style lang="scss" scoped>
  .main {
    height: 100vh;
    overflow: hidden;
  }

  .content {
    height: calc(100vh - 50px);
    overflow: auto;
    background-color: white;
  }

  .menu__icon {
    transition: all 0.3s;
    cursor: pointer;
  }

  .rotate__icon {
    transform: rotate(-180deg);
  }
</style>
