<script setup lang="ts">
const { getCrumbs } = useUiUtils()
const breadcrumbs = computed(() => getCrumbs())
</script>

<template>
    <template v-for="(crumb, index) in breadcrumbs">
        <span class="crumb" v-if="crumb" @click="$router.push(crumb.to)">
            {{ crumb.name }}
        </span>
        <span class="separator" v-if="index !== breadcrumbs.length - 1">/</span>
    </template>
    <slot />
</template>

<style lang="scss" scoped>
.crumb {
    cursor: pointer;
    padding-right: $unit;
    padding-left: $unit;
    text-transform: uppercase;


    &:hover {
        text-decoration: underline;
    }
}

.separator {
    padding-right: calc($unit/2);
}
</style>