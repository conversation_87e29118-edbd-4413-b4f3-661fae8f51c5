<script setup lang="ts">
  import type { TableColumn } from '~/types/ui'

  const props = defineProps({
    columns: {
      type: Array<TableColumn>,
      required: true,
    },
    data: {
      type: Array<any>,
      required: true,
    },
    readonly: {
      type: Boolean,
      default: false,
    },
  })

  function getTabIndex(columnIndex: number, siteColumnIndex: number) {
    return columnIndex + 1 + siteColumnIndex * (props.columns.length - 1)
  }
</script>

<template>
  <div class="tableComponent">
    <template v-for="(column, columnIndex) in columns">
      <div class="table__column">
        <div class="table__column__header">
          <p>{{ column.title }}</p>
        </div>
        <div class="table__column__content">
          <template v-if="column.key !== 'actions'">
            <template v-if="$slots[column.key]">
              <div class="table__column__row" v-for="(siteColumn, siteColumnIndex) in data">
                <slot
                  :name="column.key"
                  :item="siteColumn"
                  :tabIndex="getTabIndex(columnIndex, siteColumnIndex)" />
              </div>
            </template>
            <template v-else>
              <div class="table__column__row" v-for="(siteColumn, siteColumnIndex) in data">
                <input
                  class="ivu-input"
                  type="text"
                  v-model="siteColumn[column.key]"
                  :tabindex="getTabIndex(columnIndex, siteColumnIndex)"
                  :readonly="readonly" />
              </div>
            </template>
          </template>
          <template v-if="column.key === 'actions'">
            <div class="table__column__row" v-for="(_, index) in data">
              <slot name="actions" :index="index" />
            </div>
          </template>
        </div>
      </div>
    </template>
  </div>
</template>

<style lang="scss" scoped>
  .tableComponent {
    display: flex;
    gap: $unit;

    .table__column {
      flex-grow: 1;

      &__header {
        font-weight: 600;
      }

      &__content {
        display: flex;
        flex-direction: column;
        gap: $unit;
      }
    }
  }
</style>
