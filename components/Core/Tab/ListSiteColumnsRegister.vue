<script setup lang="ts">
  import type { CoreList, CoreSiteColumn } from '~/types/core'

  const props = defineProps({
    list: {
      type: Object as PropType<CoreList>,
      required: true,
    },
  })

  onMounted(() => {
    initialiseListSiteColumns()
  })

  watch(
    () => props.list,
    (newValue) => {
      initialiseListSiteColumns()
    },
  )

  function initialiseListSiteColumns() {
    if (props.list.SiteColumns)
      siteColumnIds.value = props.list.SiteColumns.map((siteColumn: CoreSiteColumn) => {
        return siteColumn.id
      })
    else siteColumnIds.value = []
  }

  const uiStore = useUiStore()
  const coresStore = useCoresStore()
  const { activeCore, activeCoreSiteColumns } = storeToRefs(coresStore)
  const editList = ref(false)
  const siteColumnIds = ref<number[]>([])

  const siteColumns = computed(() => {
    return activeCoreSiteColumns.value.filter((siteColumn: CoreSiteColumn) => {
      return siteColumnIds.value.includes(siteColumn.id)
    })
  })

  function clickHandlerToggle() {
    editList.value = !editList.value
  }

  async function clickHandlerSubmit() {
    uiStore.setLoading(true, 'Updating core list site columns...')
    if (activeCore.value)
      await coresStore.updateCoreListSiteColumns(
        activeCore.value.key,
        props.list.id,
        siteColumns.value,
      )
    editList.value = false
    uiStore.setLoading(false)
  }
</script>

<template>
  <BaseSiteColumnCard>
    <template #header>
      <h6>{{ list.DisplayName }}</h6>
      <Button shape="circle" size="small" icon="ios-create" @click="clickHandlerToggle" />
    </template>
    <!-- <div class="card__content"> -->
    <template v-if="editList">
      <Select v-model="siteColumnIds" multiple filterable transfer>
        <Option v-for="item in activeCoreSiteColumns" :value="item.id" :key="item.DisplayName"
          >{{ item.DisplayName }}
        </Option>
      </Select>
      <div class="buttons">
        <Button @click="clickHandlerToggle">Cancel</Button>
        <Button type="primary" @click="clickHandlerSubmit">Submit</Button>
      </div>
    </template>
    <template v-else>
      <Select v-model="siteColumnIds" multiple filterable transfer>
        <Option v-for="item in activeCoreSiteColumns" :value="item.id" :key="item.DisplayName"
          >{{ item.DisplayName }}
        </Option>
      </Select>
      <div class="buttons">
        <Button @click="clickHandlerToggle">Cancel</Button>
        <Button type="primary" @click="clickHandlerSubmit">Submit</Button>
      </div>
    </template>
    <template v-else>
      <template v-if="!list.SiteColumns">
        <p>No site columns</p>
      </template>
      <div class="siteColumns">
        <template v-for="siteColumn in list.SiteColumns">
          <div>{{ siteColumn.DisplayName }}</div>
        </template>
      </div>
    </template>
    <!-- </div> -->
  </BaseSiteColumnCard>
</template>

<style lang="scss" scoped>
  .buttons {
    display: flex;
    justify-content: space-between;
  }

  .siteColumns {
    display: flex;
    flex-direction: column;
    gap: $unit;
  }
</style>
