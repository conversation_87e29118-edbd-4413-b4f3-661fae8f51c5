// GET ALL Tenants
import { useFirebaseDatabase } from '~/server/utils/firebaseDatabase'

export default defineEventHandler(async (event) => {
  const token = getCookie(event, 'session')
  if (!token) {
    return {
      ok: false,
      errorMessage: 'Unauthorized: No session token found',
    }
  }

  try {
    const tenants = await fetchTenants(token)
    return {
      ok: true,
      message: 'Tenants fetched successfully',
      data: tenants,
    }
  } catch (error) {
    return {
      ok: false,
      errorMessage: error,
    }
  }
})

async function fetchTenants(token: string) {
  const { firebaseFetch } = useFirebaseDatabase(token)
  return await firebaseFetch('/tenants')
}
