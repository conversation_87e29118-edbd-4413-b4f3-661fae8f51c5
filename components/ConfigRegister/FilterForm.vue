<script setup lang="ts">
import { CONFIG_REGISTER_NEW_FILTER } from '~/constants/default-values';
import type { FieldOption } from '~/types/form';
import { RegisterFilterOperator, RegisterFilterType, type RegisterFilter } from '~/types/register';

const props = defineProps({
    modelValue: {
        type: Object as PropType<RegisterFilter[]>,
        default: () => []
    }
})

const registerStore = useConfigRegisterStore()
const emit = defineEmits(['update:modelValue'])

const valueOptions = ref<Array<FieldOption[] | undefined>>([])
const manualInputArray = ref<boolean[]>([])

defineExpose({
    resetValueOptions
})

watch(() => props.modelValue, () =>
{
    initialiseFilterForm()
})

onMounted(() =>
{
    initialiseFilterForm()
})

function initialiseFilterForm()
{
    manualInputArray.value = Array(props.modelValue.length).fill(false)
}

const operatorOption = computed(() =>
{
    return registerStore.getRegisterFilterOperatorOptions()
})

const columnTypeOptions = computed(() =>
{
    return registerStore.getRegisterFilterTypeOptions()
})

function resetValueOptions()
{
    valueOptions.value = Array(props.modelValue.length).fill(undefined)
}

function clickHandlerAdd()
{
    const newModelValue = useCloneDeep(props.modelValue)
    newModelValue.push(CONFIG_REGISTER_NEW_FILTER)
    emit('update:modelValue', newModelValue)
}

function clickHandlerDelete(index: number)
{
    const newModelValue = useCloneDeep(props.modelValue)
    newModelValue.splice(index, 1)
    emit('update:modelValue', newModelValue)
}

function updateHandlerType(type: RegisterFilterType, index: number)
{
    const newModelValue = useCloneDeep(props.modelValue)
    newModelValue[index].type = type
    emit('update:modelValue', newModelValue)
}

function updateHandlerOperator(operator: RegisterFilterOperator, index: number)
{
    const newModelValue = useCloneDeep(props.modelValue)
    newModelValue[index].operator = operator
    emit('update:modelValue', newModelValue)
}

</script>

<template>
    <FormBuilderSpecificOptionHeader label="Filters" @add="clickHandlerAdd" />
    <BaseLayoutRowFormHeader grid-layout="75px 1fr 75px 1fr 50px">
        <span>Type</span>
        <span>Field Name</span>
        <span>Operator</span>
        <span>Filter Value</span>
        <span>Actions</span>
    </BaseLayoutRowFormHeader>
    <Form>
        <BaseLayoutRowForm grid-layout="75px 1fr 75px 1fr 50px">
            <template v-for="filter, index in modelValue">
                <FieldInput class="field">
                    <Select :model-value="modelValue[index].type" @update:model-value="updateHandlerType($event, index)"
                        clearable transfer class="select">
                        <Option v-for="option in columnTypeOptions" :value="option.value" :key="option.value"
                            :label="option.label">
                            <span>
                                {{ option.label }}</span>
                        </Option>
                    </Select>
                </FieldInput>
                <FieldInputText v-model="modelValue[index].fieldName" />
                <FieldInput class="field">
                    <Select :model-value="modelValue[index].operator"
                        @update:model-value="updateHandlerOperator($event, index)" clearable transfer class="select">
                        <Option v-for="option in operatorOption" :value="option.value" :key="option.value"
                            :label="option.label">
                            <span>
                                {{ option.label }}</span>
                        </Option>
                    </Select>
                </FieldInput>
                <FieldInputText v-model="modelValue[index].value" />
                <BaseButtonDelete @click="clickHandlerDelete(index)" class="formButton" />
            </template>
        </BaseLayoutRowForm>
    </Form>
</template>

<style lang="scss" scoped>
.field {
    width: 100%;
    max-width: 100%;


    .select {
        overflow: auto;
        text-overflow: ellipsis;
    }
}

.option {
    display: flex;
    justify-content: space-between;

    &__description {
        color: $primary-gray;
    }
}

.formButton {
    margin-bottom: 24px;
}
</style>