<script setup lang="ts">
type Type = 'select' | 'drag'

defineProps({
    label: {
        type: String
    },
    modelValue: {
        type: String
    },
    type: {
        type: String as PropType<Type>,
        default: 'select'
    },
    action: {
        type: String,
        default: 'http://localhost:3000/upload'
    }
})
const emit = defineEmits(['update:modelValue'])


</script>

<template>
    <div class="fieldInputFileUpload">
        <h4>{{ label }}</h4>
        <Upload id="fileUpload" multiple type="drag" :show-upload-list="false"
            action="//jsonplaceholder.typicode.com/posts/">
            <div class="content">
                <Icon type="ios-cloud-upload" size="52" class="upload-icon"></Icon>
                <p>Click or drag files here to upload</p>
            </div>
        </Upload>
    </div>
</template>

<style lang="scss" scoped>
.content {
    padding: $p-margin;
}
</style>