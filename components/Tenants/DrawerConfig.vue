<script setup lang="ts">
  import type { Tenant } from '@/types/data'

  const emit = defineEmits(['close'])
  const props = defineProps({
    tenant: {
      type: Object as PropType<Tenant>,
      required: true,
    },
  })

  const tenantsStore = useTenantsStore()
  const isLoading = ref(false)
  onMounted(() => {
    if (props.tenant) data.value = props.tenant
  })

  watch(
    () => props.tenant,
    (newValue) => {
      if (!newValue) return
      data.value = {
        name: newValue.name,
        tenant: newValue.tenant,
        tenantId: newValue.tenantId,
        clientId: newValue.clientId,
        clientSecret: newValue.clientSecret,
        adminUsername: newValue.adminUsername,
        adminPassword: newValue.adminPassword,
        dateAdded: newValue.dateAdded,
        key: newValue.key,
      }
    },
  )
  const data = ref({
    name: '',
    tenant: '',
    tenantId: '',
    clientId: '',
    clientSecret: '',
    adminUsername: '',
    adminPassword: '',
    dateAdded: '',
  } as Tenant)

  function closeHandler() {
    emit('close')
  }

  async function clickHandler() {
    isLoading.value = true
    await tenantsStore.updateTenant({ tenantKey: props.tenant.key, tenantData: data.value })
    isLoading.value = false
  }
</script>

<template>
  <Drawer
    title="Tenant Config"
    :model-value="true"
    width="600"
    :mask-closable="false"
    @on-close="closeHandler">
    <BaseLoadingOverlay v-if="isLoading" />
    <Form ref="tenantConfig" :model="data">
      <FieldInputText
        v-model="data.name"
        label="Tenant Name"
        required
        prop="name"
        :rules="{ required: true, message: 'Please enter customer name', trigger: 'blur' }" />
      <FieldInputText
        v-model="data.tenant"
        label="Tenant URL"
        required
        prop="tenant"
        :rules="{ required: true, message: 'Please enter tenant name', trigger: 'blur' }" />
      <FieldInputText
        v-model="data.tenantId"
        label="Tenant ID"
        required
        prop="tenantId"
        :rules="{ required: true, message: 'Please enter tenant id', trigger: 'blur' }" />
      <FieldInputText
        v-model="data.clientId"
        label="Client ID"
        required
        prop="clientId"
        :rules="{ required: true, message: 'Please enter client id', trigger: 'blur' }" />
      <FieldInputText
        v-model="data.clientSecret"
        label="Client Secret"
        required
        prop="clientSecret"
        :rules="{ required: true, message: 'Please enter client secret', trigger: 'blur' }" />
      <FieldInputText
        v-model="data.dateAdded"
        label="Date Added"
        required
        prop="dateAdded"
        :rules="{ required: true, message: 'Please enter date added', trigger: 'blur' }" />
      <FieldInputText
        v-model="data.adminUsername"
        label="Admin Username"
        required
        prop="adminUsername"
        :rules="{ required: true, message: 'Please enter admin username', trigger: 'blur' }" />
      <FieldInputText
        v-model="data.adminPassword"
        label="Admin Password"
        required
        prop="adminPassword"
        :rules="{ required: true, message: 'Please enter admin password', trigger: 'blur' }" />
    </Form>
    <div style="text-align: right">
      <BaseButton appearance="accent" @click="clickHandler">Save Site Details</BaseButton>
    </div>
  </Drawer>
</template>
