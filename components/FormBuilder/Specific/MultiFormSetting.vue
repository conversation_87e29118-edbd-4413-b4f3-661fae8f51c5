<script setup lang="ts">
import { FORM_NEW_MULTIFORM } from '~/constants/default-values';
import type { MultiFormForm } from '~/types/form';

const props = defineProps({
    modelValue: {
        type: Array as PropType<MultiFormForm[]>,
        default: () => []
    }
})

const emit = defineEmits(['update:modelValue'])
const fieldStore = useFieldStore();

const activeForm = computed(() =>
{
    return fieldStore.getActiveForms()
})
const activeGroup = computed(() =>
{
    return fieldStore.getActiveSiteCollectionGroups()
})

function addHandler()
{
    const newModelValue = [...props.modelValue]
    newModelValue.push(FORM_NEW_MULTIFORM)
    emit('update:modelValue', newModelValue)
}

function clickHandlerDeleteForm(index: number)
{
    const newModelValue = [...props.modelValue]
    newModelValue.splice(index, 1)
    emit('update:modelValue', newModelValue)
}
</script>

<template>
    <FormBuilderSpecificOptionHeader label="Multi Form Details" @add="addHandler" />
    <fluent-accordion class="optionAccordion">
        <FormBuilderSpecificAccordionOption v-for="form, index in modelValue">
            <template #header>
                {{ form.actionDescription }}
                <BaseButtonDelete @click="clickHandlerDeleteForm(index)" />
            </template>
            <FieldInputText v-model="modelValue[index].actionDescription" label="Action Description" />
            <FieldInputSelect v-model="form.formKey" :options="activeForm" v-slot="{ option }" :no-margin="true"
                label="Form Key" value-tag="key" label-tag="name">
                {{ option.name }}
            </FieldInputSelect>
            <FieldInputSelect v-model="form.groups" :options="activeGroup" v-slot="{ option }" :no-margin="true"
                label="Group">
                {{ option }}
            </FieldInputSelect>
        </FormBuilderSpecificAccordionOption>
    </fluent-accordion>
</template>

<style lang="scss" scoped>
.conditionToAppear {
    display: flex;
    flex-direction: column;
    gap: $unit;
}

.row__container {
    display: grid;
    grid-template-columns: 20px 1fr 20px;
    gap: $unit;
    padding: 0 $margin $margin 0;
}

.option {
    &--fieldname {
        display: grid;
        grid-template-columns: 200px 1fr;
        gap: $unit;
    }
}


.fieldname {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
}

.stagename {
    color: $primary-gray;
}
</style>