/**
 * user body example
 * { '__metadata': { 'type': 'SP.User' }, 'LoginName':'i:0#.w|domain\\user' }
 */

export default defineEventHandler(async (event) => {
  const query = getQuery(event)
  const { groupId, user } = await readBody(event)
  const { tenantUrl, tenantId, siteUrl } = query

  const accessToken = await getAccessToken(tenantUrl as string, tenantId as string)
  const url = `${siteUrl}/_api/web/sitegroups(${groupId})/users`
  const headers = {
    accept: 'application/json; odata=verbose',
    'content-type': 'application/json; odata=verbose',
    Authorization: `Bearer ${accessToken}`,
  }
  try {
    const response = await fetch(url, {
      method: 'POST',
      headers,
      body: JSON.stringify(user),
    })
    if (!response.ok) {
      const error = await response.json()
      // console.error("Error adding user:", error.error.message.value);
      return {
        ok: false,
        errorMessage: error.error.message.value,
      }
    }
    return {
      ok: true,
      data: await response.json(),
      message: 'User added successfully',
    }
  } catch (error) {
    console.error(`Failed to add user:`, error)
    return {
      ok: false,
      errorMessage: error,
    }
  }
})
