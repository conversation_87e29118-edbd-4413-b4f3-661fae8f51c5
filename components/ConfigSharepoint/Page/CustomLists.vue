<script setup lang="ts">
import type { SharepointCustomListSetting } from '~/types/sharepointConfig';

const sharepointConfigStore = useConfigSharepointStore()
const { activeSharepointConfig } = storeToRefs(sharepointConfigStore)
const uiStore = useUiStore()

const sharepointCustomList = ref({} as SharepointCustomListSetting)

onMounted(() => {
    sharepointCustomList.value = useCloneDeep(activeSharepointConfig.value)
})

watch(activeSharepointConfig, () => {
    sharepointCustomList.value = useCloneDeep(activeSharepointConfig.value)
})

function resetHandler() {
    sharepointCustomList.value = useCloneDeep(activeSharepointConfig.value)
}

async function saveHandler() {
    uiStore.setLoading(true, 'Saving Sharepoint Custom List...')
    await sharepointConfigStore.saveHandler(sharepointCustomList.value)
    uiStore.setLoading(false)
}

</script>

<template>
    <Card dis-hover>
        <BaseLayoutRowFormHeader grid-layout="25px 1fr 1fr 1fr 50px">
            <span></span>
            <span>Type</span>
            <span>Name</span>
            <span>Listname</span>
            <span></span>
        </BaseLayoutRowFormHeader>
        <ConfigSharepointCustomListsForm v-model="sharepointCustomList.customlists" />
        <BaseButtonSaveReset @reset="resetHandler" @save="saveHandler" />
    </Card>
</template>