<script setup lang="ts">
  import type { SiteCollection } from '~/types/siteCollection'

  const props = defineProps({
    site: {
      type: Object as PropType<SiteCollection>,
      required: true,
    },
  })

  const route = useRoute()
  const modulesStore = useModulesStore()
  const siteModules = computed(() => {
    return modulesStore.getSiteModules(props.site.modules)
  })
  const navigationRoute = '/tenants/' + props.site.tenant + '/' + props.site.key + '/module/'

  function getToUrl(moduleKey: string) {
    return navigationRoute + moduleKey
  }
</script>

<template>
  <div class="siderMenuModules">
    Module
    <template v-for="module in siteModules">
      <NuxtLink :to="getToUrl(module.key)">
        <div class="moduleLink" :class="{ active: module.key === route.params.key }">
          {{ module.name }}
        </div>
      </NuxtLink>
    </template>
  </div>
</template>

<style lang="scss" scoped>
  .siderMenuModules {
    display: flex;
    flex-direction: column;
    margin-left: 22px;
  }

  .moduleLink {
    padding: $sub-unit;
    padding-left: $unit;
    cursor: pointer;
    font-size: 0.875rem;
    font-weight: 400;
    color: #161616;

    &:hover {
      background-color: $sider-hover-color;
      text-decoration: underline;
    }
  }

  .active {
    background-color: $sider-hover-color;
    color: black;
  }
</style>
