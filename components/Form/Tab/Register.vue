<script setup lang="ts">
  import { FormTabNames } from '~/constants/tabs'

  const registerStore = useConfigRegisterStore()
  const formConfigStore = useFormConfigStore()
  const { activeFormConfig } = storeToRefs(formConfigStore)
  const router = useRouter()
  const route = useRoute()
  onMounted(() => {
    router.replace({
      query: {
        ...route.query,
        tab: FormTabNames.REGISTERS,
      },
    })
    registerStore.initialiseRegisterFor(RegisterFor.FORM)
  })

  watch(
    () => activeFormConfig.value,
    () => {
      registerStore.initialiseRegisterFor(RegisterFor.FORM)
    },
  )
</script>

<template>
  <ConfigRegisterPage />
</template>
