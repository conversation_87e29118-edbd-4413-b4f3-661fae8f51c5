<template>
  <fluent-card class="cardPanel">
    <div class="title">
      <slot name="title" />
    </div>
    <div class="content">
      <slot />
    </div>
  </fluent-card>
</template>

<style lang="scss" scoped>
.cardPanel {
  padding: $unit;

  .title {
    font-size: 0.9rem;
    font-weight: bold;
    margin-bottom: $unit;
  }

  .content {
    min-width: 200px;
    display: flex;
    flex-direction: column;
    gap: $unit;
    font-size: $font-content;
  }
}
</style>