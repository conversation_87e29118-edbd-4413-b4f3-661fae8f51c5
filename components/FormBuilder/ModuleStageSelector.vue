<script setup lang="ts">
import type { Stage } from '~/types/data';

const stagesStore = useStagesStore()
const formBuilderStore = useFormBuilderStore()
const { formBuilderActiveStage } = storeToRefs(formBuilderStore)
const { workflowStages } = storeToRefs(stagesStore)

function clickHandlerActive(stage: Stage) {
    formBuilderStore.resetActiveField();
    formBuilderStore.resetEditField();
    formBuilderStore.setActiveStage(stage)
    formBuilderStore.initialiseFormBuilderForModule()
}
</script>

<template>
    <div class="stageButtons">
        <template v-for="stage in workflowStages">
            <BaseButton @click="clickHandlerActive(stage)"
                :appearance="stage.firebaseKey === formBuilderActiveStage.firebaseKey ? 'accent' : 'neutral'"
                class="button">
                {{ stage.name }}
            </BaseButton>
        </template>
    </div>
</template>

<style lang="scss" scoped>
.stageButtons {
    width: 100%;
    overflow-x: auto;
    display: block;
    white-space: nowrap;
    margin-bottom: $unit;
}

.button {
    margin-right: $sub-unit;
}
</style>