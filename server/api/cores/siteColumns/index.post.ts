// UPDATE Core Site Columns
// params = { coreKey: string }
// body = CoreSiteColumn[]

import { CoreSiteColumn } from '~/types/core'
import { useAuthenticatedFirebase } from '~/server/utils/firebaseDatabase'

export default defineEventHandler(async (event) => {
  const { coreKey, siteColumns } = await readBody(event)

  if (!coreKey || !siteColumns) {
    return {
      ok: false,
      errorMessage: 'Missing required parameters',
    }
  }

  try {
    const { firebaseSet } = useAuthenticatedFirebase(event)
    const data = await firebaseSet(`/cores/${coreKey}/siteColumns`, siteColumns)
    return {
      ok: true,
      data,
      message: 'Core Site Columns updated successfully',
    }
  } catch (error) {
    return {
      ok: false,
      errorMessage: error,
    }
  }
})
