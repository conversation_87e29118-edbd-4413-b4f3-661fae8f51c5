// UPDATE Core
// body { coreKey: string, coreData: Core }

import { Core } from '~/types/core'
import { useAuthenticatedFirebase } from '~/server/utils/firebaseDatabase'

export default defineEventHandler(async (event) => {
  const { coreKey, coreData } = await readBody(event)

  if (!coreKey || !coreData) {
    return {
      ok: false,
      errorMessage: 'Missing coreKey or coreData',
    }
  }

  try {
    const { firebaseUpdate } = useAuthenticatedFirebase(event)
    await firebaseUpdate(`/cores/${coreKey}`, coreData)
    return {
      ok: true,
      message: `Core with key ${coreKey} updated successfully`,
    }
  } catch (error) {
    return {
      ok: false,
      errorMessage: `Failed to update core with key ${coreKey}`,
    }
  }
})
