<script setup lang="ts">
const { activeEditField } = useFormBuilderFieldSetting()
</script>

<template>
  <Form label-position="left">
    <FormBuilderFieldSettingLabel>Multiple Submit Settings</FormBuilderFieldSettingLabel>
    <FieldInputSwitch v-model="activeEditField.multipleSubmit" label="Enable Multiple Submit" />
    <FieldInputText v-model="activeEditField.delimiter" label="Seperator" />
  </Form>
</template>