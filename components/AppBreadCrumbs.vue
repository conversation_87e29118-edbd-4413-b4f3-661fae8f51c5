<script setup lang="ts">
const { getCrumbs } = useUiUtils()
const breadcrumbs = computed(() => getCrumbs())
</script>

<template>
  <div class="breadcrumbs">
    <template v-for="(crumb, index) in breadcrumbs">
      <div class="crumb" v-if="crumb" @click="$router.push(crumb.to)">
        {{ crumb.name }}
      </div>
      <div class="separator" v-if="index !== breadcrumbs.length - 1">/</div>
    </template>
    <slot />
  </div>
</template>

<style lang="scss" scoped>
.breadcrumbs {
  display: flex;
  gap: $unit;
  text-transform: uppercase;
  font-weight: 400;
  color: black;
  padding: $margin;
  position: sticky;
  top: 0;
  z-index: 1;
  background-color: white;

  .crumb {
    cursor: pointer;

    &:hover {
      text-decoration: underline;
    }
  }

}
</style>