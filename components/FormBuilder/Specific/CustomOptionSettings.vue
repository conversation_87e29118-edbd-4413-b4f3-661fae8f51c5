<script setup lang="ts">
  import { FORM_NEW_CUSTOM_OPTION } from '~/constants/default-values'
  import { CustomOptionType, DatepickerDefaultValue, type CustomOptionData } from '~/types/form'

  const emit = defineEmits(['update:modelValue'])
  const props = defineProps({
    modelValue: {
      type: Array as PropType<CustomOptionData[]>,
      default: () => [],
    },
    headerLabel: {
      type: String,
      default: 'Option',
    },
  })

  const customOptionTypeOptions = Object.values(CustomOptionType)

  function addHandler() {
    const newModelValue = [...props.modelValue]
    newModelValue.push(useClone(FORM_NEW_CUSTOM_OPTION))
    emit('update:modelValue', newModelValue)
  }

  function clickHandlerDeleteOption(index: number) {
    const newModelValue = [...props.modelValue]
    newModelValue.splice(index, 1)
    emit('update:modelValue', newModelValue)
  }

  function updateHandlerType(option: CustomOptionType, index: number) {
    const newModelValue = [...props.modelValue]
    newModelValue[index].type = option
    emit('update:modelValue', newModelValue)
  }
</script>

<template>
  <FormBuilderSpecificOptionHeader :label="headerLabel" @add="addHandler" />
  <fluent-accordion class="optionAccordion">
    <FormBuilderSpecificAccordionOption v-for="(option, index) in modelValue">
      <template #header>
        {{ option.name }}
        <BaseButtonDelete @click="clickHandlerDeleteOption(index)" />
      </template>
      <FieldInputSelect
        v-model="modelValue[index].type"
        label="Type"
        :options="customOptionTypeOptions"
        v-slot="{ option }">
        <div>
          {{ option }}
        </div>
      </FieldInputSelect>
      <FieldInputText v-model="modelValue[index].name" label="Display Name" />
      <FieldInputText v-model="modelValue[index].fieldName" label="Field Name" />
      <div class="columnSpan">
        <FieldInputSwitch v-model="modelValue[index].customColSpan" label="Custom Column Span" />
        <FieldInputNumber
          v-model="modelValue[index].colSpan"
          label="Column Span"
          v-if="modelValue[index].customColSpan" />
      </div>
      <template v-if="modelValue[index].type === CustomOptionType.SELECT">
        <FieldInputText label="Options (Comma seperated)" v-model="modelValue[index].options" />
      </template>
      <template v-if="modelValue[index].type === CustomOptionType.DATEPICKER">
        <FieldInputSwitch
          v-model="modelValue[index].disableBeforeToday"
          label="Disable Date before Today" />
        <FieldInputSwitch
          v-model="modelValue[index].disableAfterToday"
          label="Disable Date after Today" />
        <FieldInputRadioButton v-model="modelValue[index].defaultValue" label="Default Value">
          <Radio v-for="defaultValue in DatepickerDefaultValue" :label="defaultValue" />
        </FieldInputRadioButton>
        <FieldInputNumber
          v-model="modelValue[index].daysAhead"
          label="No. of Days Ahead"
          v-if="modelValue[index].defaultValue === DatepickerDefaultValue.Custom" />
      </template>
      <template v-if="modelValue[index].type === CustomOptionType.PEOPLEPICKER">
        <FieldInputSwitch
          v-model="modelValue[index].selectFromResponsibleGroup"
          label="Select from Responsible Group" />
      </template>
      <template v-if="modelValue[index].type === CustomOptionType.MODULES">
        <FieldInputText label="Modules (Comma seperated)" v-model="modelValue[index].options" />
      </template>
      <template v-if="modelValue[index].type === CustomOptionType.MODULEITEMS">
        <FieldInputText label="Module Field Name" v-model="modelValue[index].moduleFieldName" />
        <FieldInputText label="Item Field Name" v-model="modelValue[index].itemFieldName" />
      </template>
      <template v-if="modelValue[index].type === CustomOptionType.MODULEITEMSFIELD">
        <FieldInputText label="Module Field Name" v-model="modelValue[index].moduleFieldName" />
        <FieldInputText
          label="Related Item Field Name"
          v-model="modelValue[index].relatedItemFieldName" />
      </template>
    </FormBuilderSpecificAccordionOption>
  </fluent-accordion>
</template>

<style lang="scss" scoped>
  .columnSpan {
    display: flex;
    justify-content: space-between;
  }
</style>
