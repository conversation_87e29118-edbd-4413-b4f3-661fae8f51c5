// GET ALL Forms
import { useAuthenticatedFirebase } from '~/server/utils/firebaseDatabase'

export default defineEventHandler(async (event) => {
  try {
    const { firebaseFetch } = useAuthenticatedFirebase(event)
    const forms = await firebaseFetch('/forms')
    return {
      ok: true,
      data: forms,
      message: 'Forms fetched successfully',
    }
  } catch (error) {
    return {
      ok: false,
      errorMessage: error,
    }
  }
})
