<script setup lang="ts">
defineProps({
    modelValue: {
        type: String
    },
    required: {
        type: Boolean,
        default: false
    },
    rules: {
        type: Object,
        default: {}
    }
})
const emit = defineEmits(['update:modelValue'])

function inputHandler(event: Event) {
    const target = event.target as HTMLInputElement
    emit('update:modelValue', target.value)
}
</script>

<template>
    <fluent-text-field :value="modelValue" @input="inputHandler" class="field" :required="required" />
</template>

<style lang="scss" scoped>
.field {
    width: 100%;
}
</style>