<script setup lang="ts">
const { assessmentsField } = useFormBuilderFieldSetting()
</script>

<template>
    <FormBuilderFieldSettingLabel>Assessments Settings</FormBuilderFieldSettingLabel>
    <template v-if="assessmentsField">
        <Form label-position="top">
            <FieldInputText label="Template List Name" v-model="assessmentsField.listname" />
            <FieldInputText label="Template Assessment List Name" v-model="assessmentsField.assessmentListname" />
        </Form>
    </template>
</template>