<script lang="ts" setup>

const formBuilderStore = useFormBuilderStore()
const uiStore = useUiStore()
async function clickHandlerSave() {
    uiStore.setLoading(true, 'Saving Form Builder...')
    await formBuilderStore.saveHandler()
    uiStore.setLoading(false)
}

async function clickHandlerReset() {
    formBuilderStore.resetFormBuilder()
}
</script>

<template>
    <div class="formBuilderButton">

    </div>
    <div class="formBuilder">
        <FormBuilderFieldSelector />
        <FormBuilderCanvas />
        <div>
            <div class="buttonWrapper">
                <BaseButton @click="clickHandlerReset">Reset Form Builder</BaseButton>
                <BaseButton appearance="accent" @click="clickHandlerSave">Save Form</BaseButton>
            </div>
            <FormBuilderFieldSetting />
        </div>

    </div>
</template>

<style lang="scss" scoped>
.formBuilder {
    display: grid;
    grid-template-columns: $form-builder-layout;
    column-gap: $margin;
}

.buttonWrapper {
    display: flex;
    justify-content: flex-end;
    gap: $padding;
    margin-bottom: $unit;
}
</style>