import { signInWith<PERSON>ustomToken, signInWithEmailAndPassword } from 'firebase/auth'
import { useAuthUser } from './useAuthUser'
import type { User } from '~/types/auth'

export const useAuth = () => {
  const authUser = useAuthUser()
  const { $auth } = useNuxtApp()

  const setUser = (user: User | null) => {
    authUser.value = user
  }

  const setUserData = (data: Partial<User>) => {
    if (!authUser.value) return
    authUser.value = {
      ...authUser.value,
      ...data,
      id: data.id ?? authUser.value.id,
      toJSON: data.toJSON ?? (() => ({})),
    }
  }

  const me = async () => {
    if (!authUser.value) {
      try {
        const { user } = await $fetch<{ user: User | null }>('/api/auth/me', {
          headers: useRequestHeaders(['cookie']),
        })
        setUser(user)
      } catch (error) {
        console.error('Auth check failed:', error)
        setUser(null)
      }
    }

    return authUser
  }

  const signUpNewUser = async (name: string, mobile: string, email: string, password: string) => {
    try {
      await $fetch('/api/auth/user', {
        method: 'POST',
        body: {
          displayName: name,
          email,
          password,
          phoneNumber: mobile,
        },
      })

      // login after signup
      await loginWithEmail(email, password)
    } catch (error) {
      console.error('Signup failed:', error)
      setUser(null)
    }
  }

  const loginWithEmail = async (email: string, password: string) => {
    try {
      const { user, customToken } = await $fetch<{ user: User; customToken: string }>(
        '/api/auth/login',
        {
          method: 'POST',
          body: { email, password },
        },
      )

      // Client-side login with custom token for real-time updates
      await signInWithCustomToken($auth, customToken)

      setUser(user)
    } catch (error) {
      console.error('Login failed:', error)
      setUser(null)
      return null
    }

    return authUser
  }

  const updateUser = async (uid: string, data: Partial<User>) => {
    try {
      await $fetch('/api/auth/update', {
        method: 'POST',
        body: { uid, data },
      })

      setUserData(data)
    } catch (error) {
      console.error('Update user failed:', error)
      return error
    }
  }

  const logout = async () => {
    try {
      await $fetch('/api/auth/logout', {
        method: 'POST',
      })

      setUser(null)
    } catch (error) {
      console.error('Logout failed:', error)
    }
  }

  return {
    authUser,
    me,
    loginWithEmail,
    logout,
    signUpNewUser,
    updateUser,
  }
}
