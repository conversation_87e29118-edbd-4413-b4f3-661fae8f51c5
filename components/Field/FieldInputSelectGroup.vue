<script setup lang="ts">
  import type { OptionGroup } from '@/types'

  defineProps({
    label: {
      type: String,
    },
    modelValue: {
      type: String,
      required: true,
    },
    groups: {
      type: Object as PropType<OptionGroup>,
    },
    clearable: {
      type: Boolean,
      default: false,
    },
  })
  const emit = defineEmits(['update:modelValue'])

  function updateHandler(text: string) {
    emit('update:modelValue', text)
  }
</script>
<template>
  <FieldInput :label="label">
    <Select
      :modelValue="modelValue"
      @update:modelValue="updateHandler"
      filterable
      transfer
      :clearable="clearable">
      <OptionGroup :label="name" v-for="(options, name) in groups">
        <Option v-for="option in options" :key="option.value" :value="option.value">
          {{ option.label }}
        </Option>
      </OptionGroup>
    </Select>
  </FieldInput>
</template>
