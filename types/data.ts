import type { FormFieldData } from './form'

export type Tenant = {
  adminPassword: string
  adminUsername: string
  clientId: string
  clientSecret: string
  dateAdded: string
  key: string
  name: string
  tenant: string
  tenantId: string
}

export type HeaderField = {
  header: string
  fieldName: string
}

export type Column = {
  title: string
  slot?: string
  key?: string
  width?: number
  align?: string
  sortType?: string
}

export enum SiteDrawerType {
  SETTING = 'SETTING',
  DEPLOYMENT = 'DEPLOYMENT',
  MODULE = 'MODULE',
  FORM = 'FORM',
  SITE = 'SITE',
}

export type Module = ModuleName & {
  tenant: string
  key: string
}

export type ModuleName = {
  name: string
}

export type Stage = StageSetting &
  StageForm & {
    firebaseKey: string
    key: string
    workflowKey: string
    order: number
  }

export type StageSetting = {
  name: string
  description: string
  review: boolean
  myActionsPanel: boolean
  saveButton: boolean
  rejectButton: boolean
  rejectButtonText: string
  rejectGoToStage: string
  rejectNormalButton: boolean
  rejectNormalGoToStage: string
  dynamicProcessFlow: boolean
  flowConditions: FlowCondition[]
  multipleSubmit: boolean
}

export type FlowCondition = {
  fieldName: string
  goToStage: string
  value: string
}

export type StageForm = {
  form: FormFieldData[]
}

export type Form = FormName & {
  key: string
}

export type FormName = {
  name: string
}
