import { cert, initializeApp, getApps, getApp } from "firebase-admin/app";
import { getAuth } from "firebase-admin/auth";
import { getFirestore, FieldPath } from "firebase-admin/firestore";
import { getDatabase } from "firebase-admin/database";

export const useFirebaseAdmin = () => {
  const runtimeConfig = useRuntimeConfig();
  if (!getApps().length) {
    initializeApp({
      credential: cert({
        projectId: runtimeConfig.FIREBASE_PROJECT_ID,
        clientEmail: runtimeConfig.FIREBASE_CLIENT_EMAIL,
        privateKey: runtimeConfig.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, "\n"),
      }),
      databaseURL: runtimeConfig.FIREBASE_DATABASE_URL,
    });
  }
  return {
    auth: getAuth(getApp()),
    db: getDatabase(),
    fs: getFirestore(),
    fsFieldPath: FieldPath,
  };
};
