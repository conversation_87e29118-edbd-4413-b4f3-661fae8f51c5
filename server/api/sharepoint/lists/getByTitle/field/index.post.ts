/**
 * field body example
 * { '__metadata': { 'type': 'SP.FieldText' }, 'Title': 'MyField', "FIeldTypeKind": 2}
 */

export default defineEventHandler(async (event) => {
  try {
    const query = getQuery(event)
    const { listTitle, field } = await readBody(event)
    const { tenantUrl, tenantId, siteUrl } = query
    const accessToken = await getAccessToken(tenantUrl as string, tenantId as string)
    const url = `${siteUrl}/_api/web/lists/getByTitle('${listTitle}')/fields`
    const headers = {
      accept: 'application/json; odata=verbose',
      'content-type': 'application/json; odata=verbose',
      Authorization: `Bearer ${accessToken}`,
    }
    const response = await fetch(url, {
      method: 'POST',
      headers,
      body: JSON.stringify(field),
    })

    if (!response.ok) {
      const error = await response.json()
      console.log('Error response:', error)
      return {
        ok: false,
        errorMessage: `Failed to add Field to List: ${error.error.message.value}`,
      }
    }
    return {
      ok: true,
      data: response,
      message: `Field added to List.`,
    }
  } catch (error) {
    console.error('Error adding field to list:', error)
    return {
      ok: false,
      errorMessage: `Failed to add Field to List: ${error}`,
    }
  }
})
