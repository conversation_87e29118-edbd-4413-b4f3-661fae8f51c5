<script setup lang="ts">
  import type { UpVersionSetting } from '~/types/workflow'

  const versionSetting = ref([] as UpVersionSetting[])
  const uiStore = useUiStore()

  const upVersionStore = useUpVersionStore()
  const { activeVersions, activeUpVersionSetting, modalSelectedIndex } = storeToRefs(upVersionStore)

  const isUpVersionPressed = ref(false)
  const changeLogModal = ref(false)
  const selectedVersion = computed(() => {
    return versionSetting.value[modalSelectedIndex.value] || ({} as UpVersionSetting)
  })
  const deleteModal = ref(false)
  const deleteModalLoading = ref(true)

  onMounted(() => {
    versionSetting.value = useCloneDeep(activeVersions.value)
  })

  watch(activeVersions, () => {
    versionSetting.value = useCloneDeep(activeVersions.value)
    isUpVersionPressed.value = false
  })

  function clickHandlerUpVersion() {
    isUpVersionPressed.value = !isUpVersionPressed.value
  }

  function clickHandlerChangeLog(index: number) {
    changeLogModal.value = !changeLogModal.value
    upVersionStore.setSelectedIndex(index)
  }

  function clickHandlerDelete(index: number) {
    deleteModal.value = !deleteModal.value
    upVersionStore.setSelectedIndex(index)
  }

  function closeHandler() {
    uiStore.toggleWorkflowUpVersionDrawer(false)
  }

  async function updateHandler(index: number) {
    uiStore.setLoading(true, 'Setting active version...')
    await upVersionStore.updateHandler(index)
    uiStore.setLoading(false)
  }

  async function handleDelete() {
    uiStore.setLoading(true, 'Deleting workflow version...')
    await upVersionStore.deleteHandler()
    deleteModal.value = false
    uiStore.setLoading(false)
  }
</script>

<template>
  <Modal v-model="changeLogModal" :title="selectedVersion.version">
    {{ selectedVersion.changeLog ?? 'Change Log not documented' }}
  </Modal>
  <Modal
    v-model="deleteModal"
    :title="`Delete workflow version ${selectedVersion.version}`"
    @on-ok="handleDelete"
    :loading="deleteModalLoading">
    <p>
      Are you sure you want to delete this workflow?<br />

      Note: This action cannot be undone.
    </p>
  </Modal>
  <template v-for="(formConfig, index) in versionSetting">
    <div class="content">
      <div>
        {{ new Date(formConfig.created).toLocaleDateString() }}
      </div>
      <div>
        {{ formConfig.version }}
      </div>
      <div>
        <FieldInputSwitchNoWrapper
          :modelValue="formConfig.inUse"
          @update:modelValue="updateHandler(index)" />
      </div>
      <div class="status">
        <template v-if="activeVersions[index].inUse">
          <Tag type="dot" color="success">Active</Tag>
          <Icon
            type="md-arrow-up"
            class="iconButton"
            @click="clickHandlerUpVersion"
            v-if="!isUpVersionPressed" />
          <Icon type="md-close" class="iconButton" @click="clickHandlerUpVersion" v-else />
        </template>
        <template v-else>
          <Icon type="md-information" class="iconButton" @click="clickHandlerChangeLog(index)" />
          <Icon type="md-trash" class="iconButton__delete" @click="clickHandlerDelete(index)" />
        </template>
      </div>
    </div>
    <ModuleDrawerWorkflowVersionUpForm
      v-if="isUpVersionPressed && formConfig.key === activeUpVersionSetting.key"
      @close-drawer="closeHandler" />
  </template>
</template>

<style lang="scss" scoped>
  .content {
    display: grid;
    grid-template-columns: 25% 25% 25% 25%;
    color: $table-font-color;
    padding: $padding;
    align-items: center;
    background-color: $primary-white;
    font-weight: normal;
    border-bottom: 1px solid $primary-gray;

    &:hover {
      background-color: $table-row-hover;
    }

    &:active {
      background-color: $table-row-pressed;
    }
  }

  .iconButton {
    &:hover {
      background-color: $primary-gray;
      border-radius: 100%;
    }

    cursor: pointer;
    padding: $padding;
    margin: 0 $margin;

    &__delete {
      @extend .iconButton;

      &:hover {
        color: $delete-foreground;
        background-color: $delete-background;
      }
    }
  }

  .status {
    &__active {
      width: 16px;
      background-color: $active-indicator;
    }
  }
</style>
