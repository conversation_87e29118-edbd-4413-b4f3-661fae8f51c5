<script setup lang="ts">
import type { SiteCollection } from '~/types/siteCollection'
import { InfoTagType } from '../types/ui'

const props = defineProps({
  site: {
    type: Object as PropType<SiteCollection>,
    required: true
  }
})

const router = useRouter()
const collapsed = ref(true)


function getSiteName(siteUrl: string) {
  return siteUrl.substring(siteUrl.lastIndexOf('/') + 1)
}

function getInfoTagType(environment: string) {
  switch (environment) {
    case 'Dev':
      return InfoTagType.INFO
    case 'Test':
      return InfoTagType.WARNING
    case 'Live':
      return InfoTagType.SUCCESS
    case 'Staging':
      return InfoTagType.ERROR
  }
}

function clickHandlerToggle() {
  collapsed.value = !collapsed.value
}

function clickHandlerSiteCollection() {
  router.push(`/tenants/${props.site.tenant}/${props.site.key}`)
}
</script>

<template>
  <div class="siderMenuSiteCollectionAccordion">
    <div class="accordion__header" @click="clickHandlerToggle">
      <div class="accordion__header__icon">
        <BaseIconChevronDown v-if="collapsed" />
        <BaseIconChevronUp v-else />
      </div>
      <div class="accordion__header__title">
        <div class="sitecollection__link" @click.stop="clickHandlerSiteCollection">{{ getSiteName(site.url) }}</div>
        <BaseInfoTag :type="getInfoTagType(site.environment)">{{ site.environment }}</BaseInfoTag>
      </div>

    </div>
    <div class="accordion__content" v-if="!collapsed">
      <AppSiderMenuModules :site="site" />
      <AppSiderMenuForms :site="site" />
    </div>
  </div>

</template>

<style lang="scss" scoped>
.siderMenuSiteCollectionAccordion {
  display: flex;
  flex-direction: column;
  margin: 0 $margin;
}

.accordion {
  gap: 0;
  border-bottom: 1px solid rgb(240, 240, 240);

  &__header {
    display: grid;
    grid-template-columns: 5% 95%;
    align-items: center;
    cursor: pointer;

    &__title {
      display: flex;
      justify-content: space-between;
      padding: $sub-unit $unit;
      font-weight: 400;
      color: #161616;
      font-size: 0.875rem;
    }

    &__icon {
      width: 1.2rem;
      height: 1.2rem;
      justify-self: center;
    }



    &:hover {
      background-color: $sider-hover-color;
    }

    &:active {
      background-color: $sider-onpress-color ;
    }
  }
}

.sitecollection__link {
  cursor: pointer;

  &:hover {
    text-decoration: underline;
  }
}
</style>
