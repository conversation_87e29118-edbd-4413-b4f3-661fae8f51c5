<script setup lang="ts">
  import type { SharepointListSetting } from '~/types/sharepointConfig'

  const uiStore = useUiStore()

  const sharepointConfigStore = useConfigSharepointStore()
  const { activeSharepointConfig, activeTableInputFields } = storeToRefs(sharepointConfigStore)

  const sharepointListSetting = ref({ lists: [] } as SharepointListSetting)

  onMounted(async () => {
    await initialiseListPage()
  })

  watch(activeSharepointConfig, async () => {
    await initialiseListPage()
  })

  function resetHandler() {
    sharepointListSetting.value = {
      lists: useCloneDeep(activeSharepointConfig.value.lists),
    }
  }

  async function initialiseListPage() {
    if (!activeSharepointConfig.value.lists) {
      sharepointListSetting.value = {
        lists: [],
      }
    } else {
      sharepointListSetting.value = {
        lists: useCloneDeep(activeSharepointConfig.value.lists),
      }
    }
    // sharepointListSetting.value.lists = useCloneDeep(activeSharepointConfig.value.lists)
    await initialiseFormLists()
  }

  /**
   * Checks the current sharepoint lists against the table input fields
   */
  async function initialiseFormLists() {
    let tableInputFields = useCloneDeep(activeTableInputFields.value)
    // remove duplicate in tableInputFields
    tableInputFields = tableInputFields.filter(
      (field, index, self) => index === self.findIndex((t) => t.fieldName === field.fieldName),
    )
    // check current sharepoint lists against table input fields
    console.log(sharepointListSetting.value)
    for (const list of sharepointListSetting.value.lists) {
      const tableInputField = tableInputFields.find(
        (field) => field.fieldName === list.InternalName,
      )
      if (tableInputField) {
        const index = tableInputFields.indexOf(tableInputField)
        console.log('Removing Table Input Field:', tableInputField)
        if (index !== -1) {
          tableInputFields.splice(index, 1)
        }
      }
    }
    const remaniningFields = useCloneDeep(tableInputFields)

    if (remaniningFields.length > 0) {
      remaniningFields.forEach((field) => {
        if (
          sharepointListSetting.value.lists.some((list) => list.InternalName === field.fieldName)
        ) {
          return
        }
        sharepointListSetting.value.lists.push({
          DisplayName: field.label,
          InternalName: field.fieldName,
        })
      })
      await saveHandler()
    }
  }

  async function saveHandler() {
    uiStore.setLoading(true, 'Saving Sharepoint List...')
    await sharepointConfigStore.saveHandler(sharepointListSetting.value)
    uiStore.setLoading(false)
  }
</script>

<template>
  <Card dis-hover>
    <BaseLayoutRowFormHeader grid-layout="25px 1fr 1fr 30px 50px">
      <span></span>
      <span>Display Name</span>
      <span>Internal Name</span>
      <span></span>
      <span></span>
    </BaseLayoutRowFormHeader>
    <ConfigSharepointListForm v-model="sharepointListSetting.lists" />
    <BaseButtonSaveReset @reset="resetHandler" @save="saveHandler" />
  </Card>
</template>
