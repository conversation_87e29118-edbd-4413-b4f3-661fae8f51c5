<script setup lang="ts">
  import { ActionStatus, ActionType, type Action } from '~/types/workflow'
  import type { Option } from '~/types'

  const props = defineProps({
    modelValue: {
      type: Object as PropType<Action>,
      required: true,
    },
  })

  const emit = defineEmits(['update:modelValue'])

  const fieldStore = useFieldStore()
  const fieldStageOptions = ref<Option[]>([])

  onMounted(() => {
    fieldStageOptions.value = fieldStore.getStageOptions()
  })

  const fieldTypeOptions = Object.values(ActionType).map((value) => value)
  const fieldStatusOptions = Object.values(ActionStatus).map((value) => value)
  const assignedToOptionFieldName = computed(() => {
    const option = fieldStore
      .getPeoplePickerFieldNameOptions()
      .map((field) => ({ label: field, value: field }))
    option.unshift({ label: 'Author', value: 'Author' })
    return option
  })

  const assignedToOptionGroup = computed(() => {
    return fieldStore
      .getActiveSiteCollectionGroups()
      .map((group) => ({ label: group, value: group }))
  })

  const assignedToOptionPerson = computed(() => {
    return fieldStore.getPeoplePickerOptions()
  })

  const assignedToOptionList = computed(() => {
    return fieldStore.getListOptions()
  })

  const assignedToOptionCL = computed(() => {
    return fieldStore.getCustomListOptions()
  })

  function updateHandlerType(option: ActionType) {
    const newModelValue = { ...props.modelValue }
    newModelValue.Type = option
    if (option === (ActionType.GROUP || ActionType.PERSON)) {
      newModelValue.AssignedToId = []
    } else {
      newModelValue.AssignedToId = ''
    }
    emit('update:modelValue', newModelValue)
  }

  function updateHandlerStatus(option: ActionStatus) {
    const newModelValue = { ...props.modelValue }
    newModelValue.Status = option
    emit('update:modelValue', newModelValue)
  }

  function updateHandlerStage(option: string) {
    const newModelValue = { ...props.modelValue }
    newModelValue.Stage = option
    emit('update:modelValue', newModelValue)
  }
</script>

<template>
  <Form label-position="left">
    <FieldInputText
      label="Description"
      v-model="modelValue.ActionDescription"
      class="field"
      placeholder="Action Description" />
    <div class="form">
      <FieldInputSelect
        v-model="modelValue.Type"
        :options="fieldTypeOptions"
        v-slot="{ option }"
        no-margin
        placeholder="Select action type"
        class="field"
        label="Action Type">
        {{ option }}
      </FieldInputSelect>
      <FieldInputSelect
        v-model="modelValue.Status"
        :options="fieldStatusOptions"
        v-slot="{ option }"
        no-margin
        placeholder="Select action status"
        class="field"
        label="Status">
        {{ option }}
      </FieldInputSelect>
      <FieldInputSelect
        v-model="modelValue.Stage"
        :options="fieldStageOptions"
        v-slot="{ option }"
        no-margin
        placeholder="Select stage"
        class="field"
        label="Stage"
        label-tag="label"
        value-tag="value">
        {{ option.label }}
      </FieldInputSelect>

      <FieldInput class="field" label="Assigned to">
        <template v-if="modelValue.Type === ActionType.GROUP">
          <Select v-model="modelValue.AssignedToId" :max-tag-count="1" multiple transfer>
            <Option
              v-for="(option, index) in assignedToOptionGroup"
              :value="option.value"
              :key="index">
              {{ option.label }}
            </Option>
          </Select>
        </template>
        <template v-if="modelValue.Type === ActionType.FIELDNAME">
          <Select v-model="modelValue.AssignedToId" :max-tag-count="1" transfer>
            <Option
              v-for="(option, index) in assignedToOptionFieldName"
              :value="option.value"
              :key="index">
              {{ option.label }}
            </Option>
          </Select>
        </template>
        <template v-if="modelValue.Type === ActionType.PERSON">
          <Select v-model="modelValue.AssignedToId" :max-tag-count="1" multiple transfer>
            <Option
              v-for="(option, index) in assignedToOptionPerson"
              :value="option.value"
              :key="index">
              {{ option.label }}
            </Option>
          </Select>
        </template>
        <template v-if="modelValue.Type === ActionType.LIST">
          <Select v-model="modelValue.AssignedToId" :max-tag-count="1">
            <Option
              v-for="(option, index) in assignedToOptionList"
              :value="option.value"
              :key="index">
              {{ option.label }}
            </Option>
          </Select>
        </template>
        <template v-if="modelValue.Type === ActionType.CL">
          <Select v-model="modelValue.AssignedToId" :max-tag-count="1">
            <Option
              v-for="(option, index) in assignedToOptionCL"
              :value="option.value"
              :key="index">
              {{ option.label }}
            </Option>
          </Select>
        </template>
      </FieldInput>
    </div>
    <template v-if="modelValue.Type === ActionType.LIST">
      <div class="activeCondition">
        <Icon type="md-git-network" />
        <span>Active Condition</span>
        <FieldInputSwitchNoWrapper v-model="modelValue.ActiveCondition" label="Active Condition" />
      </div>
    </template>
    <template v-if="modelValue.Type === ActionType.CL">
      <div class="activeCondition">
        <Icon type="md-git-network" />
        <span>Active Condition</span>
        <FieldInputSwitchNoWrapper v-model="modelValue.ActiveCondition" label="Active Condition" />
      </div>
      <FieldInputRadioButton v-model="modelValue.Process" label="Process">
        <Radio label="S" />
        <Radio label="P" />
      </FieldInputRadioButton>
    </template>
  </Form>
</template>

<style lang="scss" scoped>
  .activeCondition {
    display: flex;
    align-items: center;
    gap: $unit;
    margin-bottom: $margin;
  }

  .form {
    display: grid;
    grid-template-columns: 50% 50%;
    align-items: start;
  }

  .field {
    padding: $padding;
  }
</style>
