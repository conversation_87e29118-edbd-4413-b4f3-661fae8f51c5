<script setup lang="ts">
  import type { SiteCollectionNavigation } from '~/types/siteCollection'

  defineProps<{
    formCustomNav: SiteCollectionNavigation
  }>()

  const emit = defineEmits<{
    (e: 'delete', index: number): void
  }>()
</script>

<template>
  <div>
    <div class="navigationRow">
      <div><b>Title</b></div>
      <div><b>Link</b></div>
      <div><b>Icon</b></div>
      <div><b>IFrame</b></div>
      <div><b>Action</b></div>
    </div>

    <template v-for="(nav, index) in formCustomNav.navigations" :key="index">
      <div class="navigationRow">
        <div><FieldInputTextNoWrapper v-model="nav.title" /></div>
        <div><FieldInputTextNoWrapper v-model="nav.link" /></div>
        <div><FieldInputIconNoWrapper v-model="nav.icon" /></div>
        <div><FieldInputSwitchNoWrapper v-model="nav.iframe" /></div>
        <div><BaseButtonDelete @click="emit('delete', index)" /></div>
      </div>
    </template>
  </div>
</template>

<style scoped lang="scss">
  .navigationRow {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 75px 75px;
    column-gap: $unit;
    margin-bottom: $unit;
    align-items: center;
    justify-items: center;

    div {
      width: 100%;
    }
  }
</style>
