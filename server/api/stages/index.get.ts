// GET ALL Stages
import { useAuthenticatedFirebase } from '~/server/utils/firebaseDatabase'

export default defineEventHandler(async (event) => {
  try {
    const { firebaseFetch } = useAuthenticatedFirebase(event)
    const stages = await firebaseFetch(`/stages`)
    return {
      ok: true,
      data: stages,
      message: `Successfully fetch stages`,
    }
  } catch (error) {
    return {
      ok: false,
      errorMessage: error,
    }
  }
})
