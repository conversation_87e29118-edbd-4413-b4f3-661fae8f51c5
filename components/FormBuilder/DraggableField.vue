<script setup lang="ts">
import type { FormFieldData } from '~/types/form';

defineProps({
    active: {
        type: Boolean,
        default: false
    },
    width: {
        type: Number,
        default: 100
    },
    field: {
        type: Object as PropType<FormFieldData>,
    },
    isReview: {
        type: Boolean,
        default: false
    },
    isImage: {
        type: Boolean,
        default: false
    }
})

const emit = defineEmits(['click', 'delete', 'edit'])

function clickHandler()
{
    emit('click')
}

function clickHandlerDelete()
{
    emit('delete')
}

const hover = ref(false)

</script>


<template>
    <div class="draggableField handle" :class="{ 'active': active, 'review': isReview }" @click="clickHandler"
        :style="{ width: `${isImage ? 100 : width}%` }">
        <div class="innerContainer" @mouseleave="hover = false" @mouseover="hover = true">
            <div class="field__details" :class="{ 'visible': active || hover }">
                <div class="field__details--type">
                    {{ field?.type }}
                </div>
                <div class="field__details--buttons">
                    <BaseIconDelete @click.stop="clickHandlerDelete" />
                </div>
            </div>
            <slot />
        </div>
    </div>
</template>

<style lang="scss" scoped>
$border : 2px dotted #7c7c7c;

.draggableField {
    box-sizing: border-box;
    cursor: pointer;
    user-select: none;
    text-align: left;
    display: inline-block;
    border-radius: $margin;
    padding: $sub-unit $margin;

    .innerContainer {
        padding: $unit;
        border-radius: $margin;
        border: 2px solid transparent;
        display: flex;
        flex-direction: column;
        gap: $unit;

        &:hover {
            border: $border;
            background: $primary-white;
            transition: border 0.2s;
            background-color: $white-hover;
        }

        .field__details {
            color: transparent;
            display: flex;
            justify-content: space-between;

            &--type {
                display: inline-block;
                padding: 0 $unit;
            }

            &--fieldname {
                display: inline-block;
                padding: 0 $unit;
            }

            &__container {
                display: flex;
                gap: $unit;
            }
        }

        .visible {
            color: $primary-text;

            .field__details--type {
                background: $primary-gray;
                border-radius: $unit;
            }

            .field__details--fieldname {
                background: $primary-gray;
                border-radius: $unit;
            }

            .field__details--buttons {

                display: flex;
                gap: $margin;

                >svg {
                    height: 20px;
                    width: 20px;

                    &:hover {
                        background: $primary-white;
                    }
                }


            }
        }
    }
}

.active {
    .innerContainer {
        padding: $unit;
        box-sizing: content-box;
        border-radius: $margin;
        border: $border;
        background: $primary-white;
    }
}

.review {
    .innerContainer {
        padding: $unit;
        box-sizing: content-box;
        background: $light-green;

        &:hover {
            background: $light-green-hover;
        }

        .visible {
            .field__details--buttons {
                >svg {
                    &:hover {
                        background: $light-green;
                    }
                }
            }
        }
    }
}
</style>