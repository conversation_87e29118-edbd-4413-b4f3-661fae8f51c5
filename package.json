{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "start": "node ./node_modules/nuxt/bin/nuxt.js start", "deploy": "npm run build"}, "dependencies": {"@fluentui/web-components": "^2.6.1", "@microsoft/fast-element": "^1.13.0", "@microsoft/microsoft-graph-client": "^3.0.7", "@nuxtjs/google-fonts": "^3.1.3", "@pinia/nuxt": "^0.5.1", "@pnp/common": "^2.15.0", "@pnp/core": "^4.6.0", "@pnp/graph": "^4.3.0", "@pnp/nodejs": "^4.6.0", "@pnp/queryable": "^4.6.0", "@pnp/sp": "^4.6.0", "@pnp/sp-commonjs": "^2.15.0", "@vueuse/core": "^13.1.0", "@vueuse/nuxt": "^13.1.0", "firebase": "^11.4.1", "firebase-admin": "^13.2.0", "isomorphic-fetch": "^3.0.0", "nuxt": "^3.10.3", "pinia": "^2.1.7", "uuid": "^10.0.0", "view-ui-plus": "^1.3.16", "vue": "^3.4.19", "vue-i18n": "^9.10.2", "vue-json-excel3": "^1.0.29", "vue-router": "^4.3.0", "vuedraggable": "^4.1.0"}, "devDependencies": {"@azure/identity": "^3.4.2", "@azure/msal-node": "^2.16.2", "@firebase/app-types": "^0.9.0", "@pnp/azidjsclient": "^4.6.0", "@types/node-fetch": "^2.6.12", "@types/uuid": "^10.0.0", "axios": "^1.7.5", "less": "^4.2.0", "msal": "^1.4.18", "node-fetch": "^3.3.2", "nuxt-lodash": "^2.5.3", "prettier": "^3.5.3", "sass": "^1.71.1", "ts-node": "^10.9.2", "typescript": "^5.8.3"}, "engines": {"node": "20.12.2"}}