<script setup lang="ts">
  import type { SiteUser, SiteGroup } from '~/types/sharepointAPI'

  const sharepointAPIStore = useSharepointAPIStore()
  const { activeGroup } = storeToRefs(sharepointAPIStore)
  const selectedGroup = ref<SiteGroup>({} as SiteGroup)
  const userInGroup = ref<SiteUser[]>([])
  const loading = ref(false)
  const selectedUserLoginName = ref<string>('')

  onMounted(async () => {
    await initialisePanel()
  })

  watch(activeGroup, async () => {
    await initialisePanel()
  })

  const isGroupSelected = computed(() => !!activeGroup.value)

  async function initialisePanel() {
    loading.value = true
    if (!activeGroup.value) {
      selectedGroup.value = {} as SiteGroup
      userInGroup.value = []
      loading.value = false
      return
    }
    selectedGroup.value = useCloneDeep(activeGroup.value)
    if (!selectedGroup.value.Users) {
      userInGroup.value = []
      loading.value = false
      return
    }
    userInGroup.value = await sharepointAPIStore.fetchDeferUri(
      selectedGroup.value.Users.__deferred.uri,
    )
    loading.value = false
  }

  async function clickHandlerDelete(userId: number) {
    loading.value = true
    await sharepointAPIStore.removeUserFromGroup(selectedGroup.value.Id, userId)
    await initialisePanel()
    loading.value = false
  }

  async function clickHandlerAdd() {
    loading.value = true
    await sharepointAPIStore.addUserToGroup(selectedGroup.value.Id, selectedUserLoginName.value)
    selectedUserLoginName.value = ''
    await initialisePanel()
    loading.value = false
  }
</script>

<template>
  <BaseCardPanel class="card">
    <template #title> Members </template>
    <div class="user_container">
      <BaseLoadingOverlay v-if="loading" />
      <template v-for="user in userInGroup">
        <div class="user">
          {{ user.Title }}
        </div>
        <div class="delete">
          <BaseButtonDelete @click="clickHandlerDelete(user.Id)" />
        </div>
      </template>
    </div>
    <template v-if="isGroupSelected">
      <div class="add">
        <FieldPeoplePicker v-model="selectedUserLoginName" value-tag="LoginName" />
        <BaseButtonAdd @click="clickHandlerAdd" text="User" />
      </div>
    </template>
    <template v-else> Select a group to view members </template>
  </BaseCardPanel>
</template>

<style lang="scss" scoped>
  .card {
    margin-bottom: $margin * 12;
  }

  .user_container {
    display: grid;
    grid-template-columns: 1fr 50px;
    align-items: center;
    column-gap: $unit;
    row-gap: $unit;
    font-size: 14px;
  }

  .user {
    border: 1px solid rgb(234, 234, 234);
    padding: $unit;
  }

  .add {
    display: grid;
    grid-template-columns: 1fr 100px;
    column-gap: $unit;
    font-size: 14px;
  }
</style>
