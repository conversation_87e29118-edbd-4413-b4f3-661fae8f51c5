import { Stage } from '~/types/data'

export default defineEventHandler(async (event) => {
  const { stages } = await readBody(event)
  const { db } = useFirebaseAdmin()

  if (!stages || !Array.isArray(stages)) {
    return {
      ok: false,
      errorMessage: `stages is required and must be an array`,
    }
  }

  try {
    const updates: { [key: string]: Partial<Stage> } = {}
    const newKeys: { [key: string]: string } = {}
    for (const stage of stages) {
      // Generate a new key for each stage
      const newRef = db.ref('stages').push()
      const newKey = newRef.key

      // Store the key for response
      newKeys[stage.name || `stage_${Object.keys(newKeys).length}`] = newKey

      // Add to updates object
      updates[`stages/${newKey}`] = stage
    }
    await db.ref().update(updates)
    return {
      ok: true,
      message: `Successfully created ${stages.length} stages`,
      data: newKeys,
    }
  } catch (error) {
    return {
      ok: false,
      errorMessage: error instanceof Error ? error.message : String(error),
    }
  }
})
