import {
  type Date<PERSON><PERSON><PERSON>ield,
  type <PERSON><PERSON><PERSON><PERSON><PERSON>ield,
  Form<PERSON>ieldType,
  type <PERSON><PERSON>ield,
  type <PERSON>I<PERSON><PERSON><PERSON>ield,
  type People<PERSON><PERSON><PERSON>ield,
  type <PERSON><PERSON>utton<PERSON>ield,
  type <PERSON><PERSON>ield,
  type <PERSON><PERSON><PERSON><PERSON>ield,
  type <PERSON>In<PERSON><PERSON>ield,
  type <PERSON><PERSON>ield,
  type <PERSON><PERSON>ield,
  type TextCustomOption,
  type InputCustomOption,
  type InputNumberCustomOption,
  type SelectCustomOption,
  type TextAreaCustomOption,
  type DatePickerCustomOption,
  type PeoplePickerCustomOption,
  type CustomOptionType,
  type FormFieldData,
} from '~/types/form'

export type AVAILABLEFIELDTYPES =
  | TextInputField
  | NumberInputField
  | SelectField
  | DatepickerField
  | RadioButtonField
  | PeoplePickerField
  | TextAreaField
  | FileUploadField
  | ImageField
  // HiddenField
  | LinkField
  // Custom Option
  | TextCustomOption
  | InputCustomOption
  | InputNumberCustomOption
  | SelectCustomOption
  | TextAreaCustomOption
  | DatePickerCustomOption
  | PeoplePickerCustomOption

type SharepointField = {
  __metadata: { type: string }
  Title: string
  FieldTypeKind: number
  Choices?: {
    results: string[]
  }
}

export function isFieldTableInput(field: FormFieldData) {
  return (
    field.type === FormFieldType.TABLEINPUT ||
    field.type === FormFieldType.ACTION ||
    field.type === FormFieldType.BASICRISK ||
    field.type === FormFieldType.FMEA ||
    field.type === FormFieldType.RPN ||
    field.type === FormFieldType.FINALRPN
  )
}

// is field that holds options as CustomOptionData[]
// export type TableInputFieldType =
//   |
//   | FormFieldType.ACTION
//   | FormFieldType.BASICRISK
//   | FormFieldType.FMEA
//   | FormFieldType.RPN
//   | FormFieldType.FINALRPN

export const useSharepointFieldUtils = () => {
  const FIELDKINDBYTYPE = {
    // Text: 2,
    Input: 2,
    InputNumber: 9,
    Select: 6,
    DatePicker: 4,
    'Radio Buttons': 6,
    Peoplepicker: 20,
    // PeoplePicker: 20,
    TextArea: 3,
    FileUpload: 18,
    Image: 11,
    // Hidden: '',
    Link: 11,
  }

  const FIELDTYPEBYTYPE = {
    // Text: "SP.FieldText",
    Input: 'SP.FieldText',
    InputNumber: 'SP.FieldNumber',
    Select: 'SP.FieldChoice',
    DatePicker: 'SP.FieldDateTime',
    'Radio Buttons': 'SP.FieldChoice',
    Peoplepicker: 'SP.FieldUser',
    // PeoplePicker: 'SP.FieldUser',
    TextArea: 'SP.FieldMultiLineText',
    FileUpload: 'SP.FieldFile',
    Image: 'SP.FieldUrl',
    // Hidden: '',
    Link: 'SP.FieldUrl',
  }

  const SHAREPOINTLIST_INITIAL_FIELD = [
    '_ColorTag',
    '_ComplianceFlags',
    '_ComplianceTag',
    '_ComplianceTagUserId',
    '_ComplianceTagWrittenTime',
    '_IsRecord',
    '_UIVersionString',
    'AppAuthor',
    'AppEditor',
    'Attachments',
    'Author',
    'ComplianceAssetId',
    'ContentType',
    'Created',
    'DocIcon',
    'Edit',
    'Editor',
    'FolderChildCount',
    'ID',
    'ItemChildCount',
    'LinkTitle',
    'LinkTitleNoMenu',
    'Modified',
    'Title',
  ]

  const checkFieldCanBeAdded = (fieldType: FormFieldType | CustomOptionType) => {
    return fieldType in FIELDKINDBYTYPE && fieldType in FIELDTYPEBYTYPE
  }

  const convertFormFieldToSharepointField = (field: AVAILABLEFIELDTYPES) => {
    const fieldBody: SharepointField = {
      __metadata: {
        type: FIELDTYPEBYTYPE[field.type as keyof typeof FIELDTYPEBYTYPE],
      },
      Title: field.fieldName,
      FieldTypeKind: FIELDKINDBYTYPE[field.type as keyof typeof FIELDKINDBYTYPE],
    }

    // if (field.type === "Select" || field.type === "Radio Buttons") {
    //   fieldBody.Choices = {
    //     results: field.options.map((option) => option.name),
    //   };
    // }

    return fieldBody
  }

  function isFieldInternalNameInitial(fieldInternalName: string) {
    return SHAREPOINTLIST_INITIAL_FIELD.includes(fieldInternalName)
  }

  return {
    checkFieldCanBeAdded,
    convertFormFieldToSharepointField,
    isFieldInternalNameInitial,
  }
}
