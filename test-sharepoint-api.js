import crypto from 'crypto'
import { ConfidentialClientApplication } from '@azure/msal-node'
import fetch from 'node-fetch'

// This script tests making an actual API call to SharePoint
// Run with: node test-sharepoint-api.js

// Replace these with your actual values or use environment variables
const TENANT_ID = process.env.TENANT_ID || 'your-tenant-id'
const TENANT_URL = process.env.TENANT_URL || 'https://yourtenant.sharepoint.com'
const SITE_URL = process.env.SITE_URL || 'https://yourtenant.sharepoint.com/sites/yoursite'
const CLIENT_ID = process.env.SHAREPOINT_CLIENT_ID || '0c6e6a75-2629-4285-b8a6-d64908bbad5a'
const WORKBENCH_KEY = process.env.WORKBENCH_KEY

async function testSharePointAPI() {
  if (!WORKBENCH_KEY) {
    console.error('WORKBENCH_KEY environment variable is not set')
    return
  }

  try {
    // Create private key from raw key
    const privateKeyObject = crypto.createPrivateKey({
      key: WORKBENCH_KEY.replace(/\\n/g, '\n'),
      format: 'pem',
    })

    const privateKey = privateKeyObject.export({
      format: 'pem',
      type: 'pkcs8',
    })

    // Configure the client application
    const config = {
      auth: {
        authority: `https://login.microsoftonline.com/${TENANT_ID}`,
        clientId: CLIENT_ID,
        clientCertificate: {
          thumbprint: '5D656A5A81A048E13DBCAD1BDF6C7365851BD190',
          privateKey: privateKey,
        },
      },
    }

    console.log('Creating ConfidentialClientApplication...')
    const cca = new ConfidentialClientApplication(config)

    console.log('Acquiring token...')
    const result = await cca.acquireTokenByClientCredential({
      scopes: [`${TENANT_URL}/.default`],
    })

    if (!result) {
      console.error('❌ Failed to acquire token')
      return
    }

    console.log('✅ Token acquired successfully')

    // Make a simple API call to SharePoint to verify the token works
    console.log('Making API call to SharePoint...')
    const apiUrl = `${SITE_URL}/_api/web`

    const response = await fetch(apiUrl, {
      headers: {
        Accept: 'application/json;odata=verbose',
        Authorization: `Bearer ${result.accessToken}`,
      },
    })

    if (response.ok) {
      const data = await response.json()
      console.log('✅ SharePoint API call successful!')
      console.log('Site title:', data.d.Title)
    } else {
      console.error('❌ SharePoint API call failed:', response.status, response.statusText)
      const errorText = await response.text()
      console.error('Error details:', errorText)
    }
  } catch (error) {
    console.error('❌ Error during test:', error)
  }
}

testSharePointAPI().catch(console.error)
