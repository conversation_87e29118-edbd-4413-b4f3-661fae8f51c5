<script setup lang="ts">
  import { useSettingForm } from '~/composables/useSettingForm'
  import { CONFIG_PERMISSION_NEW_PERMISSION } from '~/constants/default-values'
  import { type PermissionSetting } from '~/types/permission'

  const permissionStore = useConfigPermissionStore()
  const { activePermissionSetting } = storeToRefs(permissionStore)

  const permissionSetting = ref({} as PermissionSetting)

  const { resetHand<PERSON>, submitHandler } = useSettingForm<PermissionSetting>({
    form: permissionSetting,
    activeData: activePermissionSetting,
    updateHandler: async () => {
      await permissionStore.saveHandler(permissionSetting.value)
    },
  })

  function addHandler() {
    try {
      if (!permissionSetting.value.permissions) {
        permissionSetting.value.permissions = []
      }
      permissionSetting.value.permissions.push(CONFIG_PERMISSION_NEW_PERMISSION)
    } catch (error) {
      console.error(error)
    }
  }

  const { groupOptions } = useSelectOptionsUtils()
  const isGroupOptionsEmpty = computed(() => groupOptions.value.length === 0)
</script>

<template>
  <Card dis-hover class="card">
    <template #title> Permissions </template>
    <BaseLayoutRowFormHeader grid-layout="1fr 1fr 50px">
      <span>Name</span>
      <span
        >Groups <BaseWarningIcon text="No group found in SiteCollection" v-if="isGroupOptionsEmpty"
      /></span>
      <span></span>
    </BaseLayoutRowFormHeader>
    <ConfigPermissionSetting v-model="permissionSetting.permissions" />
    <BaseButtonAdd text="Permission" @click="addHandler" class="addButton" />
    <BaseButtonSaveReset @reset="resetHandler" @save="submitHandler" class="saveResetButton" />
  </Card>
</template>

<style scoped lang="scss">
  .saveResetButton {
    margin-top: $margin * 6;
  }
</style>
