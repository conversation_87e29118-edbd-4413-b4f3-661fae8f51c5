import type { Workflow } from "~/types/workflow";

export const useIndicatorStore = defineStore("indicator", () =>
{
  const workflowStore = useWorkflowsStore();
  const formConfigStore = useFormConfigStore();
  function getNoInUseWorkflowIndicator()
  {
    const inUseWorkflow = workflowStore.moduleWorkflowByDateCreated.find(
      (workflow: Workflow) => workflow.inUse
    );
    if (inUseWorkflow)
    {
      return false;
    } else
    {
      return true;
    }
  }

  function getNoInUseFormConfigIndicator()
  {
    if (formConfigStore.formFormConfigByDateCreated.length === 0) return false;
    const inUseFormConfig = formConfigStore.formFormConfigByDateCreated.find(
      (workflow: Workflow) => workflow.inUse
    );
    if (inUseFormConfig)
    {
      return false;
    } else
    {
      return true;
    }
  }
  return {
    getNoInUseWorkflowIndicator,
    getNoInUseFormConfigIndicator
  };
});
