<script setup lang="ts">
const { searchOrCreateField } = useFormBuilderFieldSetting()
</script>

<template>
    <FormBuilderFieldSettingLabel>Search Or Create Settings</FormBuilderFieldSettingLabel>
    <template v-if="searchOrCreateField">
        <Form label-position="top">
            <FieldInputText v-model="searchOrCreateField.customlist" label="Custom List Name" />
            <FieldInputText v-model="searchOrCreateField.formConfigKey" label="Form Config Key" />
            <FieldInputText v-model="searchOrCreateField.itemText" label="Item Text" />
            <FieldInputText v-model="searchOrCreateField.itemValue" label="Item Value" />
            <FieldInputSwitch v-model="searchOrCreateField.createOnly" label="Create Only" />
            <FieldInputSwitch v-model="searchOrCreateField.disableAddButton" label="Disable Add Button" />
        </Form>
    </template>
</template>