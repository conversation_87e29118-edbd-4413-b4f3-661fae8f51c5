import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { setup, $fetch } from '@nuxt/test-utils'

// Mock Firebase Admin
vi.mock('firebase-admin/app', () => ({
  cert: vi.fn(),
  initializeApp: vi.fn(),
  getApps: vi.fn(() => []),
  getApp: vi.fn(),
}))

vi.mock('firebase-admin/auth', () => ({
  getAuth: vi.fn(() => ({
    verifyIdToken: vi.fn(),
    createCustomToken: vi.fn(),
  })),
}))

vi.mock('firebase-admin/database', () => ({
  getDatabase: vi.fn(() => ({
    ref: vi.fn(() => ({
      once: vi.fn(),
      set: vi.fn(),
      update: vi.fn(),
      push: vi.fn(),
      remove: vi.fn(),
    })),
  })),
}))

describe('Authentication API', () => {
  beforeEach(async () => {
    await setup({
      rootDir: process.cwd(),
    })
  })

  describe('POST /api/auth/login', () => {
    it('should login with valid credentials', async () => {
      const mockResponse = {
        idToken: 'mock-id-token',
        email: '<EMAIL>',
        refreshToken: 'mock-refresh-token',
        expiresIn: '3600',
        localId: 'mock-user-id',
      }

      // Mock the Firebase REST API call
      global.fetch = vi.fn().mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockResponse),
      })

      const response = await $fetch('/api/auth/login', {
        method: 'POST',
        body: {
          email: '<EMAIL>',
          password: 'password123',
        },
      })

      expect(response).toHaveProperty('user')
      expect(response).toHaveProperty('customToken')
      expect(response.user.email).toBe('<EMAIL>')
    })

    it('should return error for invalid credentials', async () => {
      global.fetch = vi.fn().mockRejectedValue(new Error('Invalid credentials'))

      try {
        await $fetch('/api/auth/login', {
          method: 'POST',
          body: {
            email: '<EMAIL>',
            password: 'wrongpassword',
          },
        })
      } catch (error) {
        expect(error).toBeDefined()
      }
    })

    it('should validate required fields', async () => {
      try {
        await $fetch('/api/auth/login', {
          method: 'POST',
          body: {
            email: '<EMAIL>',
            // Missing password
          },
        })
      } catch (error) {
        expect(error).toBeDefined()
      }
    })
  })

  describe('POST /api/auth/logout', () => {
    it('should logout successfully', async () => {
      const response = await $fetch('/api/auth/logout', {
        method: 'POST',
        headers: {
          cookie: 'session=mock-session-token',
        },
      })

      expect(response).toHaveProperty('ok', true)
    })
  })

  describe('GET /api/auth/me', () => {
    it('should return user info for valid session', async () => {
      // Mock Firebase Admin Auth
      const mockAuth = {
        verifyIdToken: vi.fn().mockResolvedValue({
          uid: 'mock-user-id',
          email: '<EMAIL>',
        }),
      }

      vi.mocked(require('firebase-admin/auth').getAuth).mockReturnValue(mockAuth)

      const response = await $fetch('/api/auth/me', {
        headers: {
          cookie: 'session=valid-session-token',
        },
      })

      expect(response).toHaveProperty('user')
      expect(response.user.email).toBe('<EMAIL>')
    })

    it('should return null for invalid session', async () => {
      const mockAuth = {
        verifyIdToken: vi.fn().mockRejectedValue(new Error('Invalid token')),
      }

      vi.mocked(require('firebase-admin/auth').getAuth).mockReturnValue(mockAuth)

      const response = await $fetch('/api/auth/me', {
        headers: {
          cookie: 'session=invalid-session-token',
        },
      })

      expect(response.user).toBeNull()
    })

    it('should return null when no session cookie', async () => {
      const response = await $fetch('/api/auth/me')
      expect(response.user).toBeNull()
    })
  })

  describe('POST /api/auth/user', () => {
    it('should create new user successfully', async () => {
      const mockAuth = {
        createUser: vi.fn().mockResolvedValue({
          uid: 'new-user-id',
          email: '<EMAIL>',
        }),
      }

      vi.mocked(require('firebase-admin/auth').getAuth).mockReturnValue(mockAuth)

      const response = await $fetch('/api/auth/user', {
        method: 'POST',
        body: {
          displayName: 'New User',
          email: '<EMAIL>',
          password: 'password123',
          phoneNumber: '+1234567890',
        },
      })

      expect(response).toHaveProperty('ok', true)
      expect(mockAuth.createUser).toHaveBeenCalledWith({
        displayName: 'New User',
        email: '<EMAIL>',
        password: 'password123',
        phoneNumber: '+1234567890',
      })
    })

    it('should validate required fields for user creation', async () => {
      try {
        await $fetch('/api/auth/user', {
          method: 'POST',
          body: {
            displayName: 'New User',
            // Missing email and password
          },
        })
      } catch (error) {
        expect(error).toBeDefined()
      }
    })
  })

  describe('POST /api/auth/update', () => {
    it('should update user successfully', async () => {
      const mockAuth = {
        updateUser: vi.fn().mockResolvedValue({
          uid: 'user-id',
          displayName: 'Updated Name',
        }),
      }

      vi.mocked(require('firebase-admin/auth').getAuth).mockReturnValue(mockAuth)

      const response = await $fetch('/api/auth/update', {
        method: 'POST',
        body: {
          uid: 'user-id',
          data: {
            displayName: 'Updated Name',
          },
        },
      })

      expect(response).toHaveProperty('ok', true)
      expect(mockAuth.updateUser).toHaveBeenCalledWith('user-id', {
        displayName: 'Updated Name',
      })
    })

    it('should validate uid parameter', async () => {
      try {
        await $fetch('/api/auth/update', {
          method: 'POST',
          body: {
            // Missing uid
            data: {
              displayName: 'Updated Name',
            },
          },
        })
      } catch (error) {
        expect(error).toBeDefined()
      }
    })
  })

  afterEach(() => {
    vi.clearAllMocks()
  })
})
