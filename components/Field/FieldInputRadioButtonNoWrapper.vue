<script setup lang="ts" generic="T">
defineProps({
    modelValue: {
        type: String
    },
    type: {
        type: String,
        default: 'button'
    },

})
const emit = defineEmits(['update:modelValue'])

function updateHandler(text: string) {
    emit('update:modelValue', text)
}
</script>

<template>
    <RadioGroup :modelValue="modelValue" @update:modelValue="updateHandler" :type="type">
        <slot />
    </RadioGroup>
</template>