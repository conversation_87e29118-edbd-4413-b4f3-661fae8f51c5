<script setup lang="ts">
defineProps({
    label: {
        type: String,
        default: ''
    },
    url: {
        type: String,
    },
    width: {
        type: Number,
        default: 100
    }
})
</script>

<template>
    <div class="fieldImage">
        <h6>{{ label }}</h6>
        <template v-if="url">
            <Image :src="url" :width="width" />
        </template>
        <template v-else>
            <div>
                Image
            </div>
        </template>
    </div>

</template>