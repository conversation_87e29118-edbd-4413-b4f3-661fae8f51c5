<script setup lang="ts">
const { imageField } = useFormBuilderFieldSetting()
</script>

<template>
  <FormBuilderFieldSettingLabel>Image Settings</FormBuilderFieldSettingLabel>
  <template v-if="imageField">
    <Form label-position="top">
      <FieldInputText label="Image Url" v-model="imageField.url" />
      <FieldInputNumber v-model="imageField.width" label="Width (px)" />
    </Form>
  </template>
</template>