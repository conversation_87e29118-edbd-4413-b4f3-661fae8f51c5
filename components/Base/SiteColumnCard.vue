<script lang="ts" setup>
  defineProps({
    isTableInput: {
      type: Boolean,
      default: false,
    },
  })
</script>

<template>
  <div class="card__sitecolumn">
    <div class="card__sitecolumn__header" :class="{ 'table-input': isTableInput }">
      <slot name="header" />
    </div>
    <div class="card__sitecolumn__body">
      <slot />
    </div>
  </div>
</template>

<style lang="scss" scoped>
  .card__sitecolumn {
    border: 1px solid $primary-gray;
  }

  .card__sitecolumn__header {
    height: 40px;
    background: $primary-blue;
    color: white;
    padding: $unit;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .table-input {
    color: $primary-text;
    background: $primary-charcoal-10;
  }

  .card__sitecolumn__body {
    padding: $unit;
    display: flex;
    flex-direction: column;
    gap: $unit;
  }
</style>
