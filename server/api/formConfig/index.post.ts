import { FormConfig } from '~/types/formConfig'
import { useAuthenticatedFirebase } from '~/server/utils/firebaseDatabase'

export default defineEventHandler(async (event) => {
  const { formConfigData } = await readBody(event)

  if (!formConfigData) {
    return {
      ok: false,
      errorMessage: 'No form config data provided',
    }
  }

  try {
    const { firebasePush } = useAuthenticatedFirebase(event)
    const newFormConfigKey = await firebasePush('/formConfigs', formConfigData)
    return {
      ok: true,
      data: newFormConfigKey,
      message: 'Form Config created successfully',
    }
  } catch (error) {
    return {
      ok: false,
      errorMessage: `Database Error: ${error}`,
    }
  }
})
