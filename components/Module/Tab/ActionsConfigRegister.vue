<script setup lang="ts">
  import type { PropType } from 'vue'
  import { ActionStatus, ActionType, type Action, type NewWorkflowAction } from '~/types/workflow'
  import type { Column, Stage } from '~/types/data'
  import { MODULE_NEW_ACTION_CONFIG } from '~/constants/default-values'
  const props = defineProps({
    modelValue: {
      type: Object as PropType<NewWorkflowAction>,
      required: true,
    },
    stageKey: {
      type: String,
      required: true,
    },
  })
  const emit = defineEmits(['update:modelValue', 'submit'])
  const stageStore = useStagesStore()
  const stage = computed<Stage>(() => stageStore.getStageByFirebaseKey(props.stageKey))
  const activeAction = ref<Action>({} as Action)
  const activeIndex = ref(0)
  const NEW_ACTION: Action = {
    ...MODULE_NEW_ACTION_CONFIG,
    Stage: props.stageKey,
  }

  const columns: Column[] = [
    { title: 'Description', key: 'ActionDescription', slot: 'description' },
    { title: 'Status', key: 'Status', slot: 'status' },
    { title: 'Type', key: 'Type', slot: 'type' },
    { title: 'Assigned To', key: 'AssignedToId', slot: 'assignedTo' },
    {
      title: 'Action',
      slot: 'action',
      width: 150,
    },
  ]

  const isActionEmpty = computed(() => {
    if (!props.modelValue.actions) {
      return true
    }
    return props.modelValue.actions.length === 0
  })

  function clickHandlerAdd() {
    const newModelValue = useCloneDeep(props.modelValue)
    newModelValue.actions.push(NEW_ACTION)
    emit('update:modelValue', newModelValue)
    activeAction.value = useCloneDeep(NEW_ACTION)
    activeIndex.value = props.modelValue.actions.length
  }

  function clickHandlerDelete(index: number) {
    const newModelValue = useCloneDeep(props.modelValue)
    newModelValue.actions.splice(index, 1)
    emit('update:modelValue', newModelValue)
  }

  const fieldStore = useFieldStore()

  const fieldStatusOptions = Object.values(ActionStatus).map((value) => value)
  const fieldTypeOptions = Object.values(ActionType).map((value) => value)
  const assignedToOptionGroup = computed(() => {
    return fieldStore
      .getActiveSiteCollectionGroups()
      .map((group) => ({ label: group, value: group }))
  })
  const assignedToOptionFieldName = computed(() => {
    const option = fieldStore
      .getPeoplePickerFieldNameOptions()
      .map((field) => ({ label: field, value: field }))
    option.unshift({ label: 'Author', value: 'Author' })
    return option
  })
  const assignedToOptionPerson = computed(() => {
    return fieldStore.getPeoplePickerOptions()
  })
  const assignedToOptionList = computed(() => {
    return fieldStore.getListOptions()
  })
  const assignedToOptionCL = computed(() => {
    return fieldStore.getCustomListOptions()
  })
</script>

<template>
  <Card dis-hover class="action--card">
    <template #title>
      <div class="card--header">
        <b>{{ stage.name }}</b>
        <div class="buttons">
          <BaseButtonAdd @click="clickHandlerAdd" appearance="default" text="Action" />
        </div>
      </div>
    </template>
    <div class="form__wrapper">
      <template v-if="isActionEmpty">
        {{ 'No actions found' }}
      </template>
      <Table :data="modelValue.actions" :columns="columns" row-key="key" size="small">
        <template #description="{ row, index }">
          <Input
            v-model="modelValue.actions[index].ActionDescription"
            :placeholder="'Action Description'" />
        </template>
        <template #status="{ row, index }">
          <Select v-model="modelValue.actions[index].Status" transfer>
            <Option v-for="option in fieldStatusOptions" :key="option" :value="option">
              {{ option }}
            </Option>
          </Select>
        </template>
        <template #type="{ row, index }">
          <Select v-model="modelValue.actions[index].Type" transfer>
            <Option v-for="option in fieldTypeOptions" :key="option" :value="option">
              {{ option }}
            </Option>
          </Select>
        </template>
        <template #assignedTo="{ row, index }">
          <template v-if="row.Type === ActionType.GROUP">
            <Select
              v-model="modelValue.actions[index].AssignedToId"
              :max-tag-count="1"
              multiple
              transfer>
              <Option
                v-for="(option, index) in assignedToOptionGroup"
                :value="option.value"
                :key="index">
                {{ option.label }}
              </Option>
            </Select>
          </template>
          <template v-if="row.Type === ActionType.FIELDNAME">
            <Select v-model="modelValue.actions[index].AssignedToId" :max-tag-count="1" transfer>
              <Option
                v-for="(option, index) in assignedToOptionFieldName"
                :value="option.value"
                :key="index">
                {{ option.label }}
              </Option>
            </Select>
          </template>
          <template v-if="row.Type === ActionType.PERSON">
            <Select
              v-model="modelValue.actions[index].AssignedToId"
              :max-tag-count="1"
              multiple
              transfer>
              <Option
                v-for="(option, index) in assignedToOptionPerson"
                :value="option.value"
                :key="index">
                {{ option.label }}
              </Option>
            </Select>
          </template>
          <template v-if="row.Type === ActionType.LIST">
            <Select v-model="modelValue.actions[index].AssignedToId" :max-tag-count="1">
              <Option
                v-for="(option, index) in assignedToOptionList"
                :value="option.value"
                :key="index">
                {{ option.label }}
              </Option>
            </Select>
          </template>
          <template v-if="row.Type === ActionType.CL">
            <Select v-model="modelValue.actions[index].AssignedToId" :max-tag-count="1" transfer>
              <Option
                v-for="(option, index) in assignedToOptionCL"
                :value="option.value"
                :key="index">
                {{ option.label }}
              </Option>
            </Select>
          </template>
        </template>
        <template #action="{ row, index }">
          <div class="action--button">
            <BaseButtonDelete @click="clickHandlerDelete(index)" />
          </div>
        </template>
      </Table>
    </div>
  </Card>
</template>

<style lang="scss" scoped>
  .card--header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .buttons {
      display: flex;
      gap: $unit;
    }
  }

  .description {
    padding-left: $padding;
    padding-right: $padding;
  }

  .action--button {
    display: flex;
    gap: $unit;
  }

  .action--card {
    padding-bottom: $margin;
  }
</style>
