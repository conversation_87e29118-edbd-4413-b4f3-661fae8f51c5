// GET ALL Cores
import { useAuthenticatedFirebase } from '~/server/utils/firebaseDatabase'

export default defineEventHandler(async (event) => {
  try {
    const { firebaseFetch } = useAuthenticatedFirebase(event)
    const cores = await firebaseFetch('/cores')
    return {
      ok: true,
      data: cores,
      message: 'Cores fetched successfully',
    }
  } catch (error) {
    return {
      ok: false,
      errorMessage: error,
    }
  }
})
