import { CoreListTemplate } from '~/types/core'
import { SiteCollectionEnvironment, SiteCollectionPermissionsName } from '~/types/siteCollection'

export const useSelectOptionsUtils = () => {
  const coresStore = useCoresStore()
  const { cores } = storeToRefs(coresStore)

  const siteCollectionStore = useSiteCollectionsStore()
  const { activeSiteCollection } = storeToRefs(siteCollectionStore)

  const siteCollectionEnvironment = [
    SiteCollectionEnvironment.Dev,
    SiteCollectionEnvironment.Staging,
    SiteCollectionEnvironment.Test,
    SiteCollectionEnvironment.Live,
  ]

  const siteCollectionPermissionName = [
    SiteCollectionPermissionsName.showConfig,
    SiteCollectionPermissionsName.showChangeType,
    SiteCollectionPermissionsName.showAssessmentTemplate,
  ]

  const coreTemplates = [
    CoreListTemplate.GENERIC_LIST,
    CoreListTemplate.DOCUMENT_LIBRARY,
    CoreListTemplate.RECORD_LIBRARY,
  ]

  // SITE COLLECTION

  const environmentOptions = siteCollectionEnvironment.map((env) => ({
    label: env,
    value: env,
  }))

  const permissionOptions = siteCollectionPermissionName.map((p) => ({
    label: p,
    value: p,
  }))

  const coreTemplateOptions = coreTemplates.map((t) => ({
    label: t,
    value: t,
  }))

  const coreOptions = computed(() =>
    cores.value.map((core) => ({
      label: core.name,
      value: core.key,
    })),
  )

  const groupOptions = computed(() => {
    if (!activeSiteCollection.value.groups) {
      return []
    }
    return (
      activeSiteCollection.value.groups?.map((group) => ({
        label: group,
        value: group,
      })) ?? []
    )
  })

  return {
    environmentOptions,
    permissionOptions,
    groupOptions,
    coreOptions,
    coreTemplateOptions,
  }
}
