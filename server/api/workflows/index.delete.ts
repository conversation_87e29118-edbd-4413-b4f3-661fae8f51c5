// DELETE Workflow
// body { workflowKey: string }

import { useAuthenticatedFirebase } from '~/server/utils/firebaseDatabase'

export default defineEventHandler(async (event) => {
  const { workflowKey } = await readBody(event)

  if (!workflowKey) {
    return {
      ok: false,
      errorMessage: `workflowKey is required`,
    }
  }
  try {
    const { firebaseRemove } = useAuthenticatedFirebase(event)
    await firebaseRemove(`/workflows/${workflowKey}`)
    return {
      ok: true,
      message: `Sucessfully delete workflow with key ${workflowKey}`,
    }
  } catch (error) {
    return {
      ok: false,
      errorMessage: error,
    }
  }
})
