<script setup lang="ts">
const siteCollectionStore = useSiteCollectionsStore()
const { siteCollectionsSortedByEnvironment } = storeToRefs(siteCollectionStore)
</script>

<template>
  <div class="siderMenuSiteCollections">
    <template v-for="site in siteCollectionsSortedByEnvironment">
      <AppSiderMenuSiteCollectionAccordion :site="site" />
    </template>
  </div>
</template>

<style lang="scss" scoped>
.siderMenuSiteCollections {
  display: flex;
  flex-direction: column;
}
</style>
