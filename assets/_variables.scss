//Spacing
// @import url('Segoe UI.ttf');
$mini-unit: 2px;
$sub-unit: 4px;
$unit: 8px;
$padding: 12px;
$margin: 2 * $unit;
$margin-md: 3 * $unit;
// Margin for bottom of paragraphs, logical sections, and between components
$p-margin: 5 * $unit;
$section-margin: 100px;

// Width
$width-md: 768px;
$width-lg: 992px;
$width-xl: 1200px;
$width-xxl: 1400px;

// Fonts
$primary-font-stack: 'Cardo', sans-serif;
$secondary-font-stack: 'Quicksand', sans-serif;
$fluentui-font-stack: 'Segoe UI', sans-serif;

// Font sizes
$loading-font-size: 2 * $unit;
$font-content: 14px;
$font-title: 20px;

// Colors
$primary-white: #ffffff;
$primary-gray: #d8d8d8;
$primary-light-gray: #f5f5f5;
$primary-lightblue: #2d8cf0;
$primary-black: #000000;
$primary-blue: #0f6cbd;
$primary-blue-hover: #006cbe;
$primary-blue-pressed: #1683d8;
$primary-text: #515a6e;
$loading-blue-text: #2d8cf0;
$light-green: #f2fbf2;
$light-green-hover: #ccf1ca;

$delete-foreground: #751d1f;
$delete-background: #f1bbbc;

$active-indicator: #ccf1ca;

// Layout theme
$content-bg: #fafafa;
$sider-bg: #ffffff;

//Sider Theme
$sider-active-color: #0f6cbd;
$sider-hover-color: #f5f5f5;
$sider-onpress-color: #e0e0e0;
$navigation-border: rgb(240, 240, 240);

// Sider Font Color
$sider-submenu-color: #242424;

// Sider Submenu font size
$sider-menu-collapsed-size: 20px;
$sider-menu-font-size: 16px;
$sider-submenu-font-size: 14px;

// Table Theme
$table-font-color: #242424;
$table-row-hover: #f0f0f0;
$table-row-pressed: #e6e6e6;
$table-background: #fafafa;

// Fluent-ui Theme
$fluent-font-color: #242424;
$fluent-font-color-light: #424242;

$fluent-foreground1: #fafafa;
$foreground1-hover: #f0f0f0;
$foreground1-pressed: #dbdbdb;
$foreground1-selected: #e6e6e6;

$fluent-foreground2: #f5f5f5;
$foreground2-hover: #ebebeb;
$foreground2-pressed: #d6d6d6;
$foreground2-selected: #e0e0e0;

$fluent-foreground3: #616161;
$foreground3-hover: #424242;
$foreground3-pressed: #424242;

$white-hover: #f5f5f5;
$white-pressed: #e0e0e0;
$white-selected: #ebebeb;

$stroke-color: #d1d1d1;
$stroke-hover: #c7c7c7;
$stroke-pressed: #b3b3b3;
$stroke-selected: #bdbdbd;

$primary-hover: #115ea3;
$primary-pressed: #0e4775;
$primary-selected: #0f548c;

// Form Builder layout
$form-builder-layout: 200px 1fr 400px;

//Primary Colors
$primary-charcoal: #282828;
$primary-charcoal-10: #f7f7f7;
$primary-charcoal-15: #e4e4e4;
$primary-charcoal-20: #cecece;
$primary-charcoal-50: #8e9295;
$primary-charcoal-80: #363533;
$primary-charcoal-120: #070e13;
$primary-orange: #ff9f00;
$primary-background: #f8fbff;
$primary-link: rgb(0, 174, 255);
$form-field-border: #dcdee2;
$form-field-color: #808695;
