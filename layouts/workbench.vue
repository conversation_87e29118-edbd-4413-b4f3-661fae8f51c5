<script setup lang="ts">

const { logout } = useAuth();

async function handleLogout()
{
    useUiStore().setLoading(true);
    await logout();
}

watch(useAuthUser(), (newUser) =>
{
    if (!newUser) navigateTo("/");
});

const user = useAuthUser()
const userAvatarText = user.value?.email?.slice(0, 2).toUpperCase();
</script>

<template>
    <Layout>
        <Header class="layout__header">
            <Dropdown trigger="click">
                <a>
                    <Avatar>{{ userAvatarText ?? "U" }}</Avatar>
                </a>
                <template #list>
                    <DropdownMenu>
                        <DropdownItem name="signout" @click="handleLogout">
                            <Icon type="ios-log-out" size="16" />
                            <span style="margin-left: 8px">Log Out</span>
                        </DropdownItem>
                    </DropdownMenu>
                </template>
            </Dropdown>
        </Header>
        <Layout class="layout__main">
            <slot />
        </Layout>
    </Layout>
</template>

<style lang="scss" scoped>
.layout {
    position: relative;
    height: 100vh;
    overflow: hidden;

    &__main {
        height: calc(100vh - 50px);
    }

    &__header {
        height: 50px;
        padding: 0 32px;
        display: flex;
        justify-content: flex-end;
        align-items: center;
    }
}
</style>