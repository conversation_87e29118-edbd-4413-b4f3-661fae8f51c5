<script setup lang="ts">
import { CoreTabNames } from '~/constants/tabs';
import type { CoreListSetting } from '~/types/core';


const uiStore = useUiStore()

const coreStore = useCoresStore()
const { activeCore } = storeToRefs(coreStore)
const coreListsSetting = ref({} as CoreListSetting)

const router = useRouter()
const route = useRoute()

onMounted(() =>
{
    router.replace({
        query: {
            ...route.query,
            tab: CoreTabNames.LISTS
        }
    })
    coreListsSetting.value = useCloneDeep(activeCore.value)
})

watch(activeCore, (newVal) =>
{
    if (newVal)
    {
        initialiseCoreListSetting()
    }
})

function initialiseCoreListSetting()
{
    coreListsSetting.value = useCloneDeep(activeCore.value)
}

async function clickHandlerSave()
{
    uiStore.setLoading(true, 'Updating Core Lists')
    await coreStore.updateCore(activeCore.value.key, coreListsSetting.value)
    uiStore.setLoading(false)
}

function clickHandlerReset()
{
    initialiseCoreListSetting()
}

</script>

<template>
    <Card dis-hover>
        <BaseLayoutRowFormHeader grid-layout="25px 1fr 1fr 1fr 50px">
            <span></span>
            <span>Display Name</span>
            <span>Internal Name</span>
            <span>Template</span>
            <span></span>
        </BaseLayoutRowFormHeader>
        <CoreTabListRegister v-model="coreListsSetting.lists" />
        <BaseButtonSaveReset @save="clickHandlerSave" @reset="clickHandlerReset" />
    </Card>
</template>