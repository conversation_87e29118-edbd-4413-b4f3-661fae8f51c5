<script setup lang="ts">
  const route = useRoute()
  const tenant = route.params.tenant as string
  const siteCollectionKey = route.params.siteCollectionKey as string
  const activeTenant = computed(() => {
    return tenantStore.getTenantFromName(tenant)
  })
  const activeSite = computed(() => {
    return siteCollectionsStore.getSiteByKey(siteCollectionKey)
  })
  const siteCollectionsStore = useSiteCollectionsStore()
  const sharepointAPIStore = useSharepointAPIStore()
  const tenantStore = useTenantsStore()
  const uiStore = useUiStore()

  watch(
    [activeTenant, activeSite],
    async ([activeTenant, activeSite]) => {
      siteCollectionsStore.setTenant(tenant)
      siteCollectionsStore.setActiveSite(siteCollectionKey)
      if (activeTenant && activeSite) {
        sharepointAPIStore.resetSharepointAPIState()
        sharepointAPIStore.setSharepointAPIState(
          activeTenant.tenant,
          activeTenant.tenantId,
          activeSite.url,
        )
        uiStore.setLoading(true, `Fetching Sharepoint API...`)
        await Promise.all([
          sharepointAPIStore.fetchLists(),
          sharepointAPIStore.fetchSiteGroups(),
          sharepointAPIStore.fetchUsers(),
        ])
        uiStore.setLoading(false)
      }
    },
    { immediate: true },
  )
</script>
<template>
  <AppBreadCrumbs />
  <SiteCollectionTabs />
</template>
