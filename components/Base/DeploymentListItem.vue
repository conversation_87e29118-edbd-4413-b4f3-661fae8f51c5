<template>
    <div class="deploymentItem">
        <slot />
    </div>
</template>

<style lang="scss">
.deploymentItem {
    border: 1px solid rgb(234, 234, 234);
    padding: 2px;
    display: grid;
    grid-template-columns: 1fr 20px;
    height: 1.5rem;
    align-items: center;

    .icon--check {
        width: 1rem;
        color: green;
    }

    .icon--add {
        cursor: pointer;
        width: 1rem;
        color: red;
    }

    .icon--cross {
        cursor: pointer;
        width: 1rem;
    }
}
</style>