<script setup lang="ts">
const formBuilderStore = useFormBuilderStore()
const { enableMultipleSubmit } = storeToRefs(formBuilderStore)
</script>

<template>
    <FormBuilderFieldSettingLayout>
        <template #field>
            <FormBuilderFieldSettingGeneralSetting />
            <FormBuilderFieldSettingAdvanced />
            <FormBuilderFieldSettingMultipleSubmit v-if="enableMultipleSubmit" />
        </template>
    </FormBuilderFieldSettingLayout>
</template>
