// UPDATE Core Site Columns
// body = {coreKey: string, id: number , siteColumns:CoreSiteColumn[]}

import { CoreSiteColumn } from '~/types/core'
import { useAuthenticatedFirebase } from '~/server/utils/firebaseDatabase'

export default defineEventHandler(async (event) => {
  const { coreKey, id, siteColumns } = await readBody(event)

  if (!coreKey || !siteColumns || !id) {
    return {
      ok: false,
      errorMessage: 'Missing coreKey, id, siteColumns',
    }
  }

  try {
    const { firebaseSet } = useAuthenticatedFirebase(event)
    const data = await firebaseSet(`/cores/${coreKey}/lists/${id}/SiteColumns`, siteColumns)
    return {
      ok: true,
      data,
      message: 'Core Site Columns updated successfully',
    }
  } catch (error) {
    return {
      ok: false,
      errorMessage: 'Error updating site columns.',
    }
  }
})
