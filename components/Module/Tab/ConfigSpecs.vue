<script setup lang="ts">
import { ModuleTabNames } from '~/constants/tabs'


type JSON_DATA = {
    Stage: string,
    Field: string,
    "Field Name": string,
    Width: string,
    "Field Type": string,
    Validation: string,
    "Read Only": string,
    "Appear in Review Only": string,
    "In Review - Read Only": string,
    "Conditions to appear": string,
    Conditions: string,
}


const stagesStore = useStagesStore()
const { workflowStages } = storeToRefs(stagesStore)
const moduleStore = useModulesStore()
const { activeModule } = storeToRefs(moduleStore)
const twoDArray = ref<any[][]>([])
const router = useRouter()
const route = useRoute()

watch(workflowStages, (newValue) =>
{
    if (newValue)
        initialise2dArray()
})

onMounted(() =>
{
    router.replace({
        query: {
            ...route.query,
            tab: ModuleTabNames.CONFIGSPECS
        }
    })
    initialise2dArray()
})

function initialise2dArray()
{
    twoDArray.value = [];
    const header = [
        "Stage",
        "Field",
        "Field Name",
        "Width",
        "Field Type",
        "Validation",
        "Read Only",
        "Appear in Review Only",
        "In Review - Read Only",
        "Conditions to appear",
        "Conditions"
    ];
    twoDArray.value.push(header);
    workflowStages.value.forEach((stage) =>
    {
        if (!stage.form) return;
        stage.form.forEach((field) =>
        {
            let fieldArray: string[] = [];
            const conditionString: string[] = [];
            if (field.conditions)
            {
                field.conditions.forEach((condition) =>
                {
                    conditionString.push(
                        `Show ${condition.fieldName} when value is '${condition.value}'`
                    );
                });
            }
            fieldArray.push(stage.name);
            fieldArray.push(field.label);
            fieldArray.push(field.fieldName);
            fieldArray.push(field.width ? field.width.toString() : "100");
            fieldArray.push(field.type);
            fieldArray.push(field.noValidation ? "Yes" : "No");
            fieldArray.push(field.readOnly ? "Yes" : "No");
            fieldArray.push(field.review ? "Yes" : "No");
            fieldArray.push(field.reviewReadOnly ? "Yes" : "No");
            fieldArray.push(field.conditions ? "Yes" : "No");
            if (conditionString.length > 0)
            {
                fieldArray.push(conditionString.toString());
            } else
            {
                fieldArray.push("");
            }
            twoDArray.value.push(fieldArray);
        })
    })
}

function arrayToCsv(data: any[][])
{
    return data.map(row =>
        row
            .map(String)  // convert every value to String
            .map(v => v.replaceAll('"', '""'))  // escape double quotes
            .map(v => `"${v}"`)  // quote it
            .join(',')  // comma-separated
    ).join('\r\n');  // rows starting on new lines
}

function downloadBlob(content: any, filename: string, contentType: string)
{
    // Create a blob
    var blob = new Blob([content], { type: contentType });
    var url = URL.createObjectURL(blob);

    // Create a link to download it
    var pom = document.createElement('a');
    pom.href = url;
    pom.setAttribute('download', filename);
    pom.click();
}

function clickHandlerDownload()
{
    const content = arrayToCsv(twoDArray.value);
    downloadBlob(content, `${activeModule.value.name}-formconfig.csv`, 'text/csv');
}
</script>

<template>
    <BaseButton @click="clickHandlerDownload">Download Form Config</BaseButton>
</template>