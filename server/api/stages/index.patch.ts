// UPDATE Stage
// body { stageKey: string, stageData: Stage }

import { Stage } from '~/types/data'
import { useAuthenticatedFirebase } from '~/server/utils/firebaseDatabase'

export default defineEventHandler(async (event) => {
  const { stageKey, stageData } = await readBody(event)

  if (!stageKey || !stageData) {
    return {
      ok: false,
      errorMessage: 'stageKey and stageData are required',
    }
  }

  try {
    const { firebaseUpdate } = useAuthenticatedFirebase(event)
    await firebaseUpdate(`/stages/${stageKey}`, stageData)
    return {
      ok: true,
      message: `Stage with key: ${stageKey} updated successfully`,
    }
  } catch (error) {
    return {
      ok: false,
      errorMessage: error,
    }
  }
})
