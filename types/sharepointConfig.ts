export type SharepointSetting = SharepointListSetting &
  SharepointCustomListSetting;

export type SharepointListSetting = {
  lists: SharepointList[];
};

export type SharepointList = {
  DisplayName: string;
  InternalName: string;
};

export type SharepointCustomListSetting = {
  customlists: SharepointCustomList[];
};

export enum CustomListType {
  CONFIG = "Config",
  EXTERNAL = "External",
  TABLEINPUT = "TableInput",
}

export type SharepointCustomList =
  | CustomListConfig
  | CustomListExternal
  | CustomListTableInput;

export type CustomList = {
  fieldName: string;
  listname: string;
  type: CustomListType;
};

export interface CustomListConfig extends CustomList {
  type: CustomListType.CONFIG;
}

export interface CustomListExternal extends CustomList {
  type: CustomListType.EXTERNAL;
}

export interface CustomListTableInput extends CustomList {
  type: CustomListType.TABLEINPUT;
}

export type SiteColumnConfig = {
  __metadata: { type: string };
  Title: string;
  FieldTypeKind: number;
  StaticName: string;
  InternalName: string;
  Required: boolean;
  AllowMultipleValues?: boolean;
  FieldTypeProperties?: {
    SelectionMode: number;
  };
};

export type SiteColumnField = {
  Id: any;
  Title: string;
  InternalName: string;
  Type: string;
};
