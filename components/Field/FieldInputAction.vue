<script setup lang="ts">
import type { CustomOptionData } from '~/types/form';

const props = defineProps({
    options: {
        type: Array<CustomOptionData>,
        required: true,
        default: [] as Array<CustomOptionData>
    },
    label: {
        type: String,
        required: false,
        default: ''
    }
})

const tableInputColumns: { title: string, key: string }[] = []
onBeforeMount(() => {
    props.options.forEach((column) => {
        tableInputColumns.push({
            title: column.name,
            key: column.fieldName,
        })
    })
})

function rowClassName() {
    return 'fluent-ui-row'
}
</script>

<template>
    <div class="tableInput">
        <span class="tableHeading">
            <BaseButton appearance="accent" shape="circular">
                <Icon type="ios-grid" />
            </BaseButton>{{ props.label }}
        </span>
        <div class="table">
            <Table :columns="tableInputColumns" :row-class-name="rowClassName" />

            <!-- Todo: Implement Add Item and generate data based on Option.type -->
            <BaseButtonAdd text="item" class="addButton" />
        </div>
    </div>
</template>

<style scoped lang="scss">
.tableInput {
    display: inline-block;
    gap: $margin;
    margin-bottom: $margin
}

.tableHeading {
    display: flex;
    gap: $padding;
    align-items: center;
    font-size: medium;
    color: $fluent-font-color;
}

.addButton {
    margin-top: $margin;
}
</style>