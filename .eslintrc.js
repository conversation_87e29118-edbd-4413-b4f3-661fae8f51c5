module.exports = {
  root: true,
  extends: [
    '@nuxt/eslint-config',
    'plugin:vue/vue3-recommended',
    '@vue/typescript/recommended'
  ],
  parser: 'vue-eslint-parser',
  parserOptions: {
    parser: '@typescript-eslint/parser',
    ecmaVersion: 2022,
    sourceType: 'module'
  },
  plugins: [
    '@typescript-eslint',
    'vue'
  ],
  rules: {
    // Vue specific rules
    'vue/multi-word-component-names': 'off',
    'vue/no-multiple-template-root': 'off',
    'vue/component-definition-name-casing': ['error', 'PascalCase'],
    'vue/component-name-in-template-casing': ['error', 'PascalCase'],
    'vue/no-unused-vars': 'error',
    'vue/no-unused-components': 'warn',
    'vue/require-default-prop': 'off',
    'vue/require-prop-types': 'error',
    
    // TypeScript specific rules
    '@typescript-eslint/no-unused-vars': 'error',
    '@typescript-eslint/no-explicit-any': 'warn',
    '@typescript-eslint/explicit-function-return-type': 'off',
    '@typescript-eslint/explicit-module-boundary-types': 'off',
    '@typescript-eslint/no-non-null-assertion': 'warn',
    '@typescript-eslint/prefer-const': 'error',
    
    // General JavaScript/TypeScript rules
    'no-console': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    'no-debugger': process.env.NODE_ENV === 'production' ? 'error' : 'off',
    'prefer-const': 'error',
    'no-var': 'error',
    'object-shorthand': 'error',
    'prefer-template': 'error',
    
    // Import rules
    'import/order': ['error', {
      'groups': [
        'builtin',
        'external',
        'internal',
        'parent',
        'sibling',
        'index'
      ],
      'newlines-between': 'never'
    }],
    
    // Accessibility rules
    'vue/require-v-for-key': 'error',
    'vue/no-use-v-if-with-v-for': 'error',
  },
  overrides: [
    {
      files: ['tests/**/*', '**/*.test.*', '**/*.spec.*'],
      rules: {
        '@typescript-eslint/no-explicit-any': 'off',
        'no-console': 'off'
      }
    },
    {
      files: ['server/**/*'],
      rules: {
        'no-console': 'off'
      }
    },
    {
      files: ['nuxt.config.*', '*.config.*'],
      rules: {
        '@typescript-eslint/no-var-requires': 'off'
      }
    }
  ],
  env: {
    browser: true,
    node: true,
    es2022: true
  },
  globals: {
    $fetch: 'readonly',
    defineNuxtConfig: 'readonly',
    definePageMeta: 'readonly',
    defineNuxtPlugin: 'readonly',
    defineEventHandler: 'readonly',
    readBody: 'readonly',
    getCookie: 'readonly',
    setCookie: 'readonly',
    getQuery: 'readonly',
    useRuntimeConfig: 'readonly',
    navigateTo: 'readonly',
    useRoute: 'readonly',
    useRouter: 'readonly',
    useHead: 'readonly',
    useCookie: 'readonly',
    useRequestHeaders: 'readonly',
    useNuxtApp: 'readonly',
    ref: 'readonly',
    reactive: 'readonly',
    computed: 'readonly',
    watch: 'readonly',
    watchEffect: 'readonly',
    onMounted: 'readonly',
    onUnmounted: 'readonly',
    nextTick: 'readonly',
    defineStore: 'readonly',
    storeToRefs: 'readonly'
  }
}
