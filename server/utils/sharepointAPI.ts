import { getAccessToken } from './accessToken'

export const useSharepointAPIUtils = () => {
  async function getAccessTokenFromTenant(tenantUrl: string, tenantId: string) {
    const accessToken = await getAccessToken(tenantUrl, tenantId)
    if (!accessToken) {
      throw new Error('Failed to retrieve access token.')
    }
    return accessToken
  }

  async function getFetchHeaders(tenantUrl: string, tenantId: string) {
    const accessToken = await getAccessTokenFromTenant(tenantUrl, tenantId)
    const headers = {
      Accept: 'application/json;odata=verbose',
      Authorization: `Bearer ${accessToken}`,
    }
    return headers
  }

  async function fetchSharepointAPIWithTenant(url: string, tenantUrl: string, tenantId: string) {
    const headers = await getFetchHeaders(tenantUrl, tenantId)
    const response = await fetch(url, { headers })
    if (!response.ok) {
      const errorText = await response.text()
      throw new Error(`Error fetching data from ${url}: ${errorText}`)
    }
    return response
  }

  return {
    fetchSharepointAPIWithTenant,
  }
}
