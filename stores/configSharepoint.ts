import { FormFieldType, type FormFieldData, type TableInputFieldType } from '~/types/form'
import type {
  SharepointCustomListSetting,
  SharepointListSetting,
  SharepointSetting,
} from '~/types/sharepointConfig'

export const enum SharepointConfigFor {
  WORKFLOW = 'workflow',
  FORM = 'form',
}

export type SharepointConfigField = FormFieldData & {
  stageName?: string
}

export const useConfigSharepointStore = defineStore('sharepointConfig', () => {
  const workflowStore = useWorkflowsStore()
  const { activeWorkflow } = storeToRefs(workflowStore)

  const stageStore = useStagesStore()
  const { workflowStages } = storeToRefs(stageStore)

  const formConfigStore = useFormConfigStore()
  const { activeFormConfig } = storeToRefs(formConfigStore)

  const activeSharepointConfig = ref({} as SharepointSetting)
  const isSetFor = ref<SharepointConfigFor>()
  const sharepointConfigFormFields = ref<SharepointConfigField[]>([])
  const activeTableInputFields = ref<TableInputFieldType[]>([] as TableInputFieldType[])

  const clearSharepointConfigState = () => {
    activeTableInputFields.value = []
    activeSharepointConfig.value = {} as SharepointSetting
    isSetFor.value = undefined
    sharepointConfigFormFields.value = []
  }

  const initialiseSharepointConfigForForm = () => {
    clearSharepointConfigState()
    activeSharepointConfig.value = useCloneDeep(activeFormConfig.value)
    initialiseTableInput(activeFormConfig.value.form ? activeFormConfig.value.form : [])
    isSetFor.value = SharepointConfigFor.FORM
    initialiseFormFormFields()
  }

  const initialiseSharepointConfigForWorkflow = () => {
    clearSharepointConfigState()
    isSetFor.value = SharepointConfigFor.WORKFLOW
    activeSharepointConfig.value = useCloneDeep(activeWorkflow.value)
    const workflowFields = [] as FormFieldData[]
    for (const stage of workflowStages.value) {
      if (stage.form) {
        initialiseTableInput(stage.form)
        for (const field of stage.form) {
          const fieldData = useCloneDeep(field) as FormFieldData
          workflowFields.push(fieldData)
        }
      }
    }
    initialiseWorkflowFormFields()
  }

  const initialiseWorkflowFormFields = () => {
    if (workflowStages.value.length > 0) {
      workflowStages.value.forEach((stage) => {
        if (stage.form) {
          stage.form.forEach((formField) => {
            sharepointConfigFormFields.value.push({
              ...formField,
              stageName: stage.name,
            })
          })
        }
      })
    }
  }

  const initialiseFormFormFields = () => {
    if (activeFormConfig.value) {
      if (activeFormConfig.value.form) {
        sharepointConfigFormFields.value = useCloneDeep(activeFormConfig.value.form)
      }
    }
  }

  const initialiseTableInput = (fields: FormFieldData[]) => {
    for (const field of fields) {
      if (isTableInputVariant(field.type)) {
        activeTableInputFields.value.push(field as TableInputFieldType)
      }
    }
  }

  function checkIfListIsTableInput(internalName: string): boolean {
    return activeTableInputFields.value.some((field) => field.fieldName === internalName)
  }

  const isTableInputVariant = (fieldType: FormFieldType) => {
    return (
      fieldType === FormFieldType.TABLEINPUT ||
      fieldType === FormFieldType.ACTION ||
      fieldType === FormFieldType.FINALRPN ||
      fieldType === FormFieldType.BASICRISK ||
      fieldType === FormFieldType.RPN ||
      fieldType === FormFieldType.FMEA
    )
  }

  async function saveHandler(
    sharepointConfigData: SharepointCustomListSetting | SharepointListSetting,
  ) {
    if (isSetFor.value === SharepointConfigFor.WORKFLOW) {
      await workflowStore.updateWorkflow(activeWorkflow.value.key, sharepointConfigData)
    } else if (isSetFor.value === SharepointConfigFor.FORM) {
      await formConfigStore.updateFormConfig(activeFormConfig.value.key, sharepointConfigData)
    }
  }

  async function tableInputSaveListName(fieldName: string, selectedListName: string) {
    if (isSetFor.value === SharepointConfigFor.WORKFLOW) {
      await saveFieldInWorkflow(fieldName, {
        listname: selectedListName,
      } as FormFieldData)
    }
  }

  async function saveFieldInWorkflow(fieldName: string, fieldData: FormFieldData) {
    for (const stage of workflowStages.value) {
      if (stage.form) {
        for (const { field, index } of stage.form.map((field, index) => ({
          index,
          field,
        }))) {
          if (field.fieldName === fieldName) {
            const stageData = useCloneDeep(stage)
            stageData.form[index] = {
              ...field,
              ...fieldData,
            }
            await stageStore.updateStage(stage.firebaseKey, stageData)
          }
        }
      }
    }
  }

  return {
    initialiseSharepointConfigForForm,
    initialiseSharepointConfigForWorkflow,
    activeSharepointConfig,
    activeTableInputFields,
    sharepointConfigFormFields,
    saveHandler,
    tableInputSaveListName,
    checkIfListIsTableInput,
  }
})
