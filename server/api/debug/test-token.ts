import { getAccessToken } from '../../utils/accessToken'

export default defineEventHandler(async (event) => {
  try {
    const query = getQuery(event)
    const { tenantUrl, tenantId } = query

    if (!tenantUrl || !tenantId) {
      return {
        ok: false,
        errorMessage: 'Missing required query parameters: tenantUrl or tenantId',
      }
    }

    console.log('Attempting to get access token for:')
    console.log('- tenantUrl:', tenantUrl)
    console.log('- tenantId:', tenantId)

    const accessToken = await getAccessToken(tenantUrl as string, tenantId as string)
    
    if (!accessToken) {
      return {
        ok: false,
        errorMessage: 'Failed to retrieve access token',
      }
    }

    return {
      ok: true,
      message: 'Access token retrieved successfully',
      // Only return the first few characters for security
      tokenPreview: accessToken.substring(0, 10) + '...',
    }
  } catch (error) {
    console.error('Error in test-token endpoint:', error)
    return {
      ok: false,
      errorMessage: `Error: ${error.message || error}`,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined,
    }
  }
})
