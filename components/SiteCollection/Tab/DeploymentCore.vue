<script setup lang="ts">
import type { PropType } from 'vue';
import type { Core } from '../../../types/core';

defineProps({
  core: {
    type: Object as PropType<Core>,
    required: true
  }
})
</script>

<template>
  <fluent-card class="deployment__core">
    <h6>{{ core.name }}</h6>
    <div class="deployment__core__lists">
      <template v-for="list in core.lists">
        <SiteCollectionTabDeploymentCoreList :list="list" />
      </template>
    </div>

  </fluent-card>
</template>

<style lang="scss" scoped>
.deployment__core {
  padding: $unit;
  display: flex;
  flex-direction: column;
  gap: $unit;

  &__lists {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    column-gap: $unit;
  }
}
</style>