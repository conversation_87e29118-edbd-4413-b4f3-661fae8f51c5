<script setup lang="ts">
  import { CoreTabNames } from '~/constants/tabs'
  import type { CoreSiteColumn } from '~/types/core'

  const tableColumns = [
    {
      title: 'Internal Name',
      key: 'InternalName',
    },
    {
      title: 'Display Name',
      key: 'DisplayName',
    },
    {
      title: 'Type',
      key: 'ColumnType',
    },
    {
      title: 'Actions',
      key: 'actions',
    },
  ]
  const columnTypes = ['Text', 'Note', 'DateTime', 'User', 'UserMulti']
  const route = useRoute()
  const router = useRouter()

  onMounted(() => {
    router.replace({
      query: {
        ...route.query,
        tab: CoreTabNames.SITE_COLUMNS,
      },
    })
  })

  const uiStore = useUiStore()
  const coresStore = useCoresStore()
  const { activeCore, activeCoreSiteColumns, activeCoreSiteColumnsJSON } = storeToRefs(coresStore)
  const filterWord = ref<string>('')
  const showImportJSON = ref(false)

  const filteredSiteColumns = computed(() => {
    return activeCoreSiteColumns.value.filter((siteColumn: CoreSiteColumn) => {
      return siteColumn.InternalName.toLowerCase().includes(filterWord.value.toLowerCase())
    })
  })

  function clickHandlerDeleteColumn(index: number) {
    coresStore.deleteCoreSiteColumn(index)
  }

  function clickHandlerAddRow() {
    coresStore.addCoreSiteColumn()
  }

  async function clickHandlerSubmit() {
    if (!activeCore.value) return
    uiStore.setLoading(true, 'Updating core site columns...')
    const response = await coresStore.updateCoreSiteColumns(
      activeCore.value.key,
      filteredSiteColumns.value,
    )
    console.log(response)
    uiStore.setLoading(false)
  }
</script>

<template>
  <Card>
    <div class="coreTabSiteColumns">
      <div class="content__left">
        <Input class="search" icon="ios-search" placeholder="Search.." v-model="filterWord" />
        <BaseTableComponent :columns="tableColumns" :data="filteredSiteColumns">
          <template #ColumnType="{ item, tabIndex }">
            <Select v-model="item.ColumnType" :tabindex="tabIndex" transfer>
              <Option v-for="(opt, ind) in columnTypes" :value="opt" :key="ind">{{ opt }}</Option>
            </Select>
          </template>
          <template #actions="{ index }">
            <Button icon="md-trash" @click="clickHandlerDeleteColumn(index)"></Button>
          </template>
        </BaseTableComponent>
        <div class="buttons">
          <Button @click="clickHandlerAddRow">Add Site Column</Button>
          <Button type="primary" @click="clickHandlerSubmit">Submit</Button>
        </div>
      </div>
      <div class="content__right">
        <div class="container">
          <span>JSON</span>
          <Button
            type="primary"
            style="float: right"
            @click.prevent="showImportJSON = !showImportJSON"
            >Import JSON</Button
          >
        </div>
        <Input v-model="activeCoreSiteColumnsJSON" type="textarea" :autosize="true" readonly />
      </div>
    </div>
  </Card>
</template>

<style lang="scss" scoped>
  .coreTabSiteColumns {
    display: grid;
    grid-template-columns: 70% 30%;
  }

  .content__left {
    display: flex;
    flex-direction: column;
    gap: $margin;

    .search {
      width: 200px;
    }

    .buttons {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-right: $margin;
    }
  }

  .content__right {
    display: flex;
    flex-direction: column;
    gap: $margin;

    .container {
      display: flex;
      justify-content: space-between;
      align-items: center;

      span {
        font-weight: 600;
      }
    }
  }
</style>
