<script setup lang="ts">

const emits = defineEmits(['update:modelValue'])
const props = defineProps({
    modelValue: {
        type: String,
        default: () => '0.0.0'
    },
})

const frontNumber = ref(0);
const middleNumber = ref(0);
const backNumber = ref(0);
const versionStringArray = ref([] as number[]);
const selectedType = ref('');

enum upVersionType {
    MAJOR = 'Major',
    MINOR = 'Minor',
    PATCH = 'Patch'
}

onMounted(() => {
    initialiseWorkflowVersionForm();
})

watch(() => props.modelValue, () => {
    initialiseWorkflowVersionForm();
})

function initialiseWorkflowVersionForm() {
    versionStringArray.value = useCloneDeep(props.modelValue.split('.').map((versionNumber) => Number(versionNumber)));
    frontNumber.value = useCloneDeep(versionStringArray.value[0]);
    middleNumber.value = useCloneDeep(versionStringArray.value[1]);
    backNumber.value = useCloneDeep(versionStringArray.value[2]);
}

function isPressed(type: upVersionType) {
    return selectedType.value === type;
}

function resetVersionValue() {
    initialiseWorkflowVersionForm();
}

function clickHandler(type: upVersionType) {
    if (selectedType.value === type) {
        return;
    }
    resetVersionValue();
    selectedType.value = type;

    if (type === upVersionType.MAJOR) {
        frontNumber.value++;
        middleNumber.value = 0;
        backNumber.value = 0;
    } else if (type === upVersionType.MINOR) {
        middleNumber.value++;
        backNumber.value = 0;
    } else if (type === upVersionType.PATCH) {
        backNumber.value++;
    }
}

function getNewVersion() {
    return `${frontNumber.value}.${middleNumber.value}.${backNumber.value}`;
}

function isTypeSelected() {
    return selectedType.value !== '';
}

defineExpose({
    getNewVersion,
    isTypeSelected
})


</script>

<template>
    <div class="form">
        <div class="number__form">
            <fluent-text-field :value="frontNumber" class="number__field" onkeydown="return false"
                inputmode="numeric" />
            <fluent-text-field :value="middleNumber" class="number__field" onkeydown="return false"
                inputmode="numeric" />
            <fluent-text-field :value="backNumber" class="number__field" onkeydown="return false" inputmode="numeric" />
        </div>
    </div>
    <div class="form">
        <div class="type__form">
            <BaseButton v-for="type in upVersionType" @click="clickHandler(type)" class="type__button"
                :appearance="isPressed(type) ? 'accent' : 'neutral'">{{ type
                }}</BaseButton>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.field {
    width: 64px;
}

.form {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.number {

    &__form {
        display: flex;
        gap: $margin;
    }

    &__field {
        width: 64px;
        appearance: none;
    }
}

.type {

    &__form {
        margin-top: $margin;
        display: flex;
        gap: $margin;
    }

}
</style>