<script setup lang="ts">
import type { WorkflowSetting } from '~/types/workflow';

const workflowStore = useWorkflowsStore()
const { activeWorkflow } = storeToRefs(workflowStore)

const workflowSetting = ref({} as WorkflowSetting)

const { resetHand<PERSON>, submitHandler } = useSettingForm<WorkflowSetting>({
    form: workflowSetting,
    activeData: activeWorkflow,
    updateHandler: async () => {
        await workflowStore.updateWorkflow(activeWorkflow.value.key, workflowSetting.value)
    }
})

</script>

<template>
    <Card dis-hover>
        <template #title>
            <b>Workflow</b>
        </template>
        <div class="workflowSetting">
            <Form label-position="left">
                <FieldInputSwitch v-model="workflowSetting.notificationStatus" label="Turn On Notification" />
                <FieldInputSwitch v-model="workflowSetting.eSignatureRequired" label="Turn On E-Signature" />
                <FieldInputSwitch v-model="workflowSetting.assignedActionOverride"
                    label="Turn On Assigned Action Override" />
                <FieldInputSwitch v-model="workflowSetting.hideUploadDocument" label="Hide Upload Document" />
                <FieldInputSwitch v-model="workflowSetting.multiLevelRevisionNumber"
                    label="Turn On 3 Levels Revision Number" />
                <!-- <FieldInputText v-model="workflowSetting.icon" label="Icon" /> -->
                <!-- <BaseIconDropdown v-model="workflowSetting.icon" /> -->
                <FieldInputIcon v-model="workflowSetting.icon" label="Icon" />
                <FieldInputText v-model="workflowSetting.type" label="Type (e.g EDMS, EQMS, ERMS, DQMS)" />
                <FieldInputText v-model="workflowSetting.qmspublishing" label="QMS Publishing folder name"
                    v-if="workflowSetting.type === 'EDMS'" />
                <FieldInputText v-model="workflowSetting.prefix" label="Register Prefix" />
                <FieldInputText v-model="workflowSetting.registerHeaderName" label="Register Header Name" />
                <FieldInputText v-model="workflowSetting.mainRegisterList" label="Main Register List Name" />
            </Form>
            <BaseButtonSaveReset @reset="resetHandler" @save="submitHandler" />
        </div>
    </Card>
</template>

<style lang="scss" scoped>
.workflowSetting {
    display: flex;
    flex-direction: column;
    gap: $margin;
    margin-bottom: $margin;
}
</style>