// Update multiple stages at once
// body { stages }
import type { Stage } from '@/types/data'

export default defineEventHandler(async (event) => {
  const { stages } = await readBody(event)
  const { db } = useFirebaseAdmin()

  if (!stages) {
    return {
      ok: false,
      errorMessage: `stages are required`,
    }
  }
  try {
    const updates: { [key: string]: number } = {}

    stages.forEach(async (stage: Stage, index: number) => {
      updates[`stages/${stage.firebaseKey}/order`] = index
    })
    await db.ref().update(updates)
    return {
      ok: true,
      message: `Stages order updated sucessfully`,
    }
  } catch (error) {
    return {
      ok: false,
      errorMessage: error,
    }
  }
})
