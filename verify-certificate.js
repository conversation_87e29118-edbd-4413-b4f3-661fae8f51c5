import crypto from 'crypto'

// This is a simple script to verify that the private key is valid
// Run with: node verify-certificate.js

// Replace with your actual environment variable or test with a sample
const rawKey = process.env.WORKBENCH_KEY
if (!rawKey) {
  console.error('WORK<PERSON>NCH_KEY environment variable is not set')
  process.exit(1)
}

try {
  // Try to create a private key object from the raw key
  const privateKeyObject = crypto.createPrivateKey({
    key: rawKey.replace(/\\n/g, '\n'),
    format: 'pem',
  })

  // Export the private key to verify it's valid
  const privateKey = privateKeyObject.export({
    format: 'pem',
    type: 'pkcs8',
  })

  console.log('✅ Private key is valid')
  console.log('Key format:', privateKeyObject.asymmetricKeyType)
  console.log('Key type:', privateKeyObject.type)
} catch (error) {
  console.error('❌ Error validating private key:', error)
}
