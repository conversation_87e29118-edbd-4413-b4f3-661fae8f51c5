<script setup lang="ts">
  import { ModuleTabNames } from '~/constants/tabs'
  const route = useRoute()
  const { tab } = route.query
  const tabName = tab as string

  const sharepointAPIStore = useSharepointAPIStore()
  const { tenantId } = storeToRefs(sharepointAPIStore)
  const isTenantIdExist = () => {
    return tenantId.value !== undefined && tenantId.value !== null && tenantId.value !== ''
  }
</script>

<template>
  <BaseTabs v-model="tabName">
    <BaseTabPane label="Settings" :name="ModuleTabNames.SETTINGS">
      <ModuleTabSetting />
    </BaseTabPane>
    <BaseTabPane label="Stages" :name="ModuleTabNames.STAGES">
      <ModuleTabStage />
    </BaseTabPane>
    <BaseTabPane label="Forms" :name="ModuleTabNames.FORMS">
      <ModuleTabForms />
    </BaseTabPane>
    <BaseTabPane label="Sharepoint Config" :name="ModuleTabNames.SHAREPOINT_CONFIG">
      <template v-if="isTenantIdExist()">
        <ModuleTabSharepointConfig />
      </template>
      <template v-else>
        <BaseTenantIdNotFound text="Tenant ID not found" />
      </template>
    </BaseTabPane>
    <BaseTabPane label="Registers" :name="ModuleTabNames.REGISTERS">
      <template v-if="isTenantIdExist()">
        <ModuleTabRegister />
      </template>
      <template v-else>
        <BaseTenantIdNotFound text="Tenant ID not found" />
      </template>
    </BaseTabPane>
    <BaseTabPane label="Actions Config" :name="ModuleTabNames.ACTIONCONFIG">
      <ModuleTabActionsConfig />
    </BaseTabPane>
    <BaseTabPane label="Permissions" :name="ModuleTabNames.PERMISSIONS">
      <ModuleTabPermission />
    </BaseTabPane>
    <BaseTabPane label="Config Specs" :name="ModuleTabNames.CONFIGSPECS">
      <ModuleTabConfigSpecs />
    </BaseTabPane>
  </BaseTabs>
</template>
