<script setup lang="ts">
import { CoreTabNames } from '~/constants/tabs';

const route = useRoute()

const { tab } = route.query
const tabName = tab as string
</script>

<template>
  <BaseTabs v-model="tabName">
    <BaseTabPane :name="CoreTabNames.SITE_COLUMNS" label="Site Columns">
      <CoreTabSiteColumns />
    </BaseTabPane>
    <BaseTabPane :name="CoreTabNames.LISTS" label="Lists">
      <CoreTabList />
    </BaseTabPane>
    <BaseTabPane :name="CoreTabNames.SITE_COLUMNS_LIST" label="Site Column in List">
      <CoreTabListSiteColumns />
    </BaseTabPane>
  </BaseTabs>
</template>