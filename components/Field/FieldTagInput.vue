<script setup lang="ts">
  defineProps({
    label: {
      type: String,
      required: false,
      default: '',
    },
    modelValue: {
      type: Array<string>,
      required: false,
      default: [],
    },
  })

  const options = ref<string[]>([])

  const emit = defineEmits(['update:modelValue'])

  function updateHandler(text: string) {
    emit('update:modelValue', text)
  }

  function handleOnCreate(tag: string) {
    options.value.push(tag)
  }
</script>

<template>
  <FieldInput :label="label">
    <Select
      :modelValue="modelValue"
      @update:modelValue="updateHandler"
      filterable
      multiple
      allow-create
      @on-create="handleOnCreate"
      transfer>
      <Option v-for="option in options" :key="option" :value="option">
        {{ option }}
      </Option>
    </Select>
  </FieldInput>
</template>
