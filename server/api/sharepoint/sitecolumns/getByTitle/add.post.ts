import { getAccessToken } from '../../../../utils/accessToken'

export default defineEventHandler(async (event) => {
  const query = getQuery(event)
  const { siteColumn } = await readBody(event)

  const { tenantUrl, tenantId, siteUrl, listTitle } = query

  const accessToken = await getAccessToken(tenantUrl as string, tenantId as string)
  const headers = {
    Accept: 'application/json;odata=verbose',
    'Content-Type': 'application/json;odata=verbose',
    Authorization: `Bearer ${accessToken}`, // Add this if using OAuth
  }

  const url = `${siteUrl}/_api/web/lists/getbytitle('${listTitle}')/fields`

  try {
    const response = await fetch(url, {
      method: 'POST',
      headers,
      body: JSON.stringify(siteColumn),
    })
    if (!response.ok) {
      const error = await response.json()
      console.error('Error adding column:', error)
      return { error }
    }
    const data = await response.json()
    console.log('Column added successfully:', data)
    await new Promise((resolve) => setTimeout(resolve, 500))
    return { status: 200, data }
  } catch (error) {
    console.error(`Failed to add fields for list "${listTitle}":`, error)
  }
})
