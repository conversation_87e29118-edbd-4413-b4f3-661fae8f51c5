<script setup lang="ts">

defineProps({
    active: {
        type: Boolean,
        default: false
    }
})

const emit = defineEmits(['click'])

function clickHandlerActive() {
    emit('click')
}
</script>


<template>
    <li :class="{ 'list__stages__item__active': active }" class="list__stages__item" @click="clickHandlerActive">
        <slot />
    </li>
</template>

<style lang="scss" scoped>
.list__stages {
    min-height: calc(100% + $margin);

    &__item {
        color: $fluent-font-color;
        margin: $unit 0;
        padding: $padding;
        cursor: pointer;
        border: 1px solid $stroke-color;
        list-style-type: none;
        user-select: none;
        display: flex;
        gap: $margin;
        align-items: center;

        &:hover {
            background-color: $white-hover;
            border: 1px solid $stroke-hover;
        }

        &:active {
            background-color: $white-pressed;
            border: 1px solid $stroke-pressed;
        }

        &__active {
            font-weight: bold;
            border: 1px solid $stroke-selected;
            background-color: $white-selected;

            &:hover {
                background-color: $white-selected;
                border: 1px solid $stroke-selected;

                &:active {
                    background-color: $white-pressed;
                    border: 1px solid $stroke-pressed;
                }
            }
        }
    }
}
</style>