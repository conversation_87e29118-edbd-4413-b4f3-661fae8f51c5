<script setup lang="ts">
const props = defineProps({
  to: {
    type: String,
    required: false,
    default: '/'
  },
  title: {
    type: String,
    required: true
  }
})

const route = useRoute();

const isActive = computed(() => route.path === props.to);

</script>

<template>
  <NuxtLink :to="to">
    <div class="siderMenuItem" :class="{ active: isActive }">
      {{ title }}
    </div>
  </NuxtLink>
</template>

<style scoped lang="scss">
.siderMenuItem {
  font-size: $sider-menu-font-size;
  margin: 0 $margin;
  padding: $sub-unit 0;
  font-size: 0.875rem;
  font-weight: 400;
  color: #161616;

  &.active {
    background: $sider-onpress-color;
  }

  &:hover {
    background-color: $sider-hover-color;
    text-decoration: underline;
  }
}
</style>