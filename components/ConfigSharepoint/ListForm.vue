<script setup lang="ts">
  import { CONFIG_SHAREPOINT_NEW_LIST } from '~/constants/default-values'
  import type { SharepointList } from '~/types/sharepointConfig'

  const props = defineProps({
    modelValue: {
      type: Object as PropType<SharepointList[]>,
      default: () => [],
    },
  })

  const sharepointAPIStore = useSharepointAPIStore()

  const emit = defineEmits(['update:modelValue'])
  const uiStrore = useUiStore()

  const sharepointConfigStore = useConfigSharepointStore()

  function addHandler() {
    const newModelValue = useCloneDeep(props.modelValue)
    newModelValue.push(CONFIG_SHAREPOINT_NEW_LIST)
    emit('update:modelValue', newModelValue)
  }

  function deleteHandler(index: number) {
    const newModelValue = useCloneDeep(props.modelValue)
    newModelValue.splice(index, 1)
    emit('update:modelValue', newModelValue)
  }

  async function clickHandlerAdd(index: number) {
    uiStrore.setLoading(true, `Adding ${props.modelValue[index].DisplayName} to site`)
    const listTitle = props.modelValue[index].DisplayName
    await sharepointAPIStore.addSiteList(listTitle)
    uiStrore.setLoading(false)
  }
</script>

<template>
  <Form>
    <template v-for="(list, index) in modelValue">
      <BaseLayoutRowForm grid-layout="20px 1fr 1fr 30px 50px">
        <BaseNumberTag class="formTag">{{ index + 1 }}</BaseNumberTag>
        <ConfigSharepointListFormSetting v-model="modelValue[index]" />
        <div class="icon icon--check" v-if="sharepointAPIStore.checkListExist(list.DisplayName)">
          <BaseIconCheck />
        </div>
        <div class="icon icon--add" v-else>
          <BaseIconAdd @click="clickHandlerAdd(index)" />
        </div>
        <BaseButtonDelete
          @click="deleteHandler(index)"
          class="formButton"
          :disabled="
            sharepointConfigStore.checkIfListIsTableInput(modelValue[index].InternalName)
          " />
      </BaseLayoutRowForm>
    </template>
  </Form>
  <BaseButtonAdd @click="addHandler" text="List" />
</template>

<style lang="scss" scoped>
  .icon {
    width: 1.5rem;
    margin-bottom: 16px;

    &--check {
      color: green;
    }

    &--add {
      color: red;
    }
  }
</style>
