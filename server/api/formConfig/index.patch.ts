import { FormConfig } from '~/types/formConfig'
import { useAuthenticatedFirebase } from '~/server/utils/firebaseDatabase'

export default defineEventHandler(async (event) => {
  const { formConfigKey, formConfigData } = await readBody(event)

  if (!formConfigKey || !formConfigData) {
    return {
      ok: false,
      errorMessage: 'formConfigKey and formConfigData are required',
    }
  }

  try {
    const { firebaseUpdate } = useAuthenticatedFirebase(event)
    await firebaseUpdate(`/formConfigs/${formConfigKey}`, formConfigData)
    return {
      ok: true,
      message: `Form Config with key: ${formConfigKey} updated successfully`,
    }
  } catch (error) {
    return {
      ok: false,
      errorMessage: error,
    }
  }
})
