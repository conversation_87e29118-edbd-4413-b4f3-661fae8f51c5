<script setup lang="ts">
  import type { RegisterSetting } from '~/types/register'

  const uiStore = useUiStore()
  const registerStore = useConfigRegisterStore()
  const { activeRegisterSetting, activeRegisterIndex } = storeToRefs(registerStore)
  const registerSetting = ref({} as RegisterSetting)

  onMounted(() => {
    initialiseRegisterSetting()
  })

  watch(
    () => activeRegisterSetting.value,
    () => {
      initialiseRegisterSetting()
    },
  )

  function initialiseRegisterSetting() {
    registerSetting.value = useCloneDeep(activeRegisterSetting.value)
  }

  function clickHandlerItem(index: number) {
    const selectedRegister = registerSetting.value.registers[index]
    registerStore.setActiveRegister(useCloneDeep(selectedRegister), index)
  }

  async function clickHandlerAdd() {
    uiStore.setLoading(true, 'Adding new register...')
    await registerStore.addNewRegister()
    uiStore.setLoading(false)
  }

  const isRegisterActive = (index: number) => {
    return isEqual(index, activeRegisterIndex.value)
  }
</script>

<template>
  <Card dis-hover>
    <template #title> Register List </template>
    <template v-for="(register, index) in registerSetting.registers">
      <ConfigRegisterItem @click="clickHandlerItem(index)" :active="isRegisterActive(index)">
        {{ register.name }}
      </ConfigRegisterItem>
    </template>
    <div class="button__wrapper">
      <BaseButtonAdd icon-only @click="clickHandlerAdd" />
    </div>
  </Card>
</template>

<style lang="scss" scoped>
  .button__wrapper {
    margin-top: $margin;
    display: flex;
    justify-content: center;
  }
</style>
