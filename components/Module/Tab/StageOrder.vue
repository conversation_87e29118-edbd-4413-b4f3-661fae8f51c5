<script setup lang="ts">
  import { getCurrentInstance } from 'vue'
  import type { Stage } from '@/types/data'
  import draggableComponent from 'vuedraggable'

  const dragOptions = {
    animation: 150,
    group: 'stages',
    disabled: false,
    ghostClass: 'ghost',
  }

  const uiStore = useUiStore()
  const stagesStore = useStagesStore()
  const formBuilderStore = useFormBuilderStore()
  const workflowStore = useWorkflowsStore()
  const { activeWorkflowKey } = storeToRefs(workflowStore)
  const { workflowStages, activeStage } = storeToRefs(stagesStore)

  const stages = ref([] as Stage[])
  const stageOrderChanged = ref(false)
  const newStageName = ref('New Stage')
  const showAddStageForm = ref(false)

  onMounted(() => {
    stages.value = useCloneDeep(workflowStages.value)
  })

  watch(workflowStages, (newStages) => {
    stages.value = useCloneDeep(newStages)
  })

  function clickHandlerReset() {
    stages.value = useCloneDeep(workflowStages.value)
    stageOrderChanged.value = false
  }

  async function clickHandlerSave() {
    uiStore.setLoading(true, 'Saving stage order...')
    formBuilderStore.resetActiveField()
    formBuilderStore.resetEditField()
    await stagesStore.updateStagesOrder(stages.value)
    stageOrderChanged.value = false
    uiStore.setLoading(false)
  }

  function clickHandlerActive(stageKey: string) {
    formBuilderStore.resetEditField()
    formBuilderStore.resetActiveField()
    stagesStore.setActiveStageKey(stageKey)
  }

  function changeHandler() {
    stageOrderChanged.value = true
  }

  async function clickHandlerAdd() {
    uiStore.setLoading(true, 'Adding new stage...')
    uiStore.setUpdateStageAction(StageUpdateAction.ADD_STAGE)
    await stagesStore.createNewStage(activeWorkflowKey.value, newStageName.value)
    // await uiStore.initialiseStageUpdateModal(stagesStore.createNewStage(activeStageWorkflowKey, newStageName.value))
    // await stagesStore.createNewStage(activeStageWorkflowKey, newStageName.value)
    clickHandlerNewStage()
    uiStore.setLoading(false)
  }

  function clickHandlerNewStage() {
    showAddStageForm.value = !showAddStageForm.value
    newStageName.value = 'New Stage'
  }
</script>

<template>
  <Card dis-hover>
    <template #title>
      <b>Stages</b>
    </template>
    <template #extra>
      <div class="extraButtonWrapper">
        <BaseButtonText @click="clickHandlerReset">Reset</BaseButtonText>
        <BaseButtonText @click="clickHandlerSave">Save</BaseButtonText>
      </div>
    </template>
    <draggableComponent
      v-model="stages"
      @change="changeHandler"
      item-key="firebaseKey"
      v-bind="dragOptions"
      class="list__stages"
      :component-data="{ name: 'fade' }"
      handle=".handle">
      <template #item="{ element }">
        <BaseDraggableItem
          :active="activeStage.firebaseKey === element.firebaseKey"
          @click="clickHandlerActive(element.firebaseKey)">
          {{ element.name }}
        </BaseDraggableItem>
      </template>
    </draggableComponent>
    <div class="addStageButton" @click="clickHandlerNewStage">
      <template v-if="!showAddStageForm"> Add Stage </template>
      <template v-else> Cancel </template>
    </div>
    <div v-if="showAddStageForm" class="addStageForm">
      <Form>
        <FieldInputText v-model="newStageName" aria-placeholder="Stage Name" />
      </Form>
      <BaseButton @click="clickHandlerAdd" appearance="accent">Submit</BaseButton>
    </div>
    <Alert type="warning" v-if="stageOrderChanged">
      Order change detected, click <b>Save</b> to save changes or <b>Reset</b>
    </Alert>
    <ModuleTabStageCopy />
  </Card>
</template>

<style scoped lang="scss">
  .extraButtonWrapper {
    display: flex;
    gap: $padding;
    overflow: hidden;
  }

  .addStageButton {
    margin-top: $margin;
    color: $primary-lightblue;
    cursor: pointer;

    &:hover {
      color: $primary-blue;
    }
  }

  .addStageForm {
    margin-top: $margin;
    display: flex;
    gap: $padding;
    justify-content: space-between;
    align-items: top;
  }
</style>
