<script setup lang="ts">
import draggableComponent from 'vuedraggable';
import { FormFieldType, type FormFieldData } from '~/types/form';

const formBuilderStore = useFormBuilderStore()
const { activeFormFields, activeEditField, isFormBuilderEdited } = storeToRefs(formBuilderStore)

function isImageField(type: FormFieldType) {
    return type === FormFieldType.IMAGE
}
function handlerChange() {
    formBuilderStore.refreshFormFieldsWithKeyState(activeFormFields.value)
    formBuilderStore.resetActiveField()
    formBuilderStore.resetEditField()
}

function clickHandlerField(field: FormFieldData, index: number) {
    formBuilderStore.resetEditField()
    formBuilderStore.setEditField(field, index)
    formBuilderStore.setActiveField(field)
}

function clickHandlerDelete(index: number) {
    formBuilderStore.deleteCanvasField(index)
    formBuilderStore.resetActiveField()
    formBuilderStore.resetEditField()
}
</script>

<template>
    <Card dis-hover class="container">
        <div class="form__builder">
            <div class="form__builder__header">
                <BaseAlertInfo v-if="isFormBuilderEdited">Form
                    modified, click <b>Save</b> to save changes or <b>Reset</b>
                </BaseAlertInfo>
            </div>
            <div class="form__builder__page">
                <Form label-position="left">
                    <draggableComponent @change="handlerChange" group="form" v-model="activeFormFields" handle=".handle"
                        item-key="key">
                        <template #item="{ element, index }">
                            <FormBuilderDraggableField :active="activeEditField.key === element.key"
                                :is-review="element.review" :width="element.width" :field="element"
                                @click="clickHandlerField(element, index)" @delete="clickHandlerDelete(index)"
                                :is-image="isImageField(element.type)">
                                <FormBuilderFieldGenerator :form-element-data="element" />
                            </FormBuilderDraggableField>
                        </template>
                    </draggableComponent>
                </Form>
            </div>
        </div>
    </Card>
</template>

<style lang="scss" scoped>
.container {
    max-height: calc(100vh - 232px);
}

.form__builder {
    height: calc(100vh - 290px);
    overflow-y: auto;
    overflow-x: hidden;

    &__header {
        display: flex;
        gap: $margin;
        position: sticky;
        height: 40px;
        top: 0;
        z-index: 1;
        background: white;
        margin-bottom: $margin;

        &--buttons {
            display: flex;
            gap: $unit;
        }
    }
}

.modal__center {
    display: flex;
    justify-content: center;
    align-items: center;

    .ivu-modal {
        top: -50px;
    }
}
</style>