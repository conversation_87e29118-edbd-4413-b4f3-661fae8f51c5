<script setup lang="ts">
  import type { ConditionOption, TableFlowCondition } from '~/types/form'

  const props = defineProps({
    modelValue: {
      type: Array<TableFlowCondition>,
      required: true,
      default: () => [],
    },
  })

  const emit = defineEmits(['update:modelValue'])
  const fieldStore = useFieldStore()
  const newModel = ref([] as TableFlowCondition[])

  onMounted(() => {
    newModel.value = useClone(props.modelValue)
  })

  watch(props.modelValue, (newModelValue) => {
    newModel.value = useClone(newModelValue)
  })

  const goToStageOptions = computed(() => {
    return fieldStore.getGoToStageOptions()
  })

  const fieldNameOptions = computed(() => {
    return fieldStore.getFieldNameForFlowCondition()
  })

  function clickHandlerDelete(index: number) {
    const newModelValue = [...props.modelValue]
    newModelValue.splice(index, 1)
    emit('update:modelValue', newModelValue)
  }

  function clickHandlerAdd() {
    const newModelValue = [...props.modelValue]
    newModelValue.push({
      fieldName: '',
      value: '',
      goToStage: '',
    })
    emit('update:modelValue', newModelValue)
  }
</script>

<template>
  <div class="flowTable">
    <div class="row__header">
      <div>Field Name</div>
      <div>Value</div>
      <div>Go To Stage</div>
      <div>Action</div>
    </div>
    <template v-for="(flow, index) in modelValue">
      <div class="row__content">
        <div>
          <Select v-model="modelValue[index].fieldName" filterable transfer>
            <Option v-for="field in fieldNameOptions" :value="field.fieldName">{{
              field.fieldName
            }}</Option>
          </Select>
        </div>
        <div>
          <Input v-model="flow.value" style="align-self: baseline" />
        </div>
        <div>
          <Select v-model="modelValue[index].goToStage" filterable transfer>
            <Option v-for="option in goToStageOptions" :value="option">{{ option }}</Option>
          </Select>
        </div>
        <div>
          <BaseButtonDelete appearance="accent" @click="clickHandlerDelete(index)" />
        </div>
      </div>
    </template>
  </div>
  <BaseButtonAdd text="Condition" @click="clickHandlerAdd" />
</template>

<style lang="scss" scoped>
  .flowTable {
    margin-bottom: $margin;
  }

  .row {
    display: grid;
    grid-template-columns: 40% 20% 20% 10%;
    text-overflow: ellipsis;
    gap: $margin;
    padding: $padding;
    border-bottom: 1px solid $primary-gray;
    align-items: center;

    &__header {
      @extend .row;
      background-color: $fluent-foreground2;
      justify-content: space-between;
      font-weight: bold;
    }

    &__content {
      @extend .row;
      background-color: $primary-white;
      justify-content: space-between;

      &:hover {
        background-color: $white-hover;
      }
    }
  }
</style>
