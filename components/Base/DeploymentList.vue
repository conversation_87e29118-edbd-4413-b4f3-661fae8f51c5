<template>
    <fluent-card class="deploymentList">
        <div class="title">
            <slot name="title" />
        </div>
        <div class="content">
            <slot name="content" />
        </div>
    </fluent-card>
</template>

<style lang="scss">
.deploymentList {
    padding: $unit;
    display: flex;
    flex-direction: column;
    gap: $unit;
    position: relative;
    height: 100%;
    width: 100%;
    overflow: auto;

    .title {
        font-size: 0.8rem;
        font-weight: bold;
        color: $primary-text;
        display: flex;
        justify-content: space-between;
    }

    .content {
        font-size: 0.8rem;
    }
}
</style>
