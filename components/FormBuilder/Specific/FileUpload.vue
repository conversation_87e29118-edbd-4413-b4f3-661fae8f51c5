<script setup lang="ts">
const { fileUploadField } = useFormBuilderFieldSetting()
</script>

<template>
    <FormBuilderFieldSettingLabel>File Upload Settings</FormBuilderFieldSettingLabel>
    <template v-if="fileUploadField">
        <Form label-position="top">
            <FieldInputSwitch v-model="fileUploadField.noMetadata" label="No Metadata" />
            <FieldInputSwitch v-model="fileUploadField.singleFile" label="Single File Only" />
        </Form>
    </template>
</template>