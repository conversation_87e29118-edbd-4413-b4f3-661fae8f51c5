// Update multiple workflows .inUse prop at once
// body {workflows}

import { Workflow } from '~/types/workflow'

export default defineEventHandler(async (event) => {
  const { workflows } = await readBody(event)
  const { db } = useFirebaseAdmin()

  if (!workflows) {
    return {
      ok: false,
      errorMessage: `workflows is required`,
    }
  }

  try {
    const updates: { [key: string]: boolean } = {}
    workflows.forEach((workflow: Workflow) => {
      updates[`workflows/${workflow.key}/inUse`] = workflow.inUse
    })
    await db.ref().update(updates)
    return {
      ok: true,
      message: `Workflows inUse updated sucessfully`,
    }
  } catch (error) {
    return {
      ok: false,
      errorMessage: error,
    }
  }
})
