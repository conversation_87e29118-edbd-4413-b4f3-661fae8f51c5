import { useSharepointAPIUtils } from '~/composables/useSharepointAPIUtils'

export default defineEventHandler(async (event) => {
  const { fetchSharepointAPIWithTenant } = useSharepointAPIUtils()
  try {
    const query = getQuery(event)
    const { tenantUrl, tenantId, uri } = query
    if (!tenantUrl || !uri) {
      return {
        error: true,
        message: `Missing required query parameters: 'tenant' or 'uri'.`,
      }
    }
    const response = await fetchSharepointAPIWithTenant(
      uri as string,
      tenantUrl as string,
      tenantId as string,
    )
    const data = await response.json()
    return {
      ok: true,
      data: data.d.results,
      message: `Fetched ${uri} successfully.`,
    }
  } catch (error) {
    return {
      ok: false,
      errorMessage: `Failed to retrieve url data: ${error}`,
    }
  }
})
