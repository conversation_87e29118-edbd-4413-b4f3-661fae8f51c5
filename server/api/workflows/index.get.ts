// GET ALL Workflows
import { useAuthenticatedFirebase } from '~/server/utils/firebaseDatabase'

export default defineEventHandler(async (event) => {
  try {
    const { firebaseFetch } = useAuthenticatedFirebase(event)
    const workflows = await firebaseFetch(`/workflows`)
    return {
      ok: true,
      data: workflows,
      message: `Sucessfully feth workflows`,
    }
  } catch (error) {
    return {
      ok: false,
      errorMessage: error,
    }
  }
})
