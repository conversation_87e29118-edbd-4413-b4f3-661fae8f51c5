<script setup lang="ts">
import type { Module } from '~/types/data';

const moduleStore = useModulesStore()
const { activeModule } = storeToRefs(moduleStore)
const showDeleteModal = ref(false)
const moduleSetting = ref({} as Module)

const { resetHandler, submitHandler } = useSettingForm<Module>({
    form: moduleSetting,
    activeData: activeModule,
    updateHandler: async () => {
        await moduleStore.updateModuleName(activeModule.value.key, moduleSetting.value.name)
    }
})

async function clickHandlerDelete() {
    showDeleteModal.value = true
}

function closeHandlerDelete() {
    showDeleteModal.value = false
}

</script>

<template>
    <ModuleModalDeleteModule v-model="showDeleteModal" @close="closeHandlerDelete" />
    <Card dis-hover>
        <template #title>
            <div class="title">
                <b>Module</b>
                <BaseButtonDeleteText text="Delete Module" @click="clickHandlerDelete" />
            </div>
        </template>
        <div class="formWrapper">
            <Form label-position="left">
                <FieldInputText v-model="moduleSetting.name" label="Name" />
            </Form>
            <BaseButtonSaveReset @reset="resetHandler" @save="submitHandler" />
        </div>
    </Card>
</template>

<style lang="scss" scoped>
.formWrapper {
    padding-bottom: $padding;
}

.title {
    display: flex;
    justify-content: space-between;
    align-self: center;
}
</style>