<script setup lang="ts">
import type { PropType } from 'vue';
import type { TableInputField, ActionField, BasicRiskField, FMEAField, RPNField, FinalRPNField } from '~/types/form';

defineProps({
  item: {
    type: Object as PropType<TableInputField | ActionField | BasicRiskField | FMEAField | RPNField | FinalRPNField>,
    required: true
  }
})
</script>

<template>
  <FieldCard>
    <template #icon>
      <Icon type="ios-grid" size="20" />
    </template>
    <template #title>
      {{ item.label }}
    </template>
    <template #content>
      <div class="inner__table">
        <TableInputHeader :options="item.options" :autoSerialize="item.autoSerialize" />
        <TableInputRow :options="item.options" :autoSerialize="item.autoSerialize" />
      </div>
      <Button class="button" icon="ios-add-circle">Add item</Button>
    </template>
  </FieldCard>
</template>

<style lang="scss" scoped>
.inner__table {
  background: $primary-charcoal-10;
  padding: $margin;

  .content {
    border-bottom: 1px solid $primary-charcoal-20;

    &:last-child {
      border: none;
    }
  }
}

.button {
  margin-top: $unit;
  background: #6c7a89;
  color: #fff;
}
</style>