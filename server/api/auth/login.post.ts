export default defineEventHandler(async (event) => {
  const body = await readBody(event)
  const { email, password } = body

  // Call Firebase REST API to sign in
  interface FirebaseAuthResponse {
    idToken: string
    email: string
    refreshToken: string
    expiresIn: string
    localId: string
  }

  const response = await $fetch<FirebaseAuthResponse>(
    'https://identitytoolkit.googleapis.com/v1/accounts:signInWithPassword?key=' +
      process.env.FIREBASE_API_KEY,
    {
      method: 'POST',
      body: {
        email,
        password,
        returnSecureToken: true,
      },
    },
  )

  const idToken = response.idToken
  const uid = response.localId
  const { auth } = useFirebaseAdmin()

  // Verify token via Admin SDK
  const decodedToken = await auth.verifyIdToken(idToken)
  const customToken = await auth.createCustomToken(uid)
  // Set session cookie
  setCookie(event, 'session', idToken, {
    httpOnly: true,
    sameSite: 'strict',
    path: '/',
    maxAge: 60 * 60 * 24, // 1 day
  })

  return {
    user: decodedToken,
    customToken,
  }
})
