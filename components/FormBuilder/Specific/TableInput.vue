<script setup lang="ts">
const { tableInputField } = useFormBuilderFieldSetting()

</script>

<template>
    <FormBuilderFieldSettingLabel>Table Input Settings</FormBuilderFieldSettingLabel>
    <template v-if="tableInputField">
        <Form label-position="top">
            <FieldInputSwitch v-model="tableInputField.removeAddButton" label="Remove Add Button" />
            <FieldInputNumber v-model="tableInputField.defaultTableRows" label="Default No. of Rows" />
            <FieldInputSwitch v-model="tableInputField.autoSerialize" label="Auto Serialize" />
            <FormBuilderSpecificCustomOptionSettings v-model="tableInputField.options" header-label="Table Inputs" />
        </Form>
    </template>
</template>