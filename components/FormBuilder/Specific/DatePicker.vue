<script setup lang="ts">
import { DatepickerDefaultValue } from '~/types/form';
const { datepickerField } = useFormBuilderFieldSetting()

</script>

<template>
    <FormBuilderFieldSettingLabel>Date Picker Settings</FormBuilderFieldSettingLabel>
    <template v-if="datepickerField">
        <Form label-position="top">
            <FieldInputSwitch v-model="datepickerField.disableBeforeToday" label="Disable Date before Today" />
            <FieldInputSwitch v-model="datepickerField.disableAfterToday" label="Disable Date after Today" />
            <FieldInputRadioButton v-model="datepickerField.defaultValue" label="Default Value">
                <Radio v-for="defaultValue in DatepickerDefaultValue" :label="defaultValue" />
            </FieldInputRadioButton>
            <FieldInputNumber v-model="datepickerField.daysAhead" label="No. of Days Ahead"
                v-if="datepickerField.defaultValue === DatepickerDefaultValue.Custom" />
        </Form>
    </template>
</template>