<script setup lang="ts">
const { documentDisplayField } = useFormBuilderFieldSetting()

</script>

<template>
    <FormBuilderFieldSettingLabel>Document Display Settings</FormBuilderFieldSettingLabel>
    <template v-if="documentDisplayField">
        <Form label-position="top">
            <FormBuilderSpecificOptionSettings v-model="documentDisplayField.options" header-label="Document Names" />
        </Form>
    </template>
</template>