<script setup lang="ts">
defineProps({
  label: {
    type: String
  },
  rules: {
    type: Object,
    default: {}
  },
  required: {
    type: Boolean,
    default: false
  },
  prop: {
    type: String,
  }
})
</script>

<template>
  <FormItem :label="label" class="formItem" :rules="rules" :required="required" :prop="prop">
    <slot />
  </FormItem>
</template>

<style lang="scss" scoped>
.formItem {
  color: $fluent-font-color;
  font-weight: 400;
}
</style>