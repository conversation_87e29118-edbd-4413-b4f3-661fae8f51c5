import { FormConfig } from '~/types/formConfig'

export default defineEventHandler(async (event) => {
  const { formConfigs } = await readBody(event)
  const { db } = useFirebaseAdmin()

  if (!Array.isArray(formConfigs) || formConfigs.length === 0) {
    return {
      ok: false,
      errorMessage: 'formConfigs array is required and cannot be empty',
    }
  }

  try {
    const updates: { [key: string]: boolean } = {}

    formConfigs.forEach((formConfig: FormConfig) => {
      updates[`formConfigs/${formConfig.key}/inUse`] = formConfig.inUse
    })
    await db.ref().update(updates)

    return {
      ok: true,
      message: "Form Configs 'inUse' flags updated successfully",
    }
  } catch (error: any) {
    return {
      ok: false,
      errorMessage: error?.message || 'An unexpected error occurred',
    }
  }
})
