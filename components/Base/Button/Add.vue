<script setup lang="ts">

defineProps({
    text: {
        type: String,
        default: '[add text prop]'
    },
    iconOnly: {
        type: Boolean,
        default: false
    },
    appearance: {
        type: String,
        default: 'accent'
    }
})

const emit = defineEmits(['click'])

function clickHandler() {
    emit('click')
}
</script>

<template>
    <BaseButton @click="clickHandler" :appearance="appearance">
        <Icon type="md-add" />
        <template v-if="!iconOnly">
            Add {{ text }}
        </template>
    </BaseButton>
</template>