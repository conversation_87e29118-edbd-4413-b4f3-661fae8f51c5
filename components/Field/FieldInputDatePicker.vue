<script setup lang="ts">
defineProps({
    label: {
        type: String
    },
    modelValue: {
        type: String
    }
})
const emit = defineEmits(['update:modelValue'])

function updateHandler(text: string) {
    emit('update:modelValue', text)
}
</script>

<template>
    <FieldInput :label="label">
        <DatePicker type="date" :modelValue="modelValue" @update:modelValue="updateHandler" />
    </FieldInput>
</template>