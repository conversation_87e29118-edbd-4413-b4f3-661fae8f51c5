<script setup lang="ts">
defineProps({
    modelValue: {
        type: String
    },
    placeholder: {
        type: String,
        default: 'Please select an option'
    },
    multiple: {
        type: Boolean,
        default: false
    }
})
const emit = defineEmits(['update:modelValue', 'onChange'])

function updateHandler(text: string) {
    emit('update:modelValue', text)
    handleOnChange()
}

function handleOnChange() {
    emit('onChange')
}
</script>

<template>
    <Select :modelValue="modelValue" @update:modelValue="updateHandler" :multiple="multiple" transfer>
        <slot />
    </Select>
</template>

<style lang="scss" scoped>
.select {
    width: 100%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}
</style>