<script setup lang="ts">
defineProps({
    label: {
        type: String,
        default: ''
    },

})

const emit = defineEmits(['add'])

function clickHandler() {
    emit('add')
}
</script>

<template>
    <div class="header">
        <h4>
            {{ label }}
        </h4>
        <BaseButtonAdd @click="clickHandler" icon-only />
    </div>
</template>

<style lang="scss" scoped>
.header {
    display: flex;
    justify-content: space-between;
    flex-direction: row;
    margin-bottom: $margin;
}
</style>