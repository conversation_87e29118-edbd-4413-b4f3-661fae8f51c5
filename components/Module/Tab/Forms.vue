<script setup lang="ts">
import { ModuleTabNames } from '~/constants/tabs'

const formBuilderStore = useFormBuilderStore()
const stageStore = useStagesStore()
const { formBuilderActiveStage } = storeToRefs(formBuilderStore)
const { workflowStages } = storeToRefs(stageStore)

const router = useRouter()
const route = useRoute()

onMounted(() =>
{
    router.replace({
        query: {
            ...route.query,
            tab: ModuleTabNames.FORMS
        }
    })

    if (workflowStages.value.length === 0)
    {
        console.log('No stages found')
        return
    }
    formBuilderStore.setActiveStage(workflowStages.value[0])
    formBuilderStore.initialiseFormBuilderForModule()
})

watch(() => workflowStages.value, (newValue) =>
{
    if (newValue.length === 0)
    {
        console.log('No stages found')
        return
    } else if (formBuilderActiveStage.value)
    {
        const activeStage = newValue.find((stage) => stage.firebaseKey === formBuilderActiveStage.value.firebaseKey)
        if (!activeStage)
        {
            formBuilderStore.setActiveStage(newValue[0])
        } else
        {
            formBuilderStore.setActiveStage(activeStage)
        }
    }
    formBuilderStore.initialiseFormBuilderForModule()
})
</script>

<template>
    <div class="stage-selector">
        <div></div>
        <FormBuilderModuleStageSelector />
        <div></div>
    </div>
    <FormBuilder />
</template>

<style lang="scss" scoped>
.moduleTabForms {
    display: grid;
    grid-template-columns: 220px calc(100% - 620px - 32px) 400px;
    gap: $margin;
}

.stage-selector {
    display: grid;
    grid-template-columns: $form-builder-layout;
    column-gap: $margin;
}
</style>