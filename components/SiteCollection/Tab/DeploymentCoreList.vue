<script setup lang="ts">
  import type { CoreList } from '~/types/core'
  import { type SiteColumnField, type SiteColumnConfig } from '~/types/sharepointConfig'

  const props = defineProps({
    list: {
      type: Object as PropType<CoreList>,
      required: true,
    },
  })

  enum columnTypeMap {
    'Text' = 2,
    'Note' = 3,
    'DateTime' = 4,
    'User' = 20,
    'UserMulti' = 20,
  }

  enum metadataTypeMap {
    'Text' = 'SP.FieldText',
    'Note' = 'SP.FieldMultiLineText',
    'DateTime' = 'SP.FieldDateTime',
    'User' = 'SP.FieldUser',
    'UserMulti' = 'SP.FieldUser',
  }
  const siteCollectionsStore = useSiteCollectionsStore()
  const { tenant } = storeToRefs(siteCollectionsStore)
  const sharepointAPIStore = useSharepointAPIStore()
  const { siteUrl } = storeToRefs(sharepointAPIStore)
  const loading = ref(false)

  const listSiteColumns = ref<SiteColumnField[]>([])
  const isLoading = ref(false)

  onMounted(async () => {
    isLoading.value = true
    listSiteColumns.value = await sharepointAPIStore.fetchSiteColumnsInList(props.list.DisplayName)
    isLoading.value = false
  })

  watch(
    () => props.list,
    async (newList) => {
      isLoading.value = true
      listSiteColumns.value = await sharepointAPIStore.fetchSiteColumnsInList(newList.DisplayName)
      isLoading.value = false
    },
  )

  const siteColumns = computed(() => {
    if (!props.list.SiteColumns) return []
    return props.list.SiteColumns.map((siteColumn) => {
      let existing = false
      if (listSiteColumns.value)
        existing =
          listSiteColumns.value.find(
            (existingSiteColumn) => existingSiteColumn.Title === siteColumn.DisplayName,
          ) !== undefined
      return {
        ...siteColumn,
        existing,
      }
    })
  })

  const siteColumnsNotExisting = computed(() => {
    return siteColumns.value.filter((siteColumn) => !siteColumn.existing)
  })

  async function clickHandlerDeploy() {
    loading.value = true
    for (const siteColumn of siteColumnsNotExisting.value) {
      const fieldTypeKind = columnTypeMap[siteColumn.ColumnType as keyof typeof columnTypeMap]
      const metadataType = metadataTypeMap[siteColumn.ColumnType as keyof typeof metadataTypeMap]
      if (!fieldTypeKind || !metadataType) {
        console.error(`Unknown column type: ${siteColumn.ColumnType}`)
      }

      let newSiteColumnConfig: SiteColumnConfig = {
        __metadata: { type: metadataType },
        Title: siteColumn.DisplayName,
        FieldTypeKind: fieldTypeKind,
        StaticName: siteColumn.InternalName,
        InternalName: siteColumn.InternalName,
        Required: false,
      }

      if (siteColumn.ColumnType === 'UserMulti') {
        newSiteColumnConfig = {
          ...newSiteColumnConfig,
          AllowMultipleValues: true,
          FieldTypeProperties: {
            SelectionMode: 1,
          },
        }
      }
      console.log(newSiteColumnConfig)
      console.log(`Adding column: ${siteColumn.DisplayName}`)

      await $fetch('/api/sharepoint/sitecolumns/getByTitle/add', {
        method: 'POST',
        params: {
          tenant: tenant.value,
          siteUrl: siteUrl.value,
          listTitle: props.list.DisplayName,
        },
        body: {
          siteColumn: newSiteColumnConfig,
        },
      })
    }
    await sharepointAPIStore.fetchSiteColumnsInList(props.list.DisplayName)
    loading.value = false
    console.log('All columns added successfully.')
  }
</script>

<template>
  <BaseDeploymentList>
    <template #title>
      {{ list.DisplayName }}
      <BaseButtonText v-if="siteColumnsNotExisting.length > 0" @click="clickHandlerDeploy"
        >Deploy</BaseButtonText
      >
    </template>
    <template #content>
      <BaseLoadingOverlay v-if="isLoading || loading" />
      <template v-for="item in siteColumns">
        <BaseDeploymentListItem>
          <div>{{ item.DisplayName }}</div>
          <div class="icon--check" v-if="item.existing">
            <BaseIconCheck />
          </div>
        </BaseDeploymentListItem>
      </template>
    </template>
  </BaseDeploymentList>
</template>
