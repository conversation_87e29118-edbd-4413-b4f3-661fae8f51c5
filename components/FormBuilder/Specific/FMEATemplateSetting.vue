<script setup lang="ts">
import { FORM_NEW_TABLE_INPUT_VARIANT_OPTION } from '~/constants/default-values';
import type { TableInputVariantOption } from '~/types/form';

const emit = defineEmits(['update:modelValue'])
const props = defineProps({
    modelValue: {
        type: Array as PropType<TableInputVariantOption[]>,
        default: () => []
    }
})

function addHandler()
{
    const newModelValue = [...props.modelValue]
    newModelValue.push(FORM_NEW_TABLE_INPUT_VARIANT_OPTION)
    emit('update:modelValue', newModelValue)
}

function clickHandlerDeleteOption(index: number)
{
    const newModelValue = [...props.modelValue]
    newModelValue.splice(index, 1)
    emit('update:modelValue', newModelValue)
}
</script>

<template>
    <FormBuilderSpecificOptionHeader label="Option" @add="addHandler" />
    <fluent-accordion class="optionAccordion">
        <FormBuilderSpecificAccordionOption v-for="option, index in modelValue">
            <template #header>
                {{ option.name }}
                <BaseButtonDelete @click="clickHandlerDeleteOption(index)" />
            </template>
            <FieldInputText v-model="modelValue[index].name" label="Display Name" />
        </FormBuilderSpecificAccordionOption>
    </fluent-accordion>
</template>

<style lang="scss" scoped>
.columnSpan {
    display: flex;
    justify-content: space-between;
}
</style>