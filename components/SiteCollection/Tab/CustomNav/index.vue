<script setup lang="ts">
  import { SITECOLLECTION_NEW_CUSTOM_NAVIGATION } from '~/constants/default-values'
  import { SiteCollectionTabNames } from '~/constants/tabs'
  import type { SiteCollectionNavigation } from '~/types/siteCollection'
  import { useSetRouteTab } from '~/composables/useSetRouteTab'

  const { setTab } = useSetRouteTab()

  onMounted(() => setTab(SiteCollectionTabNames.CUSTOM_NAVIGATION))

  const siteCollectionStore = useSiteCollectionsStore()
  const { activeSiteCollection } = storeToRefs(siteCollectionStore)

  const formCustomNav = ref<SiteCollectionNavigation>({
    navigationHeader: '',
    navigations: [],
  })

  const { resetHandler, submitHandler } = useSettingForm<SiteCollectionNavigation>({
    form: formCustomNav,
    activeData: activeSiteCollection,
    updateHandler: async () => {
      await siteCollectionStore.updateSiteCollection(
        activeSiteCollection.value.key,
        formCustomNav.value,
      )
    },
  })

  function clickHandlerAdd() {
    if (!formCustomNav.value.navigations) {
      formCustomNav.value.navigations = []
    }
    formCustomNav.value.navigations.push({ ...SITECOLLECTION_NEW_CUSTOM_NAVIGATION })
  }

  function deleteHandler(index: number) {
    formCustomNav.value.navigations.splice(index, 1)
  }
</script>

<template>
  <BaseCardPanel>
    <template #title> Custom Navigation </template>
    <Form>
      <FieldInputText label="Navigation Header" v-model="formCustomNav.navigationHeader" />
      <SiteCollectionTabCustomNavList
        :formCustomNav="formCustomNav"
        @delete="deleteHandler"
        v-if="formCustomNav.navigations && formCustomNav.navigations.length !== 0" />
      <BaseButtonAdd text="Navigation" @click="clickHandlerAdd" />
      <BaseButtonSaveReset @save="submitHandler" @reset="resetHandler" />
    </Form>
  </BaseCardPanel>
</template>

<style scoped lang="scss">
  .navigationRow {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 75px 75px;
    column-gap: $unit;
    margin-bottom: $unit;
    align-items: center;
    justify-items: center;

    div {
      width: 100%;
    }
  }
</style>
