<script setup lang="ts">
const props = defineProps<{
  linkColor: string, linkBackgroundColor: string, linkActiveTextColor: string
}>()

const emit = defineEmits<{
  (e: 'update:linkColor', value: string): void
  (e: 'update:linkBackgroundColor', value: string): void
  (e: 'update:linkActiveTextColor', value: string): void
}>()
</script>

<template>
  <div>
    <div class="sectionLabel">Link</div>
    <div class="themeMenu">
      <FormItem label="link">
        <ColorPicker :modelValue="linkColor" @update:modelValue="emit('update:linkColor', $event)" transfer />
      </FormItem>
      <FormItem label="linkBackground">
        <ColorPicker :modelValue="linkBackgroundColor" @update:modelValue="emit('update:linkBackgroundColor', $event)" transfer />
      </FormItem>
      <FormItem label="linkActiveText">
        <ColorPicker :modelValue="linkActiveTextColor" @update:modelValue="emit('update:linkActiveTextColor', $event)" transfer />
      </FormItem>
    </div>
  </div>
</template>

<style scoped lang="scss">
.themeMenu {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  align-items: start;
  justify-items: start;
}
.sectionLabel {
  font-size: 1rem;
  font-weight: 600;
}
</style>

