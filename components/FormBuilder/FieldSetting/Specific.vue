<script setup lang="ts">
const formBuilder = useFormBuilderStore()
const { activeEditField } = storeToRefs(formBuilder)

const mapTypeComponents: { [key: string]: object } = {
    Text: defineAsyncComponent(() => import('../Specific/Text.vue')),
    Image: defineAsyncComponent(() => import('../Specific/Image.vue')),
    Select: defineAsyncComponent(() => import('../Specific/Select.vue')),
    'Radio Buttons': defineAsyncComponent(() => import('../Specific/RadioButton.vue')),
    DatePicker: defineAsyncComponent(() => import('../Specific/DatePicker.vue')),
    TableInput: defineAsyncComponent(() => import('../Specific/TableInput.vue')),
    Peoplepicker: defineAsyncComponent(() => import('../Specific/PeoplePicker.vue')),
    FileUpload: defineAsyncComponent(() => import('../Specific/FileUpload.vue')),
    DocumentSelect: defineAsyncComponent(() => import('../Specific/DocumentSelect.vue')),
    DocumentListSelect: defineAsyncComponent(() => import('../Specific/DocumentListSelect.vue')),
    SearchOrCreate: defineAsyncComponent(() => import('../Specific/SearchOrCreate.vue')),
    DocumentsDisplay: defineAsyncComponent(() => import('../Specific/DocumentDisplay.vue')),
    NewDocumentTableInput: defineAsyncComponent(() => import('../Specific/NewDocumentTable.vue')),
    Actions: defineAsyncComponent(() => import('../Specific/Actions.vue')),
    MultiForm: defineAsyncComponent(() => import('../Specific/MultiForm.vue')),
    Link: defineAsyncComponent(() => import('../Specific/Link.vue')),
    Assessments: defineAsyncComponent(() => import('../Specific/Assessments.vue')),
    InputNumber: defineAsyncComponent(() => import('../Specific/NumberInput.vue')),
    BasicRiskTemplate: defineAsyncComponent(() => import('../Specific/BasicRiskTemplate.vue')),
    FMEATemplate: defineAsyncComponent(() => import('../Specific/FMEATemplate.vue')),
    RPNAssessment: defineAsyncComponent(() => import('../Specific/RPNAssessment.vue')),
    FinalRPNAssessment: defineAsyncComponent(() => import('../Specific/FinalRPNAssessment.vue')),

}
</script>

<template>
    <FormBuilderFieldSettingLayout>
        <template #field>
            <!-- Dynamic Component that loads the component when needed -->
            <component :is="mapTypeComponents[activeEditField.type]" v-if="activeEditField.type" />
        </template>
    </FormBuilderFieldSettingLayout>
</template>

<style lang="scss" scoped></style>
