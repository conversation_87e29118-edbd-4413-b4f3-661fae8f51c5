<script setup lang="ts">
  import { SITECOLLECTION_NEW_PERMISSION } from '~/constants/default-values'
  import { SiteCollectionTabNames } from '~/constants/tabs'
  import type { SiteCollectionPermission } from '~/types/siteCollection'
  import { useSetRouteTab } from '~/composables/useSetRouteTab'

  const siteCollectionStore = useSiteCollectionsStore()
  const { activeSiteCollection } = storeToRefs(siteCollectionStore)
  const { updateSiteCollection } = siteCollectionStore

  const { groupOptions } = useSelectOptionsUtils()
  const formPermission = ref<SiteCollectionPermission>({} as SiteCollectionPermission)

  const { setTab } = useSetRouteTab()
  onMounted(() => setTab(SiteCollectionTabNames.PERMISSIONS))

  const { resetHandler, submitHandler } = useSettingForm<SiteCollectionPermission>({
    form: formPermission,
    activeData: activeSiteCollection,
    updateHandler: async () => {
      await updateSiteCollection(activeSiteCollection.value.key, formPermission.value)
    },
  })

  function clickHandlerAddPermission() {
    formPermission.value.permissions ??= []
    formPermission.value.permissions.push(SITECOLLECTION_NEW_PERMISSION)
  }

  function deleteHandlerPermission(index: number) {
    formPermission.value.permissions.splice(index, 1)
  }
</script>

<template>
  <BaseCardPanel>
    <template #title>Permissions <Icon type="warning" /></template>
    <SiteCollectionTabPermissionsInfo />
    <SiteCollectionTabPermissionsTable
      @delete="deleteHandlerPermission"
      :formPermission="formPermission" />

    <div>
      <BaseButtonAdd @click="clickHandlerAddPermission" appearance="accent" text="Permission" />
    </div>

    <BaseButtonSaveReset @reset="resetHandler" @save="submitHandler" />
  </BaseCardPanel>
</template>
