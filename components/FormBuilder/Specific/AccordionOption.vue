<template>
    <!-- Wrap this component with <fluent-accordion> -->
    <fluent-accordion-item class="accordion__item">
        <span slot="heading" class="heading">
            <slot name="header" />
        </span>
        <div class="panel">
            <slot />
        </div>
    </fluent-accordion-item>
</template>

<style lang="scss" scoped>
.accordion__item {
    background-color: $fluent-foreground1;

    &:hover {
        background-color: $foreground1-hover;
    }
}

.heading {
    display: flex;
    justify-content: space-between;
}
</style>