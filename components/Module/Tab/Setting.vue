<script setup lang="ts">
import { ModuleTabNames } from '~/constants/tabs'

const router = useRouter()
const route = useRoute()
onMounted(() =>
{
    router.replace({
        query: {
            ...route.query,
            tab: ModuleTabNames.SETTINGS
        }
    })
})
</script>

<template>
    <div class="moduleTabSettings">
        <ModuleTabSettingModule />
        <ModuleTabSettingWorkflow />
    </div>
</template>

<style lang="scss" scoped>
.moduleTabSettings {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}
</style>