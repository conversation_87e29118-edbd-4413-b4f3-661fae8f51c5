export type __metadata = {
  id: string;
  uri: string;
  type: string;
};

export type __deferred = {
  __deferred: { uri: string };
};

export type __userId = {
  __metadata: __metadata;
  NameId: string;
  NameIdIssuer: string;
};

export type SiteUser = {
  __metadata: __metadata;
  Alerts: __deferred;
  Groups: __deferred;
  Id: number;
  IsHiddenInUI: boolean;
  LoginName: string;
  Title: string;
  PrincipalType: number;
  Email: string;
  Expiration: string;
  IsEmailAuthenticationGuestUser: boolean;
  IsShareByEmailGuestUser: boolean;
  IsSiteAdmin: boolean;
  UserId: __userId | null;
  UserPrincipalName: string;
};

export type SiteGroup = {
  __metadata: __metadata;
  Owner: __deferred;
  Users: __deferred;
  Id: number;
  isHiddenInUI: boolean;
  LoginName: string;
  Title: string;
  PrincipalType: number;
  AllowMembersEditMembership: boolean;
  AllowRequestToJoinLeave: boolean;
  AutoAcceptRequestToJoinLeave: boolean;
  Description: string;
  OnlyAllowMembersViewMembership: boolean;
  OwnerTitle: string;
  RequestToJoinLeaveEmailSetting: string;
};

export type SiteColumn = {
  Id: any;
  Title: string; // Display name of the column
  InternalName: string; // Internal name of the column
  TypeDisplayName: string; // The display name of the column type (e.g., "Single line of text")
  FieldTypeKind: number; // Numeric representation of the field type
  Hidden: boolean; // Whether the field is hidden
  ReadOnlyField: boolean; // Whether the field is read-only
  Required: boolean; // Whether the field is required
  StaticName: string; // Static name of the column
  SchemaXml: string; // XML schema of the column
  Description?: string; // Description of the column (optional)
  DefaultValue?: string; // Default value of the column (optional)
};

export type SharepointField = {
  AutoIndexed: boolean;
  AutofillInfo: any | null;
  CanBeDeleted: boolean;
  ClientSideComponentId: string;
  ClientSideComponentProperties: any | null;
  ClientValidationFormula: any | null;
  ClientValidationMessage: any | null;
  CustomFormatter: any | null;
  DefaultFormula: any | null;
  DefaultValue: any | null;
  Description: string;
  DescriptionResource: {
    __deferred: {
      uri: string;
    };
  };
  Direction: string;
  EnforceUniqueValues: boolean;
  EntityPropertyName: string;
  FieldTypeKind: number;
  Filterable: boolean;
  FromBaseType: boolean;
  Group: string;
  Hidden: boolean;
  Id: string;
  IndexStatus: number;
  Indexed: boolean;
  InternalName: string;
  IsModern: boolean;
  JSLink: string;
  MaxLength: number;
  PinnedToFiltersPane: boolean;
  ReadOnlyField: boolean;
  Required: boolean;
  SchemaXml: string;
  Scope: string;
  Sealed: boolean;
  ShowInFiltersPane: number;
  Sortable: boolean;
  StaticName: string;
  Title: string;
  TitleResource: {
    __deferred: {
      uri: string;
    };
  };
  TypeAsString: string;
  TypeDisplayName: string;
  TypeShortDescription: string;
  ValidationFormula: any | null;
  ValidationMessage: any | null;
  __metadata: {
    id: string;
    type: string;
    uri: string;
  };
};

export type SiteList = {
  Title: string; // The title of the list
  Id: string; // Unique identifier (GUID) of the list
  Description?: string; // Description of the list
  BaseTemplate: number; // Template ID of the list (e.g., 101 for Document Library)
  Created: string; // Creation date of the list (ISO string)
  LastItemModifiedDate: string; // Last modified date (ISO string)
  ItemCount: number; // Number of items in the list
  Hidden: boolean; // Whether the list is hidden
  AllowContentTypes: boolean; // Whether the list allows content types
  ContentTypesEnabled: boolean; // Whether content types are enabled
  DefaultViewUrl: string; // URL of the default view for the list
  ParentWebUrl: string; // URL of the parent web
  ListItemEntityTypeFullName: string; // Full name of the list item entity type
  Fields: __deferred;
  [key: string]: any; // For additional dynamic properties
};
