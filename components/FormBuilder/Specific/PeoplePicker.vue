<script setup lang="ts">
import { PeoplePickerDefaultValue } from '~/types/form';
const fieldStore = useFieldStore()
const { peoplePickerField } = useFormBuilderFieldSetting()
const activeGroup = computed(() => { return fieldStore.getActiveSiteCollectionGroups() })
</script>

<template>
    <FormBuilderFieldSettingLabel>People Picker Settings</FormBuilderFieldSettingLabel>
    <template v-if="peoplePickerField">
        <Form label-position="top">
            <FieldInputRadioButton v-model="peoplePickerField.defaultValue" label="Default Value">
                <Radio v-for="option in PeoplePickerDefaultValue" :label="option" />
            </FieldInputRadioButton>
            <FormBuilderSpecificPeoplePickerSelectOptions v-model="peoplePickerField.filterGroups"
                :options="activeGroup" />
        </Form>
    </template>
</template>