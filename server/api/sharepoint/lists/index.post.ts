import { getAccessToken } from '../../../utils/accessToken'

export default defineEventHandler(async (event) => {
  const query = getQuery(event)
  const { list } = await readBody(event)
  const { tenantUrl, tenantId, siteUrl } = query
  const accessToken = await getAccessToken(tenantUrl as string, tenantId as string)
  const headers = {
    'Content-Type': 'application/json;odata=verbose',
    Authorization: `Bearer ${accessToken}`,
  }

  const url = `${siteUrl}/_api/web/lists`

  try {
    const response = await fetch(url, {
      method: 'POST',
      headers,
      body: JSON.stringify(list),
    })
    if (!response.ok) {
      return {
        ok: false,
        errorMessage: `Failed to add list to ${siteUrl}`,
      }
    }
    return {
      ok: true,
      data: response,
      message: `List added to ${siteUrl}`,
    }
  } catch (error) {
    console.log(error)
    return {
      ok: false,
      errorMessage: `Failed to add list to ${siteUrl}: ${error}`,
    }
  }
})
