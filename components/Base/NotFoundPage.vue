<script setup lang="ts">
defineProps({
    text: {
        type: String,
        default: 'Not Found'
    },
})
</script>

<template>
    <div class="container">
        <div>
            <img src="/images/not-found.png" alt="not-found" width="300" />
            <div>{{ text }}</div>
            <!-- Slot ussualy used for add button -->
            <slot />
        </div>
    </div>
</template>

<style scoped lang="scss">
.container {
    height: calc(100vh - 120px);
    width: 100%;
    display: grid;

    div {
        align-self: center;
        margin-left: auto;
        margin-right: auto;
        max-width: 300px;
        display: grid;
        row-gap: $unit;

        span {
            align-self: center;
        }
    }
}
</style>