<script setup lang="ts">
  defineProps({
    label: {
      type: String,
      required: false,
      default: '',
    },
    modelValue: {
      type: String,
    },
    valueTag: {
      type: String,
      default: 'value',
    },
  })

  const fieldStore = useFieldStore()
  const peopleOptions = computed(() => {
    return fieldStore.getPeoplePickerOptions()
  })

  const emit = defineEmits(['update:modelValue'])

  function updateHandler(text: string) {
    emit('update:modelValue', text)
  }
</script>

<template>
  <Form>
    <FieldInput :label="label">
      <Select
        :modelValue="modelValue"
        @update:modelValue="updateHandler"
        :label="label"
        filterable
        transfer>
        <Option v-for="option in peopleOptions" :label="option.label" :value="option.value">
          {{ option.label }}
        </Option>
      </Select>
    </FieldInput>
  </Form>
</template>
