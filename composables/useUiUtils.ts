export const useUiUtils = () => {
  const route = useRoute()
  const siteCollectionsStore = useSiteCollectionsStore()
  const { siteCollectionsByKey } = storeToRefs(siteCollectionsStore)
  const moduleStore = useModulesStore()
  const { moduleByKey } = storeToRefs(moduleStore)
  const formStore = useFormStore()
  const { formsByKey } = storeToRefs(formStore)
  const coreStore = useCoresStore()
  const { coresByKey } = storeToRefs(coreStore)

  function getCrumbs() {
    const crumbs = []

    if (route.fullPath.includes('tenants')) {
      crumbs.push({
        name: 'Tenants',
        to: '/tenants',
      })
    }
    // /tenants/[tenant]
    if (route.params.tenant) {
      crumbs.push(getTenantCrumb())
    }

    // /tenants/[tenant]/[siteCollectionKey]
    if (route.params.siteCollectionKey) {
      const siteCrumb = getSiteCollectionCrumb()
      if (!siteCrumb) return crumbs
      crumbs.push(siteCrumb)
    }

    // /tenants/[tenant]/[siteCollectionKey]/module/[key]
    if (route.fullPath.includes('module')) {
      crumbs.push(getModuleCrumb())
      if (route.params.key) {
        crumbs.push(getModuleKeyCrumb())
      }
    }

    // /tenants/[tenant]/[siteCollectionKey]/form/[key]
    if (route.fullPath.includes('form')) {
      crumbs.push(getFormCrumb())
      if (route.params.key) {
        crumbs.push(getFormKeyCrumb())
      }
    }

    // /tenants/[tenant]/[siteCollectionKey]/cores/[key]
    if (route.fullPath.includes('cores')) {
      crumbs.push(getCoreCrumb())
      if (route.params.key) {
        crumbs.push(getCoreKeyCrumb())
      }
    }
    return crumbs
  }

  const getTenantCrumb = () => {
    return {
      name: route.params.tenant,
      to: `/tenants/${route.params.tenant}`,
    }
  }

  const getSiteCollectionCrumb = () => {
    const site = siteCollectionsByKey.value[route.params.siteCollectionKey as string]
    if (!site) return null
    const siteName = site.url.substring(site.url.lastIndexOf('/') + 1)
    return {
      name: siteName,
      to: `/tenants/${route.params.tenant}/${route.params.siteCollectionKey}`,
    }
  }

  const getModuleCrumb = () => {
    return {
      name: 'Module',
      to: `/tenants/${route.params.tenant}/${route.params.siteCollectionKey}/module`,
    }
  }

  const getModuleKeyCrumb = () => {
    const module = moduleByKey.value[route.params.key as string]
    if (!module) return null
    return {
      name: module.name,
      to: `/tenants/${route.params.tenant}/${route.params.siteCollectionKey}/module/${route.params.key}`,
    }
  }

  const getFormCrumb = () => {
    return {
      name: 'Form',
      to: `/tenants/${route.params.tenant}/${route.params.siteCollectionKey}/form`,
    }
  }

  const getFormKeyCrumb = () => {
    const form = formsByKey.value[route.params.key as string]
    if (!form) return null
    return {
      name: form.name,
      to: `/tenants/${route.params.tenant}/${route.params.siteCollectionKey}/form/${route.params.key}`,
    }
  }

  const getCoreCrumb = () => {
    return {
      name: 'Core',
      to: `/cores`,
    }
  }
  const getCoreKeyCrumb = () => {
    const core = coresByKey.value[route.params.key as string]
    if (!core) return null
    return {
      name: core.name,
      to: `/cores/${route.params.coreKey}`,
    }
  }
  return {
    getCrumbs,
  }
}
