export type PermissionSetting = {
  permissions: Permission[];
};

export type Permission = {
  name: PermissionType;
  groups: PermissionGroup[];
};

export enum PermissionType {
  REASSIGNACTION = "ReassignAction",
  CANCELPROCESS = "CancelProcess",
  CHANGEDUEDATE = "ChangeDueDate",
  CHANGEAUTHORINGDUEDATE = "ChangeAuthoringDueDate",
  CHANGEAPPROVERS = "ChangeApprovers",
  CHANGEFORM = "ChangeForm",
  CHANGEREVIEWERS = "ChangeReviewers",
  EDITDOCUMENTMETADATA = "EditDocumentMetadata",
  EDITLINKEDDOCUMENTS = "EditLinkedDocuments",
}

export type PermissionGroup = {
  name: string;
};
