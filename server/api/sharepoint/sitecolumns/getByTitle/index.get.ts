import { useSharepointAPIUtils } from '~/composables/useSharepointAPIUtils'
import { SiteColumn } from '~/types/sharepointAPI'
import { SiteColumnField } from '~/types/sharepointConfig'

export default defineEventHandler(async (event) => {
  const { fetchSharepointAPIWithTenant } = useSharepointAPIUtils()
  const query = getQuery(event)
  const { tenantUrl, tenantId, siteUrl, listTitle } = query

  const url = `${siteUrl}/_api/web/lists/getbytitle('${listTitle}')/fields?$filter=Hidden eq false`
  let columns: SiteColumnField[] = []

  try {
    const response = await fetchSharepointAPIWithTenant(
      url,
      tenantUrl as string,
      tenantId as string,
    )

    const data = await response.json()
    columns = data.d.results.map((field: SiteColumn) => ({
      Title: field.Title,
      InternalName: field.InternalName,
      Type: field.TypeDisplayName,
      Id: field.Id,
    }))

    return {
      ok: true,
      data: columns,
      message: `Fetched ${listTitle} fields successfully.`,
    }
  } catch (error) {
    return {
      ok: false,
      errorMessage: `Failed to retrieve fields from list: ${error}`,
    }
  }
})
