// DELETE Module
// body { moduleKey: string }

import { useAuthenticatedFirebase } from '~/server/utils/firebaseDatabase'

export default defineEventHandler(async (event) => {
  const { moduleKey } = await readBody(event)

  if (!moduleKey) {
    return {
      ok: false,
      errorMessage: 'moduleKey is required',
    }
  }

  try {
    const { firebaseRemove } = useAuthenticatedFirebase(event)
    await firebaseRemove(`/modules/${moduleKey}`)
    return {
      ok: true,
      data: null,
      message: `Module with key: ${moduleKey} deleted successfully`,
    }
  } catch (error) {
    return {
      ok: false,
      errorMessage: error,
    }
  }
})
