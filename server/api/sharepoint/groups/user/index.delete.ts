export default defineEventHandler(async (event) => {
  const query = getQuery(event)
  const { groupId, userId } = await readBody(event)
  const { tenantUrl, tenantId, siteUrl } = query

  const accessToken = await getAccessToken(tenantUrl as string, tenantId as string)
  const headers = {
    'X-HTTP-Method': 'DELETE',
    Authorization: `Bearer ${accessToken}`,
  }

  const url = `${siteUrl}/_api/web/sitegroups(${groupId})/users/getbyid(${userId})`
  try {
    const response = await fetch(url, {
      method: 'POST',
      headers,
    })
    if (!response.ok) {
      const error = await response.json()
      console.log(response)
      return {
        ok: false,
        errorMessage: `Failed to remove user: ${error.error.message.value}`,
      }
    }
    return {
      ok: true,
      data: null,
      message: `User removed from group.`,
    }
  } catch (error) {
    console.log(error)
    return {
      ok: false,
      errorMessage: `Failed to remove user: ${error}`,
    }
  }
})
