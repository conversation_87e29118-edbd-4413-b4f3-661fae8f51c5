<script setup lang="ts">
import draggableComponent from 'vuedraggable';
import formBasicComponentData from '../../content/formComponentBasic.json'
import formSpecialComponentData from '../../content/formComponentSpecial.json'
const formBasicComponentList = [...formBasicComponentData.formBasicComponent]
const formSpecialComponentList = [...formSpecialComponentData.formSpecialComponent]


</script>

<template>
    <div class="components">
        <fluent-accordion>
            <fluent-accordion-item expanded class="accordionItem">
                <span slot="heading" class="componentHeading">
                    Basic Components
                </span>
                <draggableComponent class="panel" v-model="formBasicComponentList" item-key="type" :sort="false"
                    :group="{ name: 'form', pull: 'clone', put: false }">
                    <template #item="{ element }">
                        <BaseButton class="draggable__component">
                            {{ element.label }}
                        </BaseButton>
                    </template>
                </draggableComponent>
            </fluent-accordion-item>
            <fluent-accordion-item expanded class="accordionItem">
                <span slot="heading" class="componentHeading">
                    Special Components
                </span>
                <draggableComponent class="panel" v-model="formSpecialComponentList" item-key="type" :sort="false"
                    :group="{ name: 'form', pull: 'clone', put: false }">
                    <template #item="{ element }">
                        <BaseButton class="draggable__component">
                            {{ element.label }}
                        </BaseButton>
                    </template>
                </draggableComponent>
            </fluent-accordion-item>
        </fluent-accordion>
    </div>

</template>

<style lang="scss" scoped>
.components {
    max-height: calc(100vh - 255px);
    overflow-y: auto;
}

.panel {
    display: flex;
    flex-direction: column;
    gap: $sub-unit;
}

.draggable__component {
    width: 100%;
    color: $fluent-font-color-light;
}

.accordionItem {
    background-color: $fluent-foreground2;
    font-weight: bolder;
    color: $fluent-font-color;
}
</style>