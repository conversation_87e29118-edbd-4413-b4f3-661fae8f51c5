<script setup lang="ts">
defineProps({
    object: {
        type: Object,
        default: () => ({})
    }
})
</script>

<template>
    <div class="wrapper">
        <div v-for="keys in Object.keys(object)" class="data">
            <span><b>{{ keys }}</b></span>
            <span>:</span>
            <span>{{ object[keys] }}</span>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.wrapper {
    margin-top: $margin;
    border: 1px solid $primary-gray;
    padding-bottom: $padding;
}

.data {
    padding-left: $padding;
    padding-right: $padding;
    padding-top: $padding;
    display: grid;
    grid-template-columns: 1fr 20px 1fr;
}
</style>