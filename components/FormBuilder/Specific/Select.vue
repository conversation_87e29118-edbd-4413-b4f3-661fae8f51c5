<script setup lang="ts">
const { selectField } = useFormBuilderFieldSetting()
</script>

<template>
  <FormBuilderFieldSettingLabel>Select Settings</FormBuilderFieldSettingLabel>
  <template v-if="selectField">
    <Form label-position="top">
      <FieldInputSwitch v-model="selectField.multiple" label="Enable Multiple Select" />
      <FieldInputSwitch v-model="selectField.checkbox" label="Enable Checkbox UI" v-if="selectField.multiple" />
      <FieldInputSwitch v-model="selectField.useDataFromList" label="Use Data From List" />
      <template v-if="selectField.useDataFromList">
        <FieldInputText v-model="selectField.listname" label="List Name" />
        <FieldInputText v-model="selectField.itemvalue" label="Item Value" />
        <FieldInputText v-model="selectField.itemtext" label="Item Text" />
        <FieldInputSwitch v-model="selectField.addOtherValue" label="Add Other Value" />
      </template>
      <template v-else>
        <FormBuilderSpecificOptionSettings v-model="selectField.options" />
      </template>
    </Form>
  </template>
</template>