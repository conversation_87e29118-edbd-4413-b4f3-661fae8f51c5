import { type Tenant } from '../types/data'
import { useStoreUtils } from './utils/storeFetchUtils'
import { useApiMutation } from './utils/apiMutation'

export const useTenantsStore = defineStore('tenants', () => {
  const tenants = ref([] as Tenant[])
  const tenantByName = computed(() => useKeyBy(tenants.value, 'name'))
  const { convertFirebaseDataToArray } = useStoreUtils()

  // ----------------- Callbacks -----------------

  // pages/tenants
  const tenantsPageCallback = (firebaseTenantData: FirebaseData<Partial<Tenant>>): void => {
    tenants.value = convertFirebaseDataToArray(firebaseTenantData)
  }

  const getTenantFromName = (tenantName: string) => {
    return tenantByName.value[tenantName]
  }

  const updateTenant = async ({
    tenantKey,
    tenantData,
  }: {
    tenantKey: string
    tenantData: Tenant
  }) => {
    await useApiMutation(`/api/tenants`, {
      method: 'PATCH',
      body: { tenantKey, tenantData },
    })
  }

  return {
    tenantsPageCallback,
    tenants,
    getTenantFromName,
    updateTenant,
  }
})
