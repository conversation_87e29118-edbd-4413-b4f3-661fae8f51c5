<template>
    <div class="pageLayout">
        <div class="header">
            <slot name="header" />
        </div>
        <div class="main">
            <slot name="content" />
        </div>
    </div>
</template>

<style lang="scss" scoped>
.pageLayout {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.header {
    position: sticky;
    height: 50px;
    top: 0;
    background: white;
    z-index: 1;
    padding: $margin;
}

.main {
    height: 100%;
    padding: $margin;
}
</style>