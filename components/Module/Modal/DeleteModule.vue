<script setup lang="ts">
import type { Option } from '~/types';

const props = defineProps({
    modelValue: {
        type: Boolean,
        default: false
    }
});

const emit = defineEmits(['close', 'update:modelValue']);
const modulesStore = useModulesStore();
const { activeModule } = storeToRefs(modulesStore);
const selectedWorkflowsToDelete = ref<string[]>([]);
const isVisible = ref(props.modelValue);
const workflowsStore = useWorkflowsStore();
const workflowOptions = ref<Option[]>([]);
const indeterminate = ref(false);
const checkAll = ref(false);
const isLoading = ref(false);
const uiStore = useUiStore();
const siteCollectionStore = useSiteCollectionsStore();
const { activeSiteCollection, tenant } = storeToRefs(siteCollectionStore);

function handlerCheckAll() {
    if (indeterminate.value) {
        checkAll.value = false;
    } else {
        checkAll.value = !checkAll.value;
    }
    indeterminate.value = false;

    if (checkAll.value) {
        selectedWorkflowsToDelete.value = workflowOptions.value.map((workflow) => workflow.value);
    } else {
        selectedWorkflowsToDelete.value = [];
    }
}

function checkAllHandler() {
    if (selectedWorkflowsToDelete.value.length === workflowOptions.value.length) {
        checkAll.value = true;
        indeterminate.value = false;
    } else if (selectedWorkflowsToDelete.value.length === 0) {
        checkAll.value = false;
        indeterminate.value = false;
    } else {
        checkAll.value = false;
        indeterminate.value = true;
    }
}

async function onOkHandler() {
    uiStore.setLoading(true, 'Deleting module workflows...');
    emit('close');
    // Delete selected workflow
    await workflowsStore.deleteMultipleWorkflows(selectedWorkflowsToDelete.value);
    // Delete Module
    uiStore.setLoadingMessage('Removing module from site collection...');
    await siteCollectionStore.removeModuleFromSiteCollection(activeSiteCollection.value.key, activeModule.value.key);
    uiStore.setLoadingMessage('Deleting module...');
    await modulesStore.deleteModule(activeModule.value.key);

    // Navigate to Site Collection Register Page
    uiStore.setLoading(false);
    navigateTo(`/tenants/${tenant.value}/${activeSiteCollection.value.key}`)
}

function onCancelHandler() {
    selectedWorkflowsToDelete.value = [];
    emit('close');
}

async function initialiseModal() {
    isLoading.value = true;
    const moduleWorkflows = await workflowsStore.getModuleWorkflows(activeModule.value.key);
    workflowOptions.value = moduleWorkflows.map((workflow) => ({
        label: `${workflow.name} - ${workflow.version}`,
        value: workflow.key
    }));
    isLoading.value = false;
}


onMounted(async () => {
    await initialiseModal();
})

watch(() => props.modelValue, (newValue) => {
    isVisible.value = newValue;
});

watch(isVisible, (newValue) => {
    emit('update:modelValue', newValue);
});

watch(activeModule, async () => {
    await initialiseModal();
})
</script>

<template>
    <Modal v-model="isVisible" @close="emit('close')" @on-ok="onOkHandler" @on-cancel="onCancelHandler">
        <template #title>
            {{ activeModule.name }}
        </template>
        <BaseLoadingOverlay v-if="isLoading" />
        <div class="container">
            <div>Confirm Delete <b>{{ activeModule.name }}</b> Module?</div>
            <div>Select workflow to delete:</div>
            <Checkbox :indeterminate="indeterminate" v-model="checkAll" @click.prevent="handlerCheckAll">
                Select All
            </Checkbox>
            <CheckboxGroup v-model="selectedWorkflowsToDelete" @on-change="checkAllHandler">
                <div style="display: grid;">
                    <Checkbox v-for="workflow in workflowOptions" :key="workflow.value" :label="workflow.value">
                        {{ workflow.label }}
                    </Checkbox>
                </div>
            </CheckboxGroup>
            <BaseWarning>This action cannot be reverted</BaseWarning>
        </div>
    </Modal>
</template>

<style lang="scss" scoped>
.container {
    display: grid;
    row-gap: $margin;
}
</style>