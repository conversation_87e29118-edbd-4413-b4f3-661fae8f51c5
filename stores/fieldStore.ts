import type { Form } from '~/types/data'
import type { Option } from '~/types'
import { FormFieldType, type ConditionOption, type FormFieldData } from '~/types/form'
import type { SiteCollectionGroups } from '~/types/siteCollection'

export const useFieldStore = defineStore('field', () => {
  const siteCollectionStore = useSiteCollectionsStore()
  const workflowStore = useWorkflowsStore()
  const stageStore = useStagesStore()
  const formStore = useFormStore()
  const sharepointAPIStore = useSharepointAPIStore()

  const usersExisting = computed(() => sharepointAPIStore.usersExisting)
  const ListsExisting = computed(() => sharepointAPIStore.listsExisting)
  const { activeSiteCollection } = storeToRefs(siteCollectionStore)
  const { activeWorkflow } = storeToRefs(workflowStore)
  const { workflowStages } = storeToRefs(stageStore)
  const forms = computed(() => formStore.forms)

  function getPeoplePickerOptions() {
    return usersExisting.value.map((user) => ({
      value: user.LoginName,
      label: user.Title,
    }))
  }

  function getFieldOptionForCondition() {
    const availableFields: ConditionOption[] = []
    if (!workflowStages) return availableFields
    workflowStages.value.forEach((stage) => {
      if (!stage.form) {
        stage.form = []
      }
      stage.form.forEach((form) => {
        if (form.type === FormFieldType.SELECT || form.type === FormFieldType.RADIOBUTTON) {
          availableFields.push({
            fieldName: form.fieldName,
            stageName: stage.name,
            options: form.options,
          })
        }
      })
    })
    return availableFields
  }

  function getFieldNameForFlowCondition() {
    let availableFields: FormFieldData[] = []

    if (!workflowStages.value) return availableFields

    if (workflowStages.value.length === 0) {
      return availableFields
    } else {
      workflowStages.value.forEach((stage) => {
        if (!stage.form) {
          stage.form = []
        }
        stage.form.forEach((form) => {
          availableFields.push({
            ...form,
            fieldName:
              form.fieldName === '' || form.fieldName === undefined
                ? '**FIELD NAME NOT SET**'
                : form.fieldName,
          })
        })
      })
    }
    return availableFields
  }

  function getActiveForms() {
    const activeForm: Form[] = []
    siteCollectionStore.getActiveSiteCollectionForms().forEach((item) => {
      activeForm.push(forms.value.find((form) => form.key === item) ?? ({} as Form))
    })
    return activeForm
  }

  function getActiveSiteCollectionGroups() {
    const groupOptions: string[] = []
    const siteCollectionGroups: SiteCollectionGroups = activeSiteCollection.value
    if (!siteCollectionGroups.groups) return groupOptions
    siteCollectionGroups.groups.forEach((group) => {
      if (sharepointAPIStore.checkGroupExist(group)) {
        groupOptions.push(group)
      }
    })
    return groupOptions
  }

  function getGoToStageOptions() {
    const goToStageOption: string[] = []
    if (!workflowStages) return goToStageOption
    workflowStages.value.forEach((stage) => {
      goToStageOption.push(stage.name)
    })
    goToStageOption.push('Completed')
    return goToStageOption
  }

  function getStageOptions() {
    const goToStageOption: Option[] = []
    if (!workflowStages) return goToStageOption
    workflowStages.value.forEach((stage) => {
      goToStageOption.push({
        value: stage.firebaseKey,
        label: stage.name,
      })
    })
    return goToStageOption
  }

  function getPeoplePickerFieldNameOptions() {
    const fieldNameOptions: string[] = []

    workflowStages.value.forEach((stage) => {
      if (!stage.form) {
        stage.form = []
      }
      stage.form.forEach((field) => {
        if (field.type === FormFieldType.PEOPLEPICKER) {
          if (!field.fieldName) {
            fieldNameOptions.push('--Unnamed Field Name--')
            return
          }
          fieldNameOptions.push(field.fieldName)
        }
      })
    })

    return fieldNameOptions
  }

  function getListOptions() {
    const listOptions = [] as Option[]
    console.log(activeWorkflow.value.lists)
    if (!activeWorkflow.value.lists) return listOptions
    activeWorkflow.value.lists.forEach((list) => {
      listOptions.push({
        value: list.DisplayName,
        label: list.DisplayName,
      })
    })
    return listOptions
  }

  function getDCCRCCListOptions() {
    const list = ListsExisting.value.filter(
      (list) => list.BaseTemplate === 101 && list.Hidden === false,
    )
    return list.map((item) => {
      return {
        value: item.Title,
        label: item.Title,
      }
    })
  }

  function getCustomListOptions() {
    const customListOptions = [] as Option[]
    if (!activeWorkflow.value.customlists) return customListOptions
    activeWorkflow.value.customlists.forEach((list) => {
      customListOptions.push({
        value: list.fieldName,
        label: list.fieldName,
      })
    })

    return customListOptions
  }

  async function getFieldinSharepointList(listDisplayName: string) {
    const fields = await sharepointAPIStore.fetchSiteColumnsInList(listDisplayName)
    return fields
  }

  return {
    getActiveForms,
    getActiveSiteCollectionGroups,
    getFieldOptionForCondition,
    getFieldNameForFlowCondition,
    getPeoplePickerOptions,
    getCustomListOptions,
    getGoToStageOptions,
    getStageOptions,
    getListOptions,
    getPeoplePickerFieldNameOptions,
    getFieldinSharepointList,
    getDCCRCCListOptions,
  }
})
