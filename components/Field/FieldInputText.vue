<script setup lang="ts">
defineProps({
  label: {
    type: String
  },
  modelValue: {
    type: String
  },
  required: {
    type: Boolean,
    default: false
  },
  rules: {
    type: Object,
    default: {}
  },
  prop: {
    type: String,
  },
  disabled: {
    type: Boolean,
    default: false
  },
  readonly: {
    type: Boolean,
    default: false
  }
})
const emit = defineEmits(['update:modelValue'])

function inputHandler(event: Event) {
  const target = event.target as HTMLInputElement
  emit('update:modelValue', target.value)
}
</script>

<template>
  <FieldInput :label="label" :rules="rules" :required="required" :prop="prop">
    <Input :modelValue="modelValue" @input="inputHandler" class="field" :disabled="disabled" :readonly="readonly" />
  </FieldInput>
</template>

<style lang="scss" scoped>
.field {
  width: 100%;
}
</style>