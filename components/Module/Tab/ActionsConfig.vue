<script setup lang="ts">
  import { ModuleTabNames } from '~/constants/tabs'
  import type { NewWorkflowAction, WorkflowAction } from '~/types/workflow'

  const workflowStore = useWorkflowsStore()
  const { activeWorkflow } = storeToRefs(workflowStore)
  const stageStore = useStagesStore()
  const { workflowStages } = storeToRefs(stageStore)
  const uiStore = useUiStore()
  const route = useRoute()
  const router = useRouter()

  const workflowActions = ref({} as WorkflowAction)

  function initialiseWorkflowActions() {
    workflowActions.value.actions = useCloneDeep(
      workflowStore.getActionWorkflow(),
    ) as NewWorkflowAction[]
    console.log(workflowActions.value)
  }

  function clickHandlerReset() {
    workflowActions.value.actions = useCloneDeep(
      workflowStore.getActionWorkflow(),
    ) as NewWorkflowAction[]
  }

  async function clickHandlerSave() {
    uiStore.setLoading(true, 'Saving Workflow Actions Config...')
    console.log(workflowActions.value)
    await workflowStore.updateWorkflow(activeWorkflow.value.key, workflowActions.value)
    uiStore.setLoading(false)
  }

  onMounted(() => {
    router.replace({
      query: {
        ...route.query,
        tab: ModuleTabNames.ACTIONCONFIG,
      },
    })
    initialiseWorkflowActions()
  })

  watch(activeWorkflow, () => {
    initialiseWorkflowActions()
  })

  watch(workflowStages, () => {
    initialiseWorkflowActions()
  })
</script>

<template>
  <ModuleTabActionsConfigLayout>
    <div class="header">
      <BaseButtonSaveReset @save="clickHandlerSave" @reset="clickHandlerReset" />
    </div>
    <div v-for="(actions, index) in workflowActions.actions">
      <template v-if="!actions.stageKey">
        <ModuleTabActionsConfigNoStageKey :action="workflowActions.actions[index]" :index="index" />
      </template>
      <template v-else>
        <ModuleTabActionsConfigRegister
          v-model="workflowActions.actions[index]"
          :stageKey="actions.stageKey" />
      </template>
    </div>
  </ModuleTabActionsConfigLayout>
</template>

<style lang="scss" scoped>
  .header {
    display: flex;
    column-gap: $margin;
    align-items: flex-start;
  }
</style>
