<script setup lang="ts">
import { ModuleTabNames } from '~/constants/tabs'

const router = useRouter()
const route = useRoute()
onMounted(() =>
{
    router.replace({
        query: {
            ...route.query,
            tab: ModuleTabNames.STAGES
        }
    })
})
</script>
<template>
    <div class="content">
        <ModuleTabStageOrder />
        <ModuleTabStageSetting />
    </div>
</template>

<style scoped lang="scss">
.content {
    display: grid;
    grid-template-columns: 25% 75%;
    grid-auto-flow: column;
    gap: $margin;
}
</style>
