<script setup lang="ts">
const props = defineProps<{
  dashboardCardH1Color: string, dashboardCardH4Color: string
}>()

const emit = defineEmits<{
  (e: 'update:dashboardCardH1Color', value: string): void
  (e: 'update:dashboardCardH4Color', value: string): void
}>()
</script>

<template>
  <div>
    <div class="sectionLabel">Dashboard</div>
    <div class="themeMenu">
      <FormItem label="dashboardCardH1">
        <ColorPicker :modelValue="dashboardCardH1Color" @update:modelValue="emit('update:dashboardCardH1Color', $event)" transfer />
      </FormItem>
      <FormItem label="dashboardCardH4">
        <ColorPicker :modelValue="dashboardCardH4Color" @update:modelValue="emit('update:dashboardCardH4Color', $event)" transfer />
      </FormItem>
    </div>
  </div>
</template>

<style scoped lang="scss">
.themeMenu {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  align-items: start;
  justify-items: start;
}
.sectionLabel {
  font-size: 1rem;
  font-weight: 600;
}
</style>

