<script setup lang="ts">
  import { SiteCollectionEnvironment, type SiteCollection } from '~/types/siteCollection'

  const emit = defineEmits(['close'])

  const siteCollectionStore = useSiteCollectionsStore()
  const { tenant } = storeToRefs(siteCollectionStore)
  const uiStore = useUiStore()
  const { coreOptions } = useSelectOptionsUtils()

  const newSiteCollection = ref<SiteCollection>({} as SiteCollection)

  const isFormValid = computed(
    () => !!newSiteCollection.value.url && !!newSiteCollection.value.environment,
  )

  function resetNewSiteCollection() {
    newSiteCollection.value = useClone({
      tenant: tenant.value,
      environment: SiteCollectionEnvironment.Dev,
    }) as SiteCollection
  }

  function closeHandler() {
    console.log('closeHandler')
    emit('close')
  }

  watch(
    tenant,
    () => {
      resetNewSiteCollection()
    },
    { immediate: true },
  )

  async function submit() {
    uiStore.setLoading(true, `Adding ${newSiteCollection.value.url} to ${tenant.value}`)
    try {
      await siteCollectionStore.addSiteCollection(newSiteCollection.value)
      emit('close')
    } finally {
      uiStore.setLoading(false)
    }
  }
</script>

<template>
  <Drawer
    title="Add Site Collection"
    :model-value="true"
    width="800"
    :mask-closable="false"
    @on-close="closeHandler">
    <Form>
      <FieldInputText v-model="newSiteCollection.tenant" label="Tenant" readonly />
      <FieldInputText v-model="newSiteCollection.url" label="Site Collection URL" required />
      <FieldInputSiteCollectionEnvironment
        v-model="newSiteCollection.environment"
        label="Environment" />
      <FieldInput label="Cores">
        <Select v-model="newSiteCollection.cores" multiple filterable transfer>
          <Option v-for="core in coreOptions" :key="core.value" :value="core.value">
            {{ core.label }}
          </Option>
        </Select>
      </FieldInput>
    </Form>
    <BaseButtonSaveCancel @cancel="closeHandler" @save="submit" :disable-submit="!isFormValid" />
  </Drawer>
</template>
