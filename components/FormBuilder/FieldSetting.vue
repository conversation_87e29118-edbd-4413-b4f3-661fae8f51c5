<script setup lang="ts">
const formBuilderStore = useFormBuilderStore()
const { activeEditField, isEditFieldSelected, enableReview } = storeToRefs(formBuilderStore)
</script>

<template>
    <Card dis-hover class="container">
        <template #title>
            Field Setting {{ activeEditField.type ? `(${activeEditField.type})` : '' }}
        </template>
        <template v-if="isEditFieldSelected">
            <BaseTabs>
                <BaseTabPane label="General" name="general">
                    <FormBuilderFieldSettingGeneral />
                </BaseTabPane>
                <BaseTabPane label="Field Specific" name="specific">
                    <FormBuilderFieldSettingSpecific />
                </BaseTabPane>
                <BaseTabPane label="Review" :disabled="!enableReview" name="review">
                    <FormBuilderFieldSettingReview />
                </BaseTabPane>
            </BaseTabs>
        </template>
        <template v-else>
            Select field to modify
        </template>
    </Card>
</template>

<style scoped lang="scss">
.container {
    max-height: calc(100vh - 290px);
}
</style>