<script setup lang="ts">
import type { MultiFormForm } from '~/types/form';

const props = defineProps({
    modelValue: {
        type: Array as PropType<MultiFormForm[]>,
        default: () => []
    }
})

const emit = defineEmits(['update:modelValue'])
const fieldStore = useFieldStore();

const activeForm = computed(() => {
    return fieldStore.getActiveForms()
})
const activeGroup = computed(() => {
    return fieldStore.getActiveSiteCollectionGroups()
})

function addHandler() {
    const newModelValue = [...props.modelValue]
    newModelValue.push({
        formKey: '',
        actionDescription: '',
        groups: ''
    })
    emit('update:modelValue', newModelValue)
}

function clickHandlerDelete(index: number) {
    const newModelValue = [...props.modelValue]
    newModelValue.splice(index, 1)
    emit('update:modelValue', newModelValue)
}
</script>

<template>
    <Form label-position="top">
        <FormBuilderSpecificOptionHeader label="Multi Form Details" @add="addHandler" />
        <template v-for="form, index in modelValue">
            <div class="row__container">
                <BaseNumberTag>{{ index + 1 }}</BaseNumberTag>
                <div>
                    <FieldInputSelect v-model="form.formKey" :options="activeForm" v-slot="{ option }" :no-margin="true"
                        value-tag="key" label-tag="name">
                        {{ option.name }}
                    </FieldInputSelect>
                    <FieldInputTextNoWrapper v-model="form.actionDescription" placeholder="Action Description" />
                    <FieldInputSelect v-model="form.groups" :options="activeGroup" v-slot="{ option }" :no-margin="true"
                        value-tag="name">
                        {{ option }}
                    </FieldInputSelect>
                </div>
                <BaseButtonDelete @click="clickHandlerDelete(index)" />
            </div>
        </template>
    </Form>
</template>

<style lang="scss" scoped>
.conditionToAppear {
    display: flex;
    flex-direction: column;
    gap: $unit;
}

.row__container {
    display: grid;
    grid-template-columns: 20px 1fr 20px;
    gap: $unit;
    padding: 0 $margin $margin 0;
}

.option {
    &--fieldname {
        display: grid;
        grid-template-columns: 200px 1fr;
        gap: $unit;
    }
}


.fieldname {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
}

.stagename {
    color: $primary-gray;
}
</style>