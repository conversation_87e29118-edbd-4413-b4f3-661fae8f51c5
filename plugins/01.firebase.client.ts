import { initializeApp } from 'firebase/app'
import { getAuth } from 'firebase/auth'
import { getDatabase } from 'firebase/database'

export default defineNuxtPlugin(() => {
  const config = useRuntimeConfig().public
  const firebaseConfig = {
    apiKey: config.FIREBASE_API_KEY,
    authDomain: config.FIREBASE_AUTH_DOMAIN,
    projectId: config.FIREBASE_PROJECT_ID,
    storageBucket: config.FIREBASE_STORAGE_BUCKET,
    messagingSenderId: config.FIREBASE_MESSAGING_SENDER_ID,
    appId: config.FIREBASE_APP_ID,
    measurementId: config.FIREBASE_MEASUREMENT_ID,
    databaseURL: config.FIREBASE_DATABASE_URL,
  }

  for (const key in firebaseConfig) {
    if (!firebaseConfig[key as keyof typeof firebaseConfig]) {
      console.warn(`⚠️ Missing Firebase config value: ${key}`)
    }
  }

  const app = initializeApp(firebaseConfig)
  const auth = getAuth(app)
  const db = getDatabase(app)
  if (!app) {
    throw new Error('Firebase app initialization failed. Please check your configuration.')
  }

  return {
    provide: {
      db: db,
      auth: auth,
    },
  }
})
