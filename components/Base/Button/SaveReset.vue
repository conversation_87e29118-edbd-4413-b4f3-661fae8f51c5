<script setup lang="ts">
const emit = defineEmits(['reset', 'save'])

function clickHandlerReset() {
    emit('reset')
}
function clickHandlerSave() {
    emit('save')
}
</script>

<template>
    <div class="buttonWrapper">
        <BaseButton @click="clickHandlerReset">Reset</BaseButton>
        <BaseButton appearance="accent" @click="clickHandlerSave">Save</BaseButton>
    </div>

</template>

<style lang="scss" scoped>
.buttonWrapper {
    display: flex;
    gap: $padding;
    margin-top: $margin;
    margin-bottom: $margin;
}
</style>