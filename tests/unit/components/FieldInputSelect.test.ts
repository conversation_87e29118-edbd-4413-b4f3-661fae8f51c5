import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { nextTick } from 'vue'
import FieldInputSelect from '~/components/Field/FieldInputSelect.vue'

// Mock the composables
vi.mock('@vueuse/core', () => ({
  refDebounced: vi.fn((ref) => ref),
  onClickOutside: vi.fn(),
}))

describe('FieldInputSelect', () => {
  const defaultProps = {
    label: 'Test Label',
    modelValue: '',
    placeholder: 'Select an option',
    options: ['Option 1', 'Option 2', 'Option 3'],
    id: 'test-select',
  }

  let wrapper: any

  beforeEach(() => {
    wrapper = mount(FieldInputSelect, {
      props: defaultProps,
    })
  })

  describe('Basic Rendering', () => {
    it('renders correctly with default props', () => {
      expect(wrapper.exists()).toBe(true)
      expect(wrapper.find('.dropdown').exists()).toBe(true)
    })

    it('displays the label when provided', () => {
      expect(wrapper.text()).toContain('Test Label')
    })

    it('shows placeholder when no value is selected', () => {
      expect(wrapper.find('.value-text').text()).toBe('Select an option')
    })

    it('applies readonly class when readonly prop is true', async () => {
      await wrapper.setProps({ readonly: true })
      expect(wrapper.find('.dropdown').classes()).toContain('readonly')
    })
  })

  describe('Dropdown Functionality', () => {
    it('opens dropdown when clicked', async () => {
      await wrapper.find('.dropdown').trigger('click')
      expect(wrapper.vm.dropdownActive).toBe(true)
    })

    it('closes dropdown when clicking outside', async () => {
      wrapper.vm.dropdownActive = true
      await nextTick()
      
      // Simulate onClickOutside callback
      const onClickOutsideMock = vi.mocked(require('@vueuse/core').onClickOutside)
      const callback = onClickOutsideMock.mock.calls[0][1]
      callback()
      
      expect(wrapper.vm.dropdownActive).toBe(false)
    })

    it('shows search input when dropdown is active and searchable', async () => {
      await wrapper.setProps({ searchable: true })
      wrapper.vm.dropdownActive = true
      await nextTick()
      
      expect(wrapper.find('.filterInput').exists()).toBe(true)
    })

    it('does not show search input when searchable is false', async () => {
      await wrapper.setProps({ searchable: false })
      wrapper.vm.dropdownActive = true
      await nextTick()
      
      expect(wrapper.find('.filterInput').exists()).toBe(false)
    })
  })

  describe('Option Selection', () => {
    it('emits update:modelValue when option is selected', async () => {
      wrapper.vm.dropdownActive = true
      await nextTick()
      
      const option = wrapper.findAll('.dropdown__menu__item')[0]
      await option.trigger('click')
      
      expect(wrapper.emitted('update:modelValue')).toBeTruthy()
      expect(wrapper.emitted('update:modelValue')[0]).toEqual(['Option 1'])
    })

    it('closes dropdown after option selection', async () => {
      wrapper.vm.dropdownActive = true
      await nextTick()
      
      const option = wrapper.findAll('.dropdown__menu__item')[0]
      await option.trigger('click')
      
      expect(wrapper.vm.dropdownActive).toBe(false)
    })

    it('works with complex objects using valueTag and labelTag', async () => {
      const complexOptions = [
        { id: 1, name: 'John Doe' },
        { id: 2, name: 'Jane Smith' },
      ]
      
      await wrapper.setProps({
        options: complexOptions,
        valueTag: 'id',
        labelTag: 'name',
      })
      
      wrapper.vm.dropdownActive = true
      await nextTick()
      
      const option = wrapper.findAll('.dropdown__menu__item')[0]
      await option.trigger('click')
      
      expect(wrapper.emitted('update:modelValue')[0]).toEqual([1])
    })
  })

  describe('Search Functionality', () => {
    beforeEach(async () => {
      await wrapper.setProps({ searchable: true })
    })

    it('filters options based on search input', async () => {
      wrapper.vm.dropdownActive = true
      wrapper.vm.filterText = 'Option 1'
      await nextTick()
      
      expect(wrapper.vm.filteredOptions).toEqual(['Option 1'])
    })

    it('shows empty state when no options match search', async () => {
      wrapper.vm.dropdownActive = true
      wrapper.vm.filterText = 'Non-existent option'
      await nextTick()
      
      expect(wrapper.find('.dropdown__menu__empty').exists()).toBe(true)
    })

    it('is case insensitive', async () => {
      wrapper.vm.dropdownActive = true
      wrapper.vm.filterText = 'option 1'
      await nextTick()
      
      expect(wrapper.vm.filteredOptions).toEqual(['Option 1'])
    })
  })

  describe('Keyboard Navigation', () => {
    beforeEach(async () => {
      wrapper.vm.dropdownActive = true
      await nextTick()
    })

    it('navigates down with ArrowDown key', async () => {
      await wrapper.trigger('keydown', { key: 'ArrowDown' })
      expect(wrapper.vm.highlightedIndex).toBe(0)
      
      await wrapper.trigger('keydown', { key: 'ArrowDown' })
      expect(wrapper.vm.highlightedIndex).toBe(1)
    })

    it('navigates up with ArrowUp key', async () => {
      wrapper.vm.highlightedIndex = 1
      await wrapper.trigger('keydown', { key: 'ArrowUp' })
      expect(wrapper.vm.highlightedIndex).toBe(0)
    })

    it('loops to first option when navigating down from last option', async () => {
      wrapper.vm.highlightedIndex = 2 // Last option
      await wrapper.trigger('keydown', { key: 'ArrowDown' })
      expect(wrapper.vm.highlightedIndex).toBe(0)
    })

    it('loops to last option when navigating up from first option', async () => {
      wrapper.vm.highlightedIndex = 0
      await wrapper.trigger('keydown', { key: 'ArrowUp' })
      expect(wrapper.vm.highlightedIndex).toBe(2)
    })

    it('selects highlighted option with Enter key', async () => {
      wrapper.vm.highlightedIndex = 1
      await wrapper.trigger('keydown', { key: 'Enter' })
      
      expect(wrapper.emitted('update:modelValue')).toBeTruthy()
      expect(wrapper.emitted('update:modelValue')[0]).toEqual(['Option 2'])
    })

    it('closes dropdown with Escape key', async () => {
      await wrapper.trigger('keydown', { key: 'Escape' })
      expect(wrapper.vm.dropdownActive).toBe(false)
    })
  })

  describe('Clear Functionality', () => {
    it('shows clear button when clearable and has value', async () => {
      await wrapper.setProps({ 
        clearable: true, 
        modelValue: 'Option 1' 
      })
      
      expect(wrapper.find('.clear-icon').exists()).toBe(true)
    })

    it('does not show clear button when no value', async () => {
      await wrapper.setProps({ 
        clearable: true, 
        modelValue: '' 
      })
      
      expect(wrapper.find('.clear-icon').exists()).toBe(false)
    })

    it('clears value when clear button is clicked', async () => {
      await wrapper.setProps({ 
        clearable: true, 
        modelValue: 'Option 1' 
      })
      
      await wrapper.find('.clear-icon').trigger('click')
      
      expect(wrapper.emitted('update:modelValue')).toBeTruthy()
      expect(wrapper.emitted('update:modelValue')[0]).toEqual([''])
    })
  })

  describe('Accessibility', () => {
    it('has proper tabindex for keyboard navigation', () => {
      expect(wrapper.find('.dropdown').attributes('tabindex')).toBe('0')
    })

    it('highlights options with proper class', async () => {
      wrapper.vm.dropdownActive = true
      wrapper.vm.highlightedIndex = 1
      await nextTick()
      
      const highlightedOption = wrapper.findAll('.dropdown__menu__item')[1]
      expect(highlightedOption.classes()).toContain('highlighted')
    })
  })
})
