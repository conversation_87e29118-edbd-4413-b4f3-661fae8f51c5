import { Message, Notice } from "view-ui-plus";

export enum StageUpdateAction
{
  ADD_STAGE,
  UPDATE_STAGE,
  DELETE_STAGE,
  STAGE_ORDER_UPDATE,
  COPY_STAGE,
}

export const useUiStore = defineStore("ui", () =>
{
  // ----------------- Loading -----------------
  const isLoading = ref(false);
  const loadingMessage = ref("Loading");

  const setLoading = (value: boolean, message = "Loading") =>
  {
    isLoading.value = value;
    loadingMessage.value = message;
  };

  const setLoadingMessage = (message: string) =>
  {
    loadingMessage.value = message;
  };

  // ----------------- Messaging -----------------

  const showErrorMessage = (message: string) =>
  {
    Message.error(message);
  };

  const showErrorNotice = (title: string, message: string) =>
  {
    Notice.error({
      title,
      desc: message,
      duration: 10,
    });
  };

  // ----------------- Workflow Up Version Drawer -----------------
  const workflowUpVersionDrawer = ref(false);

  const toggleWorkflowUpVersionDrawer = (open: boolean) =>
  {
    workflowUpVersionDrawer.value = open;
  };

  // ----------------- Stage Update Modal -----------------
  const stageModalAction = ref<StageUpdateAction | null>(null);
  const stageUpdateModal = ref(false);
  const stageUpdateModalOnOk = ref<Promise<void>>();

  const showStageUpdateModal = (onOk?: Promise<void>) =>
  {
    if (onOk) stageUpdateModalOnOk.value = onOk;
    stageUpdateModal.value = true;
  };

  const closeStageUpdateModal = () =>
  {
    stageUpdateModal.value = false;
  };

  const setUpdateStageAction = (action: StageUpdateAction) =>
  {
    stageModalAction.value = action;
  };

  // ----------------- Exports -----------------
  return {
    // Loading
    isLoading,
    loadingMessage,
    setLoading,
    setLoadingMessage,

    // Messages
    showErrorMessage,
    showErrorNotice,

    // Workflow Drawer
    workflowUpVersionDrawer,
    toggleWorkflowUpVersionDrawer,

    // Stage Modal
    stageUpdateModal,
    stageUpdateModalOnOk,
    stageModalAction,
    showStageUpdateModal,
    closeStageUpdateModal,
    setUpdateStageAction,
  };
});
