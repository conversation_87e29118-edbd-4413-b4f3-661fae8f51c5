<script setup lang="ts">
import { FormTabNames } from '~/constants/tabs'
const route = useRoute()
const { tab } = route.query
const tabName = tab as string
</script>

<template>
    <BaseTabs v-model="tabName">
        <BaseTabPane label="Settings" :name="FormTabNames.SETTINGS">
            <FormTabSettings />
        </BaseTabPane>
        <BaseTabPane label="Form Builder" :name="FormTabNames.FORMS">
            <FormTabForms />
        </BaseTabPane>
        <BaseTabPane label="Sharepoint Config" :name="FormTabNames.SHAREPOINT_CONFIG">
            <FormTabSharepointConfig />
        </BaseTabPane>
        <BaseTabPane label="Permissions" :name="FormTabNames.PERMISSIONS">
            <FormTabPermission />
        </BaseTabPane>
        <BaseTabPane label="Register" :name="FormTabNames.REGISTERS">
            <FormTabRegister />
        </BaseTabPane>
    </BaseTabs>
</template>