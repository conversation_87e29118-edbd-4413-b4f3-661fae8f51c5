<script setup lang="ts">
const { actionField } = useFormBuilderFieldSetting()
</script>

<template>
    <FormBuilderFieldSettingLabel>Action Table Settings</FormBuilderFieldSettingLabel>
    <template v-if="actionField">
        <Form label-position="top">
            <FieldInputSwitch v-model="actionField.removeAddButton" label="Remove Add Button" />
            <FieldInputNumber v-model="actionField.defaultTableRows" label="Default No. of Rows" />
            <FieldInputText v-model="actionField.actionType" label="Action Type" />
            <FieldInputText v-model="actionField.actionDescription" label="Action Description" />
            <FieldInputText v-model="actionField.actionStage" label="Action Stage" />
            <FieldInputSwitch v-model="actionField.autoSerialize" label="Auto Serialize" />
            <FormBuilderSpecificCustomOptionSettings v-model="actionField.options" header-label="Action Inputs" />
        </Form>
    </template>
</template>