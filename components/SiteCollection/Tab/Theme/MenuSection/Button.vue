<script setup lang="ts">
const props = defineProps<{
  buttonColor: string, buttonHoverColor: string
}>()

const emit = defineEmits<{
  (e: 'update:buttonColor', value: string): void
  (e: 'update:buttonHoverColor', value: string): void
}>()
</script>

<template>
  <div>
    <div class="sectionLabel">Button</div>
    <div class="themeMenu">
      <FormItem label="button">
        <ColorPicker :modelValue="buttonColor" @update:modelValue="emit('update:buttonColor', $event)" transfer />
      </FormItem>
      <FormItem label="button Hover">
        <ColorPicker :modelValue="buttonHoverColor" @update:modelValue="emit('update:buttonHoverColor', $event)" transfer />
      </FormItem>
    </div>
  </div>
</template>

<style scoped lang="scss">
.themeMenu {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  align-items: start;
  justify-items: start;
}
.sectionLabel {
  font-size: 1rem;
  font-weight: 600;
}
</style>

