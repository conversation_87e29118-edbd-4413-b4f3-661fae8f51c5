// UPDATE SiteCollection
// body { siteKey: string, siteData: SiteCollection }
import { SiteCollection } from '~/types/siteCollection'
import { useAuthenticatedFirebase } from '~/server/utils/firebaseDatabase'

export default defineEventHandler(async (event) => {
  const { siteKey, siteData } = await readBody(event)

  try {
    const { firebaseUpdate } = useAuthenticatedFirebase(event)
    await firebaseUpdate(`/siteCollections/${siteKey}`, siteData)
    return {
      ok: true,
      message: `Site collection with key: ${siteKey} updated successfully`,
    }
  } catch (error) {
    return {
      ok: false,
      errorMessage: error,
    }
  }
})
