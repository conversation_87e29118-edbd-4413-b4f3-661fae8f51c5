export type RegisterSetting = {
  registers: Register[];
};

export type Register = {
  columns: RegisterColumn[];
  datasource: string;
  name: string;
  selectedFilters: RegisterFilter[];
  linkedList: string;
  permissions: string[];
  disableActionButtons: boolean;
  pageSize: number;
};

export type RegisterColumn = {
  label: string;
  list: string;
  order: RegisterColumnOrder;
  show: string;
  sortable: boolean;
  span: number;
  type: RegisterColumnType;
};

export type RegisterFilter = {
  fieldName: string;
  operator: RegisterFilterOperator;
  type: RegisterFilterType;
  value: string;
};

export enum RegisterColumnOrder {
  ASC = "asc",
  DESC = "desc",
  NA = "na",
}

export enum RegisterColumnType {
  TEXT = "text",
  DATE = "date",
  PEOPLE = "people",
}

export enum RegisterFilterType {
  TEXT = "text",
  DATE = "date",
  PEOPLE = "people",
}

export enum RegisterFilterOperator {
  NEQ = "neq",
  EQ = "eq",
}
