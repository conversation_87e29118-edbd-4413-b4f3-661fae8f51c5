import { ref, onUnmounted } from 'vue'
import { ref as dbRef, onValue, off } from 'firebase/database'
import { getAuth, onAuthStateChanged } from 'firebase/auth'

export function useRealtimeData(path: string, onSuccess: (data: any) => void) {
  const data = ref<any | null>(null)
  const loading = ref(true)
  const error = ref<any | null>(null)

  const { $db } = useNuxtApp()
  if (!$db) {
    throw new Error(
      'Firebase database instance is not available. Ensure Firebase is properly initialized.',
    )
  }

  const firebaseRef = dbRef($db, path)
  let unsubscribe: (() => void) | null = null

  const startListener = () => {
    const callback = (snapshot: any) => {
      console.log(`[Realtime] Data received from Firebase at path: ${path}`)
      data.value = snapshot.val()
      loading.value = false
      onSuccess(data.value)
    }

    const errorCallback = (err: any) => {
      console.error('[Realtime] Firebase error:', err)
      error.value = err
      loading.value = false
    }

    onValue(firebaseRef, callback, errorCallback)

    // Save unsubscribe function
    unsubscribe = () => off(firebaseRef)
  }

  const auth = getAuth()
  const stopAuthWatcher = onAuthStateChanged(auth, (user) => {
    if (user) {
      startListener()
    } else {
      loading.value = false
      error.value = new Error('User not authenticated')
    }
    stopAuthWatcher() // Stop watching after first check
  })

  onUnmounted(() => {
    console.log(`[Realtime] Unsubscribing from Firebase updates at path: ${path}`)
    if (unsubscribe) unsubscribe()
  })

  return { data, loading, error }
}
