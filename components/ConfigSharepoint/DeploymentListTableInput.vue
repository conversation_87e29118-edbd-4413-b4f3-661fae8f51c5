<script lang="ts" setup>
  import type { PropType } from 'vue'
  import { TABLEINPUT_REQUIRED_FIELD } from '~/constants/default-values'
  import { CustomOptionType, FormFieldType, type TableInputFieldType } from '~/types/form'
  import type { SharepointList, SiteColumnField } from '~/types/sharepointConfig'

  const props = defineProps({
    list: {
      type: Object as PropType<SharepointList>,
      required: true,
    },
  })
  const sharepointAPIStore = useSharepointAPIStore()
  const sharepointConfigStore = useConfigSharepointStore()
  const { activeTableInputFields } = storeToRefs(sharepointConfigStore)
  const tableInputField = ref<TableInputFieldType>({} as TableInputFieldType)

  const formFields = ref<SharepointConfigField[]>([])
  const siteColumns = ref<SiteColumnField[]>([])
  const requiredFields = TABLEINPUT_REQUIRED_FIELD
  const isLoading = ref(false)

  const { checkFieldCanBeAdded } = useSharepointFieldUtils()

  function customOptionTypeToOption(type: CustomOptionType) {
    switch (type) {
      case CustomOptionType.TEXT:
        return FormFieldType.TEXT
      case CustomOptionType.INPUT:
        return FormFieldType.TEXTINPUT
      case CustomOptionType.INPUTNUMBER:
        return FormFieldType.NUMBERINPUT
      case CustomOptionType.SELECT:
        return FormFieldType.SELECT
      case CustomOptionType.TEXTAREA:
        return FormFieldType.TEXTAREA
      case CustomOptionType.DATEPICKER:
        return FormFieldType.DATEPICKER
      case CustomOptionType.PEOPLEPICKER:
        return FormFieldType.PEOPLEPICKER
      default:
        return null
    }
  }
  const isFieldNameNotSpecified = (fieldName: string) => {
    return fieldName.includes('FIELDNAME not found')
  }

  watch(
    activeTableInputFields,
    async () => {
      tableInputField.value =
        activeTableInputFields.value.find((field) => field.fieldName === props.list.InternalName) ||
        ({} as TableInputFieldType)
      await initialiseDeploymentList()

      console.log('Table Input Field:', tableInputField.value)
    },
    { immediate: true },
  )
  async function fetchSiteColumns() {
    siteColumns.value = await sharepointAPIStore.fetchSiteColumnsInList(props.list.DisplayName)
  }
  async function initialiseDeploymentList() {
    isLoading.value = true
    formFields.value = []
    siteColumns.value = []

    await fetchSiteColumns()
    tableInputField.value.options.forEach((option) => {
      if (!option.fieldName) {
        option.fieldName = `FIELDNAME not found, Label: ${option.name}`
      }
      const field = {
        fieldName: option.fieldName,
        label: option.name,
        type: customOptionTypeToOption(option.type),
      } as SharepointConfigField
      if (field.type === FormFieldType.TEXT) return
      formFields.value.push(field)
    })
    sortSiteColumnsToAlphabeticalOrder()
    sortFormFieldsToAlphabeticalOrder()

    isLoading.value = false
  }

  function sortSiteColumnsToAlphabeticalOrder() {
    siteColumns.value.sort((a, b) => a.InternalName.localeCompare(b.InternalName))
  }

  function sortFormFieldsToAlphabeticalOrder() {
    formFields.value.sort((a, b) => a.fieldName.localeCompare(b.fieldName))
  }

  const isSiteColumnInitiated = computed(() => {
    return siteColumns.value.length > 0
  })

  const isFieldNameInList = (fieldName: string) => {
    return siteColumns.value.some((siteColumn) => siteColumn.InternalName === fieldName)
  }

  async function clickHandlerDeployField(field: SharepointConfigField) {
    isLoading.value = true
    const newField = useCloneDeep(field)
    newField.stageName = null as any
    if (sharepointAPIStore.isFieldCanBeAdded(newField)) {
      await sharepointAPIStore.addFieldToList(
        newField as AVAILABLEFIELDTYPES,
        props.list.DisplayName,
      )
      console.log(`Field ${field.fieldName} add to List: ${props.list.DisplayName}`)
    }
    await fetchSiteColumns()
    initialiseDeploymentList()
    isLoading.value = false
  }
  async function clickHandlerDeployRequiredField(fieldName: string) {
    isLoading.value = true
    await sharepointAPIStore.addSystemFieldToList(fieldName, props.list.DisplayName)
    await fetchSiteColumns()
    initialiseDeploymentList()
    isLoading.value = false
  }

  async function clickHandlerDeployAllField() {
    isLoading.value = true

    const deployFields = [] as SharepointConfigField[]
    const deploySystemFields = [] as string[]
    requiredFields.forEach((field) => {
      if (!isFieldNameInList(field.fieldName)) {
        deploySystemFields.push(field.fieldName)
      }
    })
    formFields.value.forEach((field) => {
      if (canBeAdded(field.fieldName, field.type)) {
        if (!isFieldNameInList(field.fieldName)) {
          deployFields.push(field)
        }
      }
    })
    for (const field of deploySystemFields) {
      await sharepointAPIStore.addSystemFieldToList(field, props.list.DisplayName)
    }
    for (const field of deployFields) {
      if (sharepointAPIStore.isFieldCanBeAdded(field)) {
        if (canBeAdded(field.fieldName, field.type)) {
          console.log(`Adding Field: ${field.fieldName}`)
          await sharepointAPIStore.addFieldToList(
            field as AVAILABLEFIELDTYPES,
            props.list.DisplayName,
          )
        }
      }
    }
    console.log(deployFields, deploySystemFields)
    await initialiseDeploymentList()
    isLoading.value = false
  }

  const canBeAdded = (fieldName: string, type: FormFieldType) => {
    if (!isFieldNameInList(fieldName)) {
      return checkFieldCanBeAdded(type)
    }
    return false
  }
</script>

<template>
  <BaseSiteColumnCard is-table-input>
    <template #header>
      <div class="table-input-header">
        <Icon type="ios-grid" size="20" />
        <h6>{{ props.list.DisplayName }}</h6>
      </div>
      <Button @click="clickHandlerDeployAllField" :disabled="!isSiteColumnInitiated">Deploy</Button>
    </template>
    <div v-if="isLoading">Loading...</div>
    <div v-else-if="!isSiteColumnInitiated">Site columns not found</div>
    <div v-else class="siteColumns">
      <div v-for="field in requiredFields" class="field__deployment" :key="field.fieldName">
        <span><span class="required__indicator">*</span>{{ field.fieldName }}</span>
        <div class="icon--check" v-if="isFieldNameInList(field.fieldName)">
          <BaseIconCheck />
        </div>
        <div class="icon--add" v-if="!isFieldNameInList(field.fieldName)">
          <BaseIconAdd @click="clickHandlerDeployRequiredField(field.fieldName)" />
        </div>
      </div>
      <!-- form fields -->
      <div v-for="field in formFields" class="field__deployment">
        <span>
          {{ field.fieldName }}
        </span>
        <div class="icon--check" v-if="isFieldNameInList(field.fieldName)">
          <BaseIconCheck />
        </div>
        <div
          class="icon--add"
          v-if="
            canBeAdded(field.fieldName, field.type) && !isFieldNameNotSpecified(field.fieldName)
          ">
          <BaseIconAdd @click="clickHandlerDeployField(field)" />
        </div>
      </div>
    </div>
  </BaseSiteColumnCard>
</template>

<style lang="scss" scoped>
  .table-input-header {
    display: flex;
    align-items: center;
    gap: calc($unit/2);
  }

  .siteColumns {
    display: flex;
    flex-direction: column;
    gap: $unit;

    .field__deployment {
      max-lines: 1;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .count {
        background-color: $primary-gray;
        padding: 0 4px;
      }

      .required__indicator {
        margin-right: calc($unit/2);
        color: red;
      }
    }
    .icon--check {
      width: 1rem;
      color: green;
    }
    .icon--add {
      cursor: pointer;
      width: 1rem;
      color: red;
    }
  }
</style>
