<script setup lang="ts">
  import type { PropType } from 'vue'
  import type { Condition, ConditionOption } from '~/types/form'

  const emit = defineEmits(['update:modelValue'])
  const props = defineProps({
    modelValue: {
      type: Array as PropType<Condition[]>,
      default: () => [],
    },
    headerLabel: {
      type: String,
      default: 'Condition',
    },
  })

  const fieldStore = useFieldStore()

  const fieldNameOptions = computed(() => {
    return fieldStore.getFieldOptionForCondition()
  })

  const valueOptions = computed(() => {
    const availableField = fieldStore.getFieldOptionForCondition()
    let options: Array<string[]> = []
    props.modelValue.forEach((condition) => {
      const availableOptions =
        availableField.find((field) => field.fieldName === condition.fieldName)?.options || []
      options.push(availableOptions.map((option) => option.name))
    })
    return options
  })

  function addHandler() {
    const newModelValue = [...props.modelValue]
    newModelValue.push({ fieldName: '', value: '' })
    emit('update:modelValue', newModelValue)
  }

  function updateHandlerFieldName(option: ConditionOption, index: number) {
    const newModelValue = [...props.modelValue]
    newModelValue[index].fieldName = option.fieldName
    newModelValue[index].value = ''
    emit('update:modelValue', newModelValue)
  }

  function updateHandlerValue(option: string, index: number) {
    const newModelValue = [...props.modelValue]
    newModelValue[index].value = option
    emit('update:modelValue', newModelValue)
  }

  function isFieldNameValid(fieldName: string) {
    const availableField = fieldStore.getFieldOptionForCondition()
    return availableField.find((field) => field.fieldName === fieldName) === undefined
  }

  function clickHandlerDelete(index: number) {
    const newModelValue = [...props.modelValue]
    newModelValue.splice(index, 1)
    emit('update:modelValue', newModelValue)
  }
</script>

<template>
  <FormBuilderSpecificOptionHeader :label="headerLabel" @add="addHandler" />
  <template v-for="(condition, index) in modelValue">
    <div class="row__container">
      <BaseNumberTag>{{ index + 1 }}</BaseNumberTag>
      <div>
        <FieldInputSelect
          v-model="modelValue[index].fieldName"
          :options="fieldNameOptions"
          :noMargin="true"
          v-slot="{ option }"
          valueTag="fieldName"
          labelTag="fieldName"
          searchable
          clearable>
          <span class="fieldname">{{ option.fieldName }}</span>
        </FieldInputSelect>
        <FieldInputSelect
          :modelValue="condition.value"
          @update:model-value="updateHandlerValue($event, index)"
          :noMargin="true"
          :readonly="isFieldNameValid(condition.fieldName)"
          :options="valueOptions[index]"
          searchable
          v-slot="{ option }">
          <div>{{ option }}</div>
        </FieldInputSelect>
      </div>
      <!-- <div>
        <Select
          :modelValue="condition.fieldName"
          @update:model-value="updateHandlerFieldName($event, index)"
          transfer
          filterable>
          <Option v-for="option in fieldNameOptions" :key="option.fieldName">
            {{ option.fieldName }}
          </Option>
        </Select>
        <Select v-model="condition.value" transfer filterable>
          <Option v-for="option in valueOptions[index]" :key="option">
            {{ option }}
          </Option>
        </Select>
      </div> -->
      <BaseButtonDelete @click="clickHandlerDelete(index)" />
    </div>
  </template>
</template>

<style lang="scss" scoped>
  .conditionToAppear {
    display: flex;
    flex-direction: column;
    gap: $unit;
  }

  .row__container {
    display: grid;
    grid-template-columns: 20px 1fr 20px;
    gap: $unit;
    padding: 0 $margin $margin 0;
  }

  .option {
    &--fieldname {
      display: grid;
      grid-template-columns: 200px 1fr;
      gap: $unit;
    }
  }

  .fieldname {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }

  .stagename {
    color: $primary-gray;
  }
</style>
