import { type FormFieldData } from "~/types/form";

export const useFormBuilderSetting = () => {
  const fieldSetting = ref({} as FormFieldData);
  const formBuilderStore = useFormBuilderStore();
  const { activeField } = storeToRefs(formBuilderStore);
  const uiStore = useUiStore();

  onMounted(() => {
    initialiseFieldSetting();
  });

  watch(activeField, () => {
    initialiseFieldSetting();
  });

  function initialiseFieldSetting() {
    fieldSetting.value = useCloneDeep(activeField.value);
  }

  function resetHandler() {
    formBuilderStore.resetHandlerEditField();
  }

  function removeUndefined(fieldSettingData: object) {
    let cleanData = { ...fieldSettingData };
    Object.keys(fieldSettingData).forEach((key: string) => {
      if (fieldSettingData[key as keyof object] === undefined)
        delete cleanData[key as keyof object];
    });
    return cleanData;
  }

  const isFieldChanged = computed(() => {
    const cleanFieldSetting = removeUndefined(fieldSetting.value);
    return !isEqual(activeField.value, cleanFieldSetting);
  });

  function updateHandler(fieldSettingData: object) {
    fieldSetting.value = { ...fieldSetting.value, ...fieldSettingData };
  }

  async function saveHandler() {
    uiStore.setLoading(
      true,
      `Saving ${fieldSetting.value.fieldName ?? "Field"} Setting...`
    );
    await formBuilderStore.saveHandlerFieldSetting(fieldSetting.value);
    uiStore.setLoading(false);
  }

  return {
    resetHandler,
    updateHandler,
    isFieldChanged,
    fieldSetting,
    saveHandler,
  };
};
