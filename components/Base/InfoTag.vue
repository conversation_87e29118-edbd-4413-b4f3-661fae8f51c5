<script setup lang="ts">
import { InfoTagType } from '~/types/ui'

const props = defineProps({
  type: {
    type: String,
    required: false,
    default: InfoTagType.INFO
  }
})

const tagColor = ref('primary')

onMounted(() => {
  initialiseInfoTag()
})

watch(() => props.type, () => {
  initialiseInfoTag()
})

function initialiseInfoTag() {
  if (props.type === InfoTagType.INFO) {
    tagColor.value = '#0f6cbd'
  } else if (props.type === InfoTagType.WARNING) {
    tagColor.value = '#eaa300'
  } else if (props.type === InfoTagType.SUCCESS) {
    tagColor.value = '#13a10e'
  } else if (props.type === InfoTagType.ERROR) {
    tagColor.value = 'orange'
  }
}
</script>

<template>
  <div class="infoTag" :style="{ background: tagColor }">
    <slot />
  </div>
</template>

<style lang="scss" scoped>
.infoTag {
  font-size: 0.7rem;
  font-weight: 400;
  border-radius: $sub-unit;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 2px;
  color: white;
}
</style>