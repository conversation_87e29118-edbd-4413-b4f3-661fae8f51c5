<script setup lang="ts">
  import { ModuleTabNames } from '~/constants/tabs'

  const sharepointConfigStore = useConfigSharepointStore()
  const workflowStore = useWorkflowsStore()
  const stageStore = useStagesStore()
  const sharepointAPIStore = useSharepointAPIStore()
  const { activeWorkflow } = storeToRefs(workflowStore)
  const { workflowStages } = storeToRefs(stageStore)

  const router = useRouter()
  const route = useRoute()

  onMounted(() => {
    router.replace({
      query: {
        ...route.query,
        tab: ModuleTabNames.SHAREPOINT_CONFIG,
      },
    })
    sharepointConfigStore.initialiseSharepointConfigForWorkflow()
  })

  watch(
    () => activeWorkflow.value,
    () => {
      sharepointConfigStore.initialiseSharepointConfigForWorkflow()
    },
  )
  watch(
    () => workflowStages.value,
    () => {
      sharepointConfigStore.initialiseSharepointConfigForWorkflow()
    },
  )
</script>

<template>
  <ConfigSharepointPage />
</template>
