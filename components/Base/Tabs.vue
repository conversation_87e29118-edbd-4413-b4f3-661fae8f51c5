<script setup lang="ts">
  import { ref, reactive, provide } from 'vue'

  const props = defineProps({
    modelValue: {
      type: String,
      default: null,
    },
  })
  interface Tab {
    label: string
    name: string
  }

  const tabs = reactive<Tab[]>([])
  const selectedTab = ref<string | null>(null)

  // Function to register tabs from child TabPane components
  function registerTab(tab: Tab) {
    if (!tabs.some((t) => t.name === tab.name)) {
      tabs.push(tab)
      if (props.modelValue) {
        selectedTab.value = props.modelValue // Set the selected tab from the prop
      }
      if (selectedTab.value === null) {
        selectedTab.value = tab.name // Automatically select the first tab
      }
    }
  }

  // Function to handle tab selection
  function selectTab(name: string) {
    console.log(name)
    selectedTab.value = name
  }

  // Provide `registerTab` to child components
  provide('registerTab', registerTab)
  provide('selectedTab', selectedTab)
</script>

<template>
  <div class="fluent-tabs">
    <!-- Tab headers -->
    <div class="tab-headers">
      <button
        v-for="tab in tabs"
        :key="tab.name"
        :class="{ active: selectedTab === tab.name }"
        class="tab-header"
        @click="selectTab(tab.name)">
        {{ tab.label }}
      </button>
    </div>

    <!-- Tab content -->
    <div class="tab-content">
      <slot :selected-tab="selectedTab" />
    </div>
  </div>
</template>

<style scoped lang="scss">
  .fluent-tabs {
    display: flex;
    flex-direction: column;
    width: 100%;
    padding: $padding;
  }

  .tab-headers {
    display: flex;
    justify-content: flex-start;
    gap: 15px;
    margin-bottom: $padding;
  }

  .tab-header {
    text-align: center;
    cursor: pointer;
    background: none;
    border: none;
    border-bottom: 3px solid transparent;
    transition: all 0.3s ease;
    white-space: nowrap;
    padding-bottom: 0.5rem;
    font-size: 0.9rem;
    color: #515a6e;
  }

  .tab-header:hover {
    color: $primary-blue;
  }

  .tab-header.active {
    color: black;
    border-bottom: 3px solid $primary-blue;
  }
</style>
