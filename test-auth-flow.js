import crypto from 'crypto'
import { ConfidentialClientApplication } from '@azure/msal-node'

// This script tests the entire authentication flow
// Run with: node test-auth-flow.js

// Replace these with your actual values or use environment variables
const TENANT_ID = process.env.TENANT_ID || 'your-tenant-id'
const TENANT_URL = process.env.TENANT_URL || 'https://yourtenant.sharepoint.com'
const CLIENT_ID = process.env.SHAREPOINT_CLIENT_ID || '0c6e6a75-2629-4285-b8a6-d64908bbad5a'
const WORKBENCH_KEY = process.env.WORKBENCH_KEY

async function testAuthFlow() {
  if (!WORKBENCH_KEY) {
    console.error('WORKBENCH_KEY environment variable is not set')
    return
  }

  try {
    // Create private key from raw key
    const privateKeyObject = crypto.createPrivateKey({
      key: WORKBENCH_KEY.replace(/\\n/g, '\n'),
      format: 'pem',
    })

    const privateKey = privateKeyObject.export({
      format: 'pem',
      type: 'pkcs8',
    })

    // Configure the client application
    const config = {
      auth: {
        authority: `https://login.microsoftonline.com/${TENANT_ID}`,
        clientId: CLIENT_ID,
        clientCertificate: {
          thumbprint: '5D656A5A81A048E13DBCAD1BDF6C7365851BD190',
          privateKey: privateKey,
        },
      },
    }

    console.log('Creating ConfidentialClientApplication...')
    const cca = new ConfidentialClientApplication(config)

    console.log('Acquiring token...')
    const result = await cca.acquireTokenByClientCredential({
      scopes: [`${TENANT_URL}/.default`],
    })

    if (result) {
      console.log('✅ Authentication successful!')
      console.log('Token acquired successfully')
      // Only show the first few characters of the token for security
      console.log('Access token (first 10 chars):', result.accessToken.substring(0, 10) + '...')
      console.log('Token expires:', new Date(result.expiresOn).toLocaleString())
    } else {
      console.error('❌ Failed to acquire token')
    }
  } catch (error) {
    console.error('❌ Error during authentication:', error)
  }
}

testAuthFlow().catch(console.error)
