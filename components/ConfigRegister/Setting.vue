<script setup lang="ts">
  import type { Register } from '~/types/register'

  import type { Option } from '~/types'
  import ConfigRegisterFilterForm from './FilterForm.vue'

  const uiStore = useUiStore()
  const registerStore = useConfigRegisterStore()
  const { activeRegister } = storeToRefs(registerStore)

  const registerSetting = ref({} as Register)
  const deleteModal = ref(false)
  const fieldStore = useFieldStore()
  const isLoading = ref(false)

  const formFilter = ref<InstanceType<typeof ConfigRegisterFilterForm> | null>(null)
  const datasourceOption = computed(() => registerStore.getListOptions())
  const linkedListOption = computed(() => registerStore.getLinkedListOptions())
  const permissionsOption = computed(() =>
    fieldStore.getActiveSiteCollectionGroups().map((group) => ({
      label: group,
      value: group,
    })),
  )

  const sharepointAPIStore = useSharepointAPIStore()

  const isDatasourceEmpty = computed(() => !!registerSetting.value.datasource)
  const isListOptionEmpty = computed(() => !datasourceOption.value.length)

  const itemToDisplayOption = [25, 50, 75, 100]
  const fieldNameOption = computed(() => availableListFields.value)

  const availableListFields = ref<Option[]>([])
  const isNotRegisterEmpty = computed(() => {
    return !isEqual(registerSetting.value, {})
  })

  watch(
    () => activeRegister.value,
    async () => {
      isLoading.value = true
      await initialiseRegisterSetting()
      isLoading.value = false
    },
  )

  watch(
    () => registerSetting.value.datasource,
    async () => {
      isLoading.value = true
      await initialiseColumnToShowOption()
      isLoading.value = false
    },
  )

  watch(
    () => registerSetting.value.linkedList,
    async () => {
      isLoading.value = true
      await initialiseColumnToShowOption()
      isLoading.value = false
    },
  )

  async function initialiseColumnToShowOption() {
    if (isListOptionEmpty.value) {
      availableListFields.value = []
      isLoading.value = false
      return
    }
    if (registerSetting.value.datasource) {
      if (sharepointAPIStore.checkListExist(registerSetting.value.linkedList)) {
        availableListFields.value = await registerStore.getColumnToShowOption(
          registerSetting.value.datasource,
          registerSetting.value.linkedList,
        )
      } else {
        availableListFields.value = await registerStore.getColumnToShowOption(
          registerSetting.value.datasource,
        )
      }
      sortColumnToShowOptionToAlphabeticalOrder()
    } else if (registerSetting.value.linkedList) {
    } else {
      availableListFields.value = []
    }
  }

  async function initialiseRegisterSetting() {
    registerSetting.value = useClone(activeRegister.value)
    await initialiseColumnToShowOption()
  }

  function clickResetHandler() {
    registerSetting.value = useCloneDeep(activeRegister.value)
    formFilter.value?.resetValueOptions()
  }

  async function clickHandlerSave() {
    uiStore.setLoading(true, 'Saving register...')
    await registerStore.updateRegister(registerSetting.value)
    uiStore.setLoading(false)
  }

  function clickHandlerDelete() {
    deleteModal.value = !deleteModal.value
  }

  async function deleteHandler() {
    uiStore.setLoading(true, 'Deleting register...')
    await registerStore.deleteRegister()
    uiStore.setLoading(false)
  }

  function updateHandlerDatasource(value: string) {
    registerSetting.value.datasource = value
  }

  function updateHandlerLinkedList(value: string) {
    registerSetting.value.linkedList = value
  }

  function sortColumnToShowOptionToAlphabeticalOrder() {
    availableListFields.value.sort((a, b) => a.value.localeCompare(b.value))
  }

  async function clickHandlerDuplicate() {
    uiStore.setLoading(true, 'Duplicating register...')
    await registerStore.duplicateRegister()
    uiStore.setLoading(false)
  }
</script>

<template>
  <Modal v-model="deleteModal" title="Delete Register" @on-ok="deleteHandler">
    <p>Are you sure you want to delete {{ activeRegister.name }}?</p>
  </Modal>
  <BaseLoadingOverlay v-if="isLoading" />
  <Form>
    <Card dis-hover class="card__register">
      <template v-if="!isNotRegisterEmpty">
        <p>Add new register to start editing</p>
      </template>
      <template v-else>
        <FieldInputSwitch
          v-model="registerSetting.disableActionButtons"
          label="Disable Action Buttons" />
        <FieldInput label="Default Item to Show">
          <Select v-model="registerSetting.pageSize" class="select__item-to-display" transfer>
            <Option v-for="item in itemToDisplayOption" :value="item" :key="item">{{
              item
            }}</Option>
          </Select>
        </FieldInput>
        <FieldInputText v-model="registerSetting.name" label="Name" />
        <BaseWarning v-if="isListOptionEmpty">No list available</BaseWarning>
        <FieldInput label="List Name">
          <Select
            :model-value="registerSetting.datasource"
            @update:model-value="updateHandlerDatasource"
            clearable
            transfer>
            <Option v-for="option in datasourceOption" :value="option.value" :key="option.value">
              {{ option.label }}
            </Option>
          </Select>
        </FieldInput>
        <FieldInput label="Linked List">
          <Select
            :model-value="registerSetting.linkedList"
            @update:model-value="updateHandlerLinkedList"
            clearable>
            <Option v-for="option in linkedListOption" :value="option.value" :key="option.value">
              {{ option.label }}
            </Option>
          </Select>
        </FieldInput>
        <FieldInput label="Permissions">
          <Select multiple v-model="registerSetting.permissions" transfer>
            <Option v-for="group in permissionsOption" :value="group.value">{{
              group.label
            }}</Option>
          </Select>
        </FieldInput>
        <BaseWarning v-if="!isDatasourceEmpty">No datasource selected</BaseWarning>
        <ConfigRegisterColumnToShowForm
          v-model="registerSetting.columns"
          :fieldNameOptions="fieldNameOption" />
        <ConfigRegisterFilterForm ref="formFilter" v-model="registerSetting.selectedFilters" />
      </template>
    </Card>
    <div class="settingButton">
      <div>
        <BaseButtonSaveReset
          @reset="clickResetHandler"
          @save="clickHandlerSave"
          v-if="isNotRegisterEmpty" />
        <BaseButton @click="clickHandlerDuplicate" class="deleteButton">
          Duplicate Register
        </BaseButton>
      </div>
      <BaseButtonDeleteText
        @click="clickHandlerDelete"
        text="Delete Register"
        class="deleteButton" />
    </div>
  </Form>
</template>

<style lang="scss" scoped>
  .card__register {
    height: calc(100vh - 260px);
    overflow: auto;
    padding-bottom: $margin * 12;
  }

  .select__item-to-display {
    width: 35%;
    margin-bottom: $margin;
  }

  .settingButton {
    width: 100%;
    display: flex;
    justify-content: space-between;

    div {
      display: flex;
      gap: $padding;
    }

    .deleteButton {
      margin-top: $margin;
    }
  }
</style>
