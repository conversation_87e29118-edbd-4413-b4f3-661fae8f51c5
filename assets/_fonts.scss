@import "./_variables.scss";

body {
  font-family: $fluentui-font-stack;
  font-size: 1rem; // 16px
  font-weight: 400;
  line-height: 1.5;
}

.body--small {
  font-size: 0.75rem; // 12px
}

h1 {
  font-size: 2.5rem; // 50px
  text-transform: uppercase;
}

h2 {
  font-size: 3rem; // 48px
}

h3 {
  font-size: 1.5rem; // 32px
  font-weight: 500;
  margin: 0;
}

h4 {
  font-family: $secondary-font-stack;
  font-size: 1.25rem; // 16px
  font-weight: 100;
}

h5 {
  text-transform: uppercase;
  font-size: 1.3rem; // 12px
}

h6 {
  font-size: 0.75rem; // 12px
}

label {
  font-size: 1rem; // 16px
}

strong {
  font-weight: 700;
}

span {
  font-size: 0.9rem;
  font-weight: 400;
}

li {
  font-size: 0.9rem;
  font-weight: 500;
}
