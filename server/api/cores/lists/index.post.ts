// UPDATE Core Site Columns
// params = { coreKey: string }
// body = CoreSiteColumn[]

import { CoreList } from '~/types/core'
import { useAuthenticatedFirebase } from '~/server/utils/firebaseDatabase'

export default defineEventHandler(async (event) => {
  const { coreKey, lists } = await readBody(event)

  if (!coreKey || !lists) {
    return {
      ok: false,
      errorMessage: 'Missing coreKey or lists',
    }
  }

  try {
    const { firebaseSet } = useAuthenticatedFirebase(event)
    const data = await firebaseSet(`/cores/${coreKey}/lists`, lists)
    return {
      ok: true,
      data,
      message: 'Core Lists updated successfully',
    }
  } catch (error) {
    return {
      ok: false,
      errorMessage: 'Error updating lists.',
    }
  }
})
