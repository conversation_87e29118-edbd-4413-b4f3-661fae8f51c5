<script setup lang="ts">
const formsStore = useFormStore();
const { activeForm } = storeToRefs(formsStore);


const emit = defineEmits(["close"]);
function closeHandler()
{
    emit("close");
}

</script>

<template>
    <Drawer width="800" :model-value="true" :title="activeForm.name" :mask-closable="false" @on-close="closeHandler">
        <div class="header">
            <div>Date Created</div>
            <div>Version</div>
            <div>Set Active</div>
            <div>Status</div>
        </div>
        <FormDrawerFormConfigVersionRegister />
    </Drawer>
</template>

<style lang="scss" scoped>
.header {
    display: grid;
    grid-template-columns: 25% 25% 25% 25%;
    background-color: $table-background;
    color: $table-font-color;
    font-weight: bold;
    padding: $padding;
}
</style>