<script setup lang="ts">
  import { type Form } from '~/types/data'
  import type { SiteCollection } from '~/types/siteCollection'

  const props = defineProps({
    site: {
      type: Object as PropType<SiteCollection>,
      required: true,
    },
  })

  const route = useRoute()
  const formStore = useFormStore()
  const siteForms = computed(() => {
    return formStore.getFormFromKeyArray(props.site.forms || ([] as Form[]))
  })

  function getToUrl(formKey: string) {
    return '/tenants/' + props.site.tenant + '/' + props.site.key + '/form/' + formKey
  }
</script>

<template>
  <div class="siderMenuForms">
    Form
    <template v-for="form in siteForms">
      <NuxtLink :to="getToUrl(form.key)">
        <div class="formLink" :class="{ active: form.key === route.params.key }">
          {{ form.name }}
        </div>
      </NuxtLink>
    </template>
  </div>
</template>

<style lang="scss" scoped>
  .siderMenuForms {
    display: flex;
    flex-direction: column;
    margin-left: 22px;
  }

  .formLink {
    padding: $sub-unit;
    padding-left: $unit;
    cursor: pointer;
    font-size: 0.875rem;
    font-weight: 400;
    color: #161616;

    &:hover {
      background-color: $sider-hover-color;
      text-decoration: underline;
    }
  }

  .active {
    background-color: $sider-hover-color;
    color: black;
  }
</style>
