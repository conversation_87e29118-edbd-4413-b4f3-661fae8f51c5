<script setup lang="ts">
  import { SiteCollectionTabNames } from '~/constants/tabs'
  import type { SiteCollectionTheme } from '~/types/siteCollection'
  import { useSetRouteTab } from '~/composables/useSetRouteTab'
  import { DEFAULT_THEME } from '~/constants/default-values'

  const formTheme = ref<SiteCollectionTheme>(DEFAULT_THEME as SiteCollectionTheme)

  const siteCollectionsStore = useSiteCollectionsStore()
  const { activeSiteCollection } = storeToRefs(siteCollectionsStore)

  const { setTab } = useSetRouteTab()

  onMounted(() => setTab(SiteCollectionTabNames.THEME))

  const { submitHandler, resetHandler } = useSettingForm<SiteCollectionTheme>({
    form: formTheme,
    activeData: activeSiteCollection,
    updateHandler: async () => {
      await siteCollectionsStore.updateSiteCollection(
        activeSiteCollection.value.key,
        formTheme.value,
      )
    },
  })
</script>

<template>
  <BaseCardPanel>
    <template #title>Theme settings</template>

    <Form :label-width="150" label-position="left">
      <SiteCollectionTabThemeMenuSectionMain
        v-model:primaryColor="formTheme.primaryColor"
        v-model:secondaryColor="formTheme.secondaryColor"
        v-model:accentColor="formTheme.accentColor" />

      <SiteCollectionTabThemeMenuSectionButton
        v-model:buttonColor="formTheme.buttonColor"
        v-model:buttonHoverColor="formTheme.buttonHoverColor" />

      <SiteCollectionTabThemeMenuSectionLink
        v-model:linkColor="formTheme.linkColor"
        v-model:linkBackgroundColor="formTheme.linkBackgroundColor"
        v-model:linkActiveTextColor="formTheme.linkActiveTextColor" />

      <SiteCollectionTabThemeMenuSectionMenu
        v-model:menuGroupTitleColor="formTheme.menuGroupTitleColor"
        v-model:submenuTitleColor="formTheme.submenuTitleColor" />

      <SiteCollectionTabThemeMenuSectionDashboard
        v-model:dashboardCardH1Color="formTheme.dashboardCardH1Color"
        v-model:dashboardCardH4Color="formTheme.dashboardCardH4Color" />
    </Form>

    <BaseButtonSaveReset @reset="resetHandler" @save="submitHandler" />
  </BaseCardPanel>
</template>
