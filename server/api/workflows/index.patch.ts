// UPDATE Workflow
// body { workflowKey: string, workflowData: Workflow }

import { Workflow } from '~/types/workflow'
import { useAuthenticatedFirebase } from '~/server/utils/firebaseDatabase'

export default defineEventHandler(async (event) => {
  const { workflowKey, workflowData } = await readBody(event)

  if (!workflowData || !workflowKey) {
    return {
      ok: false,
      errorMessage: `workflowKey and workflowData are required`,
    }
  }
  try {
    const { firebaseUpdate } = useAuthenticatedFirebase(event)
    await firebaseUpdate(`/workflows/${workflowKey}`, workflowData)
    return {
      ok: true,
      message: `Sucessfully update workflow with key ${workflowKey}`,
    }
  } catch (error) {
    return {
      ok: false,
      errorMessage: error,
    }
  }
})
