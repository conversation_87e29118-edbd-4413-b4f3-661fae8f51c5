// CREATE NEW SiteCollection
// body { siteCollectionData: string }

import { useAuthenticatedFirebase } from '~/server/utils/firebaseDatabase'
import { SiteCollection } from '~/types/siteCollection'

export default defineEventHandler(async (event) => {
  const { siteCollectionData } = await readBody(event)

  try {
    const { firebasePush } = useAuthenticatedFirebase(event)
    const newSiteCollectionKey = await firebasePush(`/siteCollections`, siteCollectionData)
    return {
      ok: true,
      message: `Site collection created successfully with key: ${newSiteCollectionKey}`,
      data: newSiteCollectionKey,
    }
  } catch (error) {
    return {
      ok: false,
      errorMessage: error,
    }
  }
})
