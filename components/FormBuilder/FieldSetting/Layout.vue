<template>
    <div class="layoutFieldSetting">
        <div class="fieldSetting">
            <slot name="field" />
        </div>
    </div>
</template>

<style lang="scss" scoped>
.layoutFieldSetting {
    display: flex;
    flex-direction: column;
    gap: $unit;
    position: relative;
    height: calc(100vh - 420px);
    font-size: 10px;
}

.fieldSetting {
    overflow: auto;
    height: calc(100vh - 420px);
    padding-right: $margin;
}
</style>