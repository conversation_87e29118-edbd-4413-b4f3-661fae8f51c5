import type { UpVersionSetting } from '~/types/workflow'

enum UpVersionFor {
  WORKFLOW = 'workflow',
  FORM = 'form',
}

export const useUpVersionStore = defineStore('upVersion', () => {
  const workflowStore = useWorkflowsStore()
  const { moduleWorkflowByDateCreated, activeWorkflow } = storeToRefs(workflowStore)
  const formConfigStore = useFormConfigStore()
  const { formFormConfigByDateCreated, activeFormConfig } = storeToRefs(formConfigStore)
  const stageStore = useStagesStore()
  const { workflowStages, stagesByWorkflowKey } = storeToRefs(stageStore)

  const upVersionFor = ref<UpVersionFor>()
  const activeVersions = ref<UpVersionSetting[]>([])
  const activeUpVersionSetting = ref({} as UpVersionSetting)
  const modalSelectedIndex = ref()

  const initialiseUpVersionForWorkflow = () => {
    upVersionFor.value = UpVersionFor.WORKFLOW
  }

  watch(moduleWorkflowByDateCreated, () => {
    activeVersions.value = useCloneDeep(moduleWorkflowByDateCreated.value)
    activeUpVersionSetting.value = useCloneDeep(activeWorkflow.value)
  })

  const initialiseUpVersionForForm = () => {
    upVersionFor.value = UpVersionFor.FORM
  }

  watch(formFormConfigByDateCreated, () => {
    activeVersions.value = useCloneDeep(formFormConfigByDateCreated.value)
    activeUpVersionSetting.value = useCloneDeep(activeFormConfig.value)
  })

  async function updateHandler(index: number) {
    const selectedItem = activeVersions.value[index]
    selectedItem.inUse = true

    if (upVersionFor.value === UpVersionFor.WORKFLOW)
      await workflowStore.setInUseWorkflow(selectedItem.key)
    else if (upVersionFor.value === UpVersionFor.FORM)
      await formConfigStore.setInUseFormConfig(selectedItem.key)
  }

  async function deleteHandler() {
    if (upVersionFor.value === UpVersionFor.WORKFLOW) {
      const selectedWorkflow = activeVersions.value[modalSelectedIndex.value]
      const selectedWorkflowStages = stagesByWorkflowKey.value[selectedWorkflow.key]
      if (selectedWorkflowStages) {
        await stageStore.deleteStageBulk(selectedWorkflowStages.map((stage) => stage.firebaseKey))
      }
      await workflowStore.deleteWorkflow(selectedWorkflow.key)
    }
  }

  function setSelectedIndex(index: number) {
    modalSelectedIndex.value = index
  }

  async function upVersionHandler(data: UpVersionSetting) {
    if (upVersionFor.value === UpVersionFor.WORKFLOW) {
      const newWorkflowKey = await workflowStore.upVersionWorkflow(activeWorkflow.value.key, data)
      if (!newWorkflowKey) return
    }
  }

  return {
    activeUpVersionSetting,
    activeVersions,
    setSelectedIndex,
    updateHandler,
    deleteHandler,
    modalSelectedIndex,
    upVersionHandler,
    initialiseUpVersionForWorkflow,
    initialiseUpVersionForForm,
  }
})
