<script setup lang="ts">
const emit = defineEmits(["click"])
const workflowsStore = useWorkflowsStore()
const { activeWorkflow } = storeToRefs(workflowsStore)
const indicatorStore = useIndicatorStore()
const isWorkflowHasNoInUse = computed(() => indicatorStore.getNoInUseWorkflowIndicator())

function handleClick() {
    emit("click")
}

</script>

<template>
    <div class="layoutHeaderModule">
        <div>
            <AppBreadCrumbsText />
        </div>
        <div class="versionButton">
            <Tooltip content="No Workflow is in use" transfer>
                <div v-if="isWorkflowHasNoInUse" class="warningIndicator">
                    <Icon type="md-help" />
                </div>
            </Tooltip>
            <h6 @click="handleClick">
                {{ activeWorkflow.version }}
            </h6>
        </div>

    </div>
</template>

<style lang="scss" scoped>
h6 {
    color: $primary-lightblue;

    &:hover {
        cursor: pointer;
        color: $primary-blue ;
    }
}

.layoutHeaderModule {
    display: grid;
    grid-auto-flow: column;
    width: auto;
    justify-content: space-between;
    align-items: baseline;
    margin-right: $margin;
}

.versionButton {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    gap: $padding;
}

.warningIndicator {
    color: white;
    background-color: #f7630c;
    border-radius: 100%;
    width: 24px;
    height: 24px;
    align-items: center;
    display: flex;
    justify-content: center;
}
</style>