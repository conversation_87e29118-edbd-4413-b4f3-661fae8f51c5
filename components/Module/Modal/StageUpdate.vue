<script setup lang="ts">
const uiStore = useUiStore();
const { stageUpdateModal, stageModalAction, stageUpdateModalOnOk } = storeToRefs(uiStore);
</script>

<template>
    <Modal title="Update Stage" v-model="stageUpdateModal" @on-ok="stageUpdateModalOnOk" @on-cancel="return">
        <p v-if="stageModalAction === StageUpdateAction.ADD_STAGE">Add Stage</p>
        <p v-if="stageModalAction === StageUpdateAction.UPDATE_STAGE">Update Stage</p>
        <p v-if="stageModalAction === StageUpdateAction.DELETE_STAGE">Delete Stage</p>
        <p v-if="stageModalAction === StageUpdateAction.STAGE_ORDER_UPPDATE">Order Stage</p>
        <p v-if="stageModalAction === StageUpdateAction.COPY_STAGE">Copy Stage</p>
    </Modal>
</template>