<script setup lang="ts" generic="T">
  const props = defineProps({
    label: {
      type: String,
      default: '',
    },
    modelValue: {
      type: String,
    },
    placeholder: {
      type: String,
      default: 'Please select an option',
    },
    options: {
      type: Array<T>,
      default: [],
    },
    readonly: {
      type: Boolean,
      default: false,
    },
    id: {
      type: String,
      default: '',
    },
    noMargin: {
      type: Boolean,
      default: false,
    },
    valueTag: {
      type: String,
      default: '',
    },
    labelTag: {
      type: String,
      default: '',
    },
    searchable: {
      type: Boolean,
      default: true,
    },
    clearable: {
      type: Boolean,
      default: false,
    },
  })

  // Note: Don't provide valueTag and labelTag if using modelValue and @update:model-value on this component :)
  const emit = defineEmits(['update:modelValue'])
  const dropdownActive = ref(false)
  const filterText = ref('')
  const searchInputRef = ref<HTMLInputElement>()
  const dropdownRef = ref<HTMLElement>()

  // Debounced search to improve performance
  const debouncedFilterText = refDebounced(filterText, 300)

  // Computed filtered options based on search
  const filteredOptions = computed(() => {
    if (!props.searchable || !debouncedFilterText.value.trim()) {
      return props.options
    }

    const searchTerm = debouncedFilterText.value.toLowerCase().trim()

    return props.options.filter((option) => {
      if (props.labelTag) {
        const labelProp = props.labelTag as keyof T
        const label = String(option[labelProp]).toLowerCase()
        return label.includes(searchTerm)
      } else {
        return String(option).toLowerCase().includes(searchTerm)
      }
    })
  })

  function clickHandlerSelect(option: T) {
    if (props.valueTag !== '') {
      const prop = props.valueTag as keyof T
      emit('update:modelValue', option[prop])
    } else {
      emit('update:modelValue', option)
    }
    dropdownActive.value = false
    filterText.value = ''
  }

  function clickHandler(event: MouseEvent) {
    if (props.readonly) return
    const element = event.target as HTMLElement
    if (element.classList.contains('filterInput')) {
      dropdownActive.value = true
    } else {
      dropdownActive.value = !dropdownActive.value
    }

    if (dropdownActive.value && props.searchable) {
      nextTick(() => {
        searchInputRef.value?.focus()
      })
    }
  }

  function handleClear() {
    if (props.clearable && !props.readonly) {
      emit('update:modelValue', '')
      filterText.value = ''
    }
  }

  function findModelValueLabel() {
    const valueTag = props.valueTag as keyof T
    const labelTag = props.labelTag as keyof T
    const option = props.options.find((option) => option[valueTag] === props.modelValue)
    return option ? option[labelTag] : props.modelValue
  }

  // Close dropdown when clicking outside
  onClickOutside(dropdownRef, () => {
    dropdownActive.value = false
    filterText.value = ''
  })

  // Handle keyboard navigation
  function handleKeydown(event: KeyboardEvent) {
    if (event.key === 'Escape') {
      dropdownActive.value = false
      filterText.value = ''
    }
  }

  const isTagGiven = computed(() => props.labelTag !== '' && props.valueTag !== '')
</script>

<template>
  <FieldInputWrapper :noMargin="noMargin">
    <template #label>
      <div v-if="label">{{ label }}</div>
    </template>
    <div
      ref="dropdownRef"
      class="dropdown"
      :class="{
        active: dropdownActive,
        readonly: readonly,
        'has-value': modelValue,
      }"
      @click="clickHandler"
      @keydown="handleKeydown"
      tabindex="0">
      <div class="select">
        <!-- Search input when dropdown is active -->
        <div v-if="dropdownActive && searchable" class="search-container">
          <input
            ref="searchInputRef"
            :id="id"
            class="filterInput"
            type="text"
            :placeholder="searchable ? 'Search options...' : placeholder || 'Select'"
            v-model="filterText"
            @click.stop />
          <Icon type="ios-search" class="search-icon" />
        </div>

        <!-- Display value when dropdown is closed -->
        <div class="value" v-show="!dropdownActive">
          <span class="value-text" :class="{ placeholder: !modelValue }">
            <template v-if="modelValue">
              <template v-if="isTagGiven">
                {{ findModelValueLabel() }}
              </template>
              <template v-else>
                {{ modelValue }}
              </template>
            </template>
            <template v-else>
              {{ placeholder }}
            </template>
          </span>
        </div>

        <!-- Action icons -->
        <div class="icons">
          <Icon
            v-if="clearable && modelValue && !readonly"
            type="ios-close-circle"
            class="clear-icon"
            @click.stop="handleClear" />
          <BaseIconChevronDown class="chevron-icon" :class="{ rotated: dropdownActive }" />
        </div>
      </div>

      <!-- Dropdown menu -->
      <Transition name="dropdown-fade">
        <div v-if="dropdownActive" class="dropdown__menu">
          <div v-if="filteredOptions.length === 0" class="dropdown__menu__empty">
            <Icon type="ios-search" />
            <span>No options found</span>
          </div>
          <template v-else>
            <div
              v-for="(option, index) in filteredOptions"
              :key="index"
              class="dropdown__menu__item"
              @click="clickHandlerSelect(option)">
              <slot :option="option">
                <template v-if="isTagGiven">
                  {{ option[labelTag as keyof T] }}
                </template>
                <template v-else>
                  {{ option }}
                </template>
              </slot>
            </div>
          </template>
        </div>
      </Transition>
    </div>
  </FieldInputWrapper>
</template>

<style lang="scss" scoped>
  $field-height: 36px;
  $border-radius: 6px;
  $box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  $box-shadow-focus: 0 0 0 2px rgba(45, 140, 240, 0.2);
  $primary-input-hover: #f8f9fa;
  $primary-input-active: #e9ecef;
  $border-color: #dcdee2;
  $border-color-hover: #c5c8ce;
  $border-color-focus: #2d8cf0;

  // Search input styling
  .search-container {
    position: relative;
    width: 100%;
    display: flex;
    align-items: center;

    .filterInput {
      border: none;
      width: 100%;
      height: 100%;
      background: transparent;
      font-size: 14px;
      color: $primary-text;
      padding-right: 24px;

      &:focus {
        outline: none;
      }

      &::placeholder {
        color: #c5c8ce;
        font-style: italic;
      }
    }

    .search-icon {
      position: absolute;
      right: 0;
      height: 16px;
      width: 16px;
      color: #c5c8ce;
      pointer-events: none;
    }
  }

  // Value display styling
  .value {
    flex: 1;
    height: 100%;
    display: flex;
    align-items: center;

    .value-text {
      font-size: 14px;
      color: $primary-text;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;

      &.placeholder {
        color: #c5c8ce;
        font-style: italic;
      }
    }
  }

  // Icons container
  .icons {
    display: flex;
    align-items: center;
    gap: 4px;
    margin-left: 8px;

    .clear-icon {
      height: 16px;
      width: 16px;
      color: #c5c8ce;
      cursor: pointer;
      transition: color 0.2s ease;

      &:hover {
        color: #999;
      }
    }

    .chevron-icon {
      height: 16px;
      width: 16px;
      color: #c5c8ce;
      transition: transform 0.3s ease;

      &.rotated {
        transform: rotate(180deg);
      }
    }
  }

  // Main dropdown container
  .dropdown {
    min-width: 100%;
    height: $field-height;
    display: inline-block;
    background-color: $primary-white;
    border-radius: $border-radius;
    transition: all 0.2s ease;
    position: relative;
    border: 1px solid $border-color;
    font-family: $fluentui-font-stack;

    .select {
      cursor: pointer;
      display: flex;
      align-items: center;
      height: $field-height;
      padding: 0 12px;
      border-radius: $border-radius;
    }

    // Hover state
    &:hover:not(.readonly) {
      border-color: $border-color-hover;
      box-shadow: $box-shadow;
    }

    // Active/focus state
    &.active:not(.readonly) {
      border-color: $border-color-focus;
      box-shadow: $box-shadow-focus;
    }

    // Readonly state
    &.readonly {
      background-color: #f8f9fa;
      cursor: not-allowed;

      .select {
        cursor: not-allowed;
      }
    }

    // Focus outline for accessibility
    &:focus-visible {
      outline: 2px solid $border-color-focus;
      outline-offset: 2px;
    }
  }

  // Dropdown menu
  .dropdown__menu {
    position: absolute;
    background: $primary-white;
    width: 100%;
    margin-top: 2px;
    border-radius: $border-radius;
    overflow: hidden;
    overflow-y: auto;
    z-index: 9999; // High z-index to ensure it appears above other elements
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    max-height: 240px;
    border: 1px solid $border-color;

    &__item {
      padding: 10px 12px;
      transition: background-color 0.15s ease;
      cursor: pointer;
      font-size: 14px;
      color: $primary-text;
      border-bottom: 1px solid #f5f5f5;

      &:last-child {
        border-bottom: none;
      }

      &:hover {
        background-color: $primary-input-hover;
      }

      &:active {
        background-color: $primary-input-active;
      }
    }

    &__empty {
      padding: 16px 12px;
      text-align: center;
      color: #999;
      font-style: italic;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 8px;

      svg {
        height: 24px;
        width: 24px;
        opacity: 0.5;
      }
    }
  }

  // Transition animations
  .dropdown-fade-enter-active,
  .dropdown-fade-leave-active {
    transition: all 0.2s ease;
  }

  .dropdown-fade-enter-from {
    opacity: 0;
    transform: translateY(-8px);
  }

  .dropdown-fade-leave-to {
    opacity: 0;
    transform: translateY(-8px);
  }

  // Responsive adjustments
  @media (max-width: 768px) {
    .dropdown__menu {
      max-height: 200px;
    }
  }
</style>
