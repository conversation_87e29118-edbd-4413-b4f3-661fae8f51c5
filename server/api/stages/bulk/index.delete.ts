export default defineEventHandler(async (event) => {
  const { stageKeys } = await readBody(event)
  const { db } = useFirebaseAdmin()
  if (!stageKeys) {
    return {
      ok: false,
      errorMessage: `stageKeys is required`,
    }
  }

  try {
    const updates: { [key: string]: null } = {}

    stageKeys.forEach(async (stageKey: string) => {
      updates[`stages/${stageKey}`] = null
    })
    await db.ref().update(updates)
    return {
      ok: true,
      message: `Successfully deleted ${stageKeys.length} stages`,
    }
  } catch (error) {
    return {
      ok: false,
      errorMessage: error instanceof Error ? error.message : String(error),
    }
  }
})
