import { defineStore } from 'pinia'
import {
  type Workflow,
  type Action,
  type NewWorkflowAction,
  type WorkflowAction,
  type Report,
  type UpVersionSetting,
} from '../types/workflow'
import { useStoreUtils } from './utils/storeFetchUtils'
import type { ServerResponse } from '~/types/api'
import { useApiMutation } from './utils/apiMutation'

export type RefactoredActionWorkflow = {
  name: string
  actions: Action[]
}

export const useWorkflowsStore = defineStore('workflows', () => {
  const uiStore = useUiStore()
  const { convertFirebaseDataToArray } = useStoreUtils()
  const moduleStore = useModulesStore()
  const { activeModule } = storeToRefs(moduleStore)
  const stageStore = useStagesStore()
  const originalActionWorkflow = ref([] as NewWorkflowAction[])

  const activeWorkflowReports = ref({} as Report)
  const activeReportIndex = ref(0)

  const workflows = ref([] as Workflow[])
  const activeWorkflowKey = ref<string>('')
  const moduleWorkflowByDateCreated = computed(() => getWorkflowByDateCreated())
  const workflowsByModuleKey = computed(() => useGroupBy(workflows.value, 'moduleKey'))
  const workflowsByTenant = computed(() => useGroupBy(workflows.value, 'tenant'))
  const workflowsByKey = computed(() => useKeyBy(workflows.value, 'key'))
  const activeWorkflow = computed(
    () => workflowsByKey.value[activeWorkflowKey.value] || ({} as Workflow),
  )

  // ----------------- Callbacks -----------------
  function workflowsCallback(firebaseWorkflowData: FirebaseData<Partial<Workflow>>): void {
    workflows.value = convertFirebaseDataToArray(firebaseWorkflowData)
  }

  const getWorkflowByDateCreated = () => {
    if (!activeModule.value.key) return []
    if (!workflows.value) return []

    const moduleWorkflows = workflowsByModuleKey.value[activeModule.value.key]
    if (!moduleWorkflows) return []
    return moduleWorkflows.sort((a, b) => {
      return new Date(b.created).getTime() - new Date(a.created).getTime()
    })
  }

  const getModuleWorkflows = async (moduleKey: string) => {
    return workflows.value.filter((workflow: Workflow) => workflow.moduleKey === moduleKey)
  }

  const getTenantWorkflows = async (tenant: string) => {
    return workflows.value.filter((workflow: Workflow) => workflow.tenant === tenant)
  }

  const getInUseWorkflow = (moduleKey: string) => {
    const groupedWorkflows = getGroupedWorkflows()
    let inUseWorkflow = {} as Workflow
    if (!groupedWorkflows[moduleKey]) return inUseWorkflow
    groupedWorkflows[moduleKey].forEach((workflow: Workflow) => {
      if (workflow.inUse) {
        inUseWorkflow = useCloneDeep(workflow)
      }
    })
    return inUseWorkflow
  }

  function getGroupedWorkflows() {
    const orderedWorkflow = useOrderBy(workflows.value, ['version'], ['desc'])
    return useGroupBy(orderedWorkflow, 'moduleKey')
  }

  function getActionWorkflow() {
    if (!activeWorkflow.value.actions) {
      originalActionWorkflow.value = initialiseWorkflowAction()
      return useCloneDeep(originalActionWorkflow.value)
    }

    /* 
    new workflow action prop is an array, while the old one uses stage name as key. 
    Refer to NewWorkflowAction and OldWorkflowAction type in types/data.ts
    */
    const isActionWorkflowNewVersion = computed(() => {
      return activeWorkflow.value.actions instanceof Array
    })

    if (isActionWorkflowNewVersion.value) {
      // New Workflow Action
      const workflowAction = useCloneDeep(activeWorkflow.value.actions)
      workflowAction.forEach((action) => {
        if (!action.actions) action.actions = []
      })
      return workflowAction
    } else {
      // Old Workflow Action
      const workflowAction = useCloneDeep(convertOldWorkflowActionToNewWorkflowAction())
      workflowAction.forEach((action) => {
        if (!action.actions) action.actions = []
      })
      return workflowAction
    }
  }

  const setActiveWorkflowKey = (key: string) => {
    activeWorkflowKey.value = key
  }

  async function setInUseWorkflow(workflowKey: string) {
    const newWorkflows = useCloneDeep(moduleWorkflowByDateCreated.value)
    newWorkflows.forEach((workflow: Workflow) => {
      if (workflow.key === workflowKey) {
        workflow.inUse = true
      } else {
        workflow.inUse = false
      }
    })
    await updateBulkInUseWorkflow(newWorkflows)
  }

  function setActiveReport(report: Report, index: number) {
    activeWorkflowReports.value = useCloneDeep(report)
    activeReportIndex.value = index
  }

  watch([activeModule, workflows], () => {
    const isWorkflowExist = workflowsByModuleKey.value[activeModule.value.key] !== undefined
    if (isWorkflowExist) {
      const inUseWorkflowKey = findInUseWorkflow()
      if (inUseWorkflowKey === undefined) {
        setActiveWorkflowKey('')
      } else {
        setActiveWorkflowKey(inUseWorkflowKey)
      }
    } else {
      setActiveWorkflowKey('')
    }
  })

  function initialiseWorkflowAction() {
    const newWorkflowAction: NewWorkflowAction[] = []
    stageStore.workflowStages.forEach((stage) => {
      newWorkflowAction.push({
        stageKey: stage.firebaseKey,
        actions: [],
      })
    })

    return newWorkflowAction
  }

  function findInUseWorkflow() {
    const inUseWorkflow = moduleWorkflowByDateCreated.value.find(
      (workflow: Workflow) => workflow.inUse,
    )
    if (inUseWorkflow !== undefined) {
      return inUseWorkflow.key
    }
    if (moduleWorkflowByDateCreated.value.length > 0) {
      return moduleWorkflowByDateCreated.value[0].key
    }
    return undefined
  }

  async function createNewWorkflow(moduleKey: string) {
    const module = moduleStore.moduleByKey[moduleKey]
    const newWorkflow = {
      moduleKey,
      name: module.name,
      tenant: module.tenant,
      version: '0.0.1',
      created: new Date().toISOString(),
    }

    await useApiMutation('/api/workflows', {
      method: 'POST',
      body: {
        workflowData: newWorkflow,
      },
    })
  }

  async function addNewWorkflowReport() {
    const workflowData = useCloneDeep(activeWorkflow.value)
    const newReport = {
      name: 'New Report',
    } as Report

    if (!workflowData.reports) workflowData.reports = []
    workflowData.reports.push(newReport)
    await updateWorkflow(activeWorkflow.value.key, workflowData)
  }

  async function updateWorkflow(workflowKey: string, workflowData: Partial<Workflow>) {
    await useApiMutation('/api/workflows', {
      method: 'PATCH',
      body: { workflowKey, workflowData },
    })
  }

  async function updateBulkInUseWorkflow(workflow: Workflow[]) {
    const response: ServerResponse = await $fetch<ServerResponse>('/api/workflows/bulk/inUse', {
      method: 'PATCH',
      body: {
        workflows: workflow,
      },
    })
    if (response.ok) {
    } else {
      uiStore.showErrorMessage(response.errorMessage)
    }
  }

  async function upVersionWorkflow(workflowKey: string, upVersionData: UpVersionSetting) {
    // Update old workflow to not in use
    await updateWorkflow(activeWorkflow.value.key, {
      inUse: false,
      changeLog: upVersionData.changeLog,
    })

    // Duplicate old worklfow, update version and set inUse true
    const targetWorkflow = workflowsByKey.value[workflowKey]
    const module = moduleStore.moduleByKey[targetWorkflow.moduleKey]
    const workflowData = {
      ...targetWorkflow,
      name: module.name,
      tenant: module.tenant,
      key: null,
      inUse: true,
      version: upVersionData.version,
      changeLog: null,
      created: new Date().toISOString(),
    }
    const newWorkflowKey = await useApiMutation(`/api/workflows`, {
      method: 'POST',
      body: {
        workflowData,
      },
    })

    setActiveWorkflowKey(newWorkflowKey as string)
    await stageStore.copyWorkflowStages(workflowKey)
  }

  async function deleteWorkflow(workflowKey: string) {
    await useApiMutation('/api/workflows', {
      method: 'DELETE',
      body: { workflowKey },
    })
  }

  async function deleteMultipleWorkflows(workflowKeys: string[]) {
    await useApiMutation('/api/workflows/bulk', {
      method: 'DELETE',
      body: { workflowKeys },
    })
  }

  async function connectWorkflowAction(stageKey: string, index: number) {
    /*
    Exclusive function for CardActionConfigNoStageKey.vue.
    [ When the stage name and action stage name cannot be connected. ]
    set activeWorkflow.actions[index] stage key to stageKey
    */
    const workflowAction = useCloneDeep({
      actions: getActionWorkflow(),
    })
    const targetActionIndex = workflowAction.actions.findIndex((action) => {
      return action.stageKey === stageKey
    })
    const actions = workflowAction.actions[index].actions ?? []
    workflowAction.actions[targetActionIndex].actions = useCloneDeep(actions)
    workflowAction.actions.splice(index, 1)
    await updateWorkflow(activeWorkflow.value.key, workflowAction as WorkflowAction)
  }

  function removeWorkflowWithNoVersion(workflows: Workflow[]) {
    return workflows.filter((workflow: Workflow) => workflow.version)
  }

  function convertOldWorkflowActionToNewWorkflowAction() {
    const newWorkflowAction: NewWorkflowAction[] = Object.keys(activeWorkflow.value.actions).map(
      (name: string) => {
        if (stageStore.isStageKeyFoundWithStageName(name)) {
          return {
            stageKey: stageStore.getStageFirebaseKeyFromStageName(name),
            actions: activeWorkflow.value.actions[name],
          }
        } else {
          return {
            stageName: name,
            stageKey: '',
            actions: activeWorkflow.value.actions[name],
          }
        }
      },
    )

    const isStageKeyExistinWorkflowAction = (stageKey: string) => {
      return newWorkflowAction.some((action) => action.stageKey === stageKey)
    }

    // Initialise new stage that are not listed on database
    stageStore.workflowStages.forEach((stage) => {
      if (!isStageKeyExistinWorkflowAction(stage.firebaseKey)) {
        newWorkflowAction.push({
          stageKey: stage.firebaseKey,
          actions: [],
        })
      }
    })
    return newWorkflowAction as NewWorkflowAction[]
  }

  return {
    // callbacks
    workflowsCallback,

    workflowsByTenant,
    activeWorkflowKey,
    activeWorkflow,
    activeWorkflowReports,
    activeReportIndex,
    addNewWorkflowReport,
    updateWorkflow,
    workflows,
    getModuleWorkflows,
    getTenantWorkflows,
    getGroupedWorkflows,
    getWorkflowByDateCreated,
    getInUseWorkflow,
    getActionWorkflow,
    moduleWorkflowByDateCreated,
    updateBulkInUseWorkflow,
    upVersionWorkflow,
    deleteWorkflow,
    setInUseWorkflow,
    connectWorkflowAction,
    setActiveReport,
    createNewWorkflow,
    deleteMultipleWorkflows,
    originalActionWorkflow,
  }
})
