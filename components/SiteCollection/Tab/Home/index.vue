<script setup lang="ts">
  import { SiteCollectionTabNames } from '~/constants/tabs'

  const siteCollectionStore = useSiteCollectionsStore()
  const { activeSiteCollection, tenant } = storeToRefs(siteCollectionStore)

  const getSiteTitle = () => {
    if (!activeSiteCollection.value?.url) return ''
    const match = activeSiteCollection.value.url.match(/\/sites\/([^/]+)/)
    return match ? match[1] : ''
  }

  const getQikAccess = () => {
    return `https://${tenant.value}.sharepoint.com${activeSiteCollection.value.url}`
  }
  const { setTab } = useSetRouteTab()
  onMounted(() => {
    setTab(SiteCollectionTabNames.HOME)
  })
</script>

<template>
  <main class="sitecollection__home">
    <section class="home__header">
      <h4>{{ getSiteTitle() }}</h4>
      <div>
        <Icon type="ios-link" size="20" />
        <a :href="getQikAccess()" target="_blank">{{ getQikAccess() }}</a>
      </div>
    </section>

    <section class="home__content">
      <div class="content__register">
        <SiteCollectionTabHomeModuleRegister />
        <SiteCollectionTabHomeFormRegister />
      </div>
    </section>
  </main>
</template>

<style lang="scss" scoped>
  .sitecollection__home {
    display: flex;
    flex-direction: column;
    gap: $margin;
  }
  .home__header {
    display: flex;
    flex-direction: column;
    gap: $unit;
  }

  .home__content {
    .content__register {
      display: grid;
      grid-template-columns: 1fr 1fr;
      column-gap: $padding;
    }
  }
</style>
