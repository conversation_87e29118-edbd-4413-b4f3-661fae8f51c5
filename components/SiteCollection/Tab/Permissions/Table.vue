<script setup lang="ts">
  import type { SiteCollectionPermission } from '~/types/siteCollection'

  defineProps({
    formPermission: {
      type: Object as () => SiteCollectionPermission,
      required: true,
    },
  })

  const { permissionOptions, groupOptions } = useSelectOptionsUtils()

  const emit = defineEmits(['delete'])
  function clickHandlerRemovePermission(index: number) {
    emit('delete', index)
  }

  const isGroupOptionsEmpty = computed(() => groupOptions.value.length === 0)
</script>

<template>
  <div class="permission__wrapper" v-if="formPermission.permissions">
    <div class="permission__header">
      <div class="permission__column name"><b>Name</b></div>
      <div class="permission__column groups">
        <b>Groups</b>
        <BaseWarningIcon text="No group found in SiteCollection" v-if="isGroupOptionsEmpty" />
      </div>
      <div class="permission__column action"></div>
    </div>
    <template v-for="(permission, index) in formPermission.permissions" :key="index">
      <div class="permission__row">
        <div class="permission__column name">
          <Select v-model="permission.name" transfer>
            <Option v-for="p in permissionOptions" :value="p.value" :key="p.value">
              {{ p.label }}
            </Option>
          </Select>
        </div>
        <div class="permission__column groups">
          <Select v-model="permission.groups" multiple transfer>
            <Option v-for="group in groupOptions" :value="group.label" :key="group.value">
              {{ group.label }}
            </Option>
          </Select>
        </div>
        <div class="permission__column action">
          <BaseButtonDelete appearance="accent" @click="clickHandlerRemovePermission(index)" />
        </div>
      </div>
    </template>
  </div>
</template>

<style lang="scss" scoped>
  .permission__wrapper {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }

  .permission__header,
  .permission__row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 0.5rem;
  }

  .permission__column {
    display: flex;
    align-items: center;
  }

  .permission__column.name {
    flex: 0 0 30%;
  }

  .permission__column.groups {
    display: flex;
    gap: calc($unit/2);
    flex: 1;
  }

  .permission__column.action {
    flex: 0 0 auto;
  }
</style>
