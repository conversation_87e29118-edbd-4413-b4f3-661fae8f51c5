<script setup lang="ts">
const props = defineProps({
  disabled: {
    type: Boolean,
    default: false,
  },
})
</script>

<template>
  <button class="buttonText" :disabled="props.disabled">
    <slot />
  </button>
</template>

<style lang="scss" scoped>
.buttonText {
  color: $primary-blue;
  user-select: none;
  border: none;
  background: none;

  &:hover {
    cursor: pointer;
    color: $primary-blue-hover;
  }

  &:active {
    color: $primary-blue-pressed;
  }

  &:disabled {
    color: gray;
    cursor: not-allowed;
  }
}
</style>