import { defineStore } from 'pinia'
import { SiteCollectionEnvironment } from '../types/siteCollection'
import type { SiteCollection } from '../types/siteCollection'
import { useStoreUtils } from './utils/storeFetchUtils'
import { useApiMutation } from '~/stores/utils/apiMutation'

// ---------- State ----------
export const useSiteCollectionsStore = defineStore('siteCollections', () => {
  const { convertFirebaseDataToArray } = useStoreUtils()
  const moduleStore = useModulesStore()
  const formStore = useFormStore()
  const tenant = ref<string>('')
  const siteUrl = ref<string>('')

  const siteCollections = ref<SiteCollection[]>([])
  const activeSiteCollectionKey = ref<string>('')
  const siteCollectionsByKey = computed(() => {
    return useKeyBy(siteCollections.value, 'key')
  })
  const siteCollectionsByTenant = computed(() => {
    return useGroupBy(siteCollections.value, 'tenant')
  })
  const siteCollectionsSortedByEnvironment = computed(() => {
    return getSiteCollectionsSortedByEnvironment(siteCollectionsByTenant.value[tenant.value] || [])
  })
  const activeSiteCollection = computed(
    () => siteCollectionsByKey.value[activeSiteCollectionKey.value] || ({} as SiteCollection),
  )

  // ---------- Callbacks ----------
  const siteCollectionsCallback = (
    firebaseSiteCollectionData: FirebaseData<Partial<SiteCollection>>,
  ): void => {
    siteCollections.value = convertFirebaseDataToArray(firebaseSiteCollectionData)
  }

  // ---------- Mutation (POST/PATCH/DELETE) ----------
  const updateSiteCollection = async (siteKey: string, siteData: Partial<SiteCollection>) =>
    await useApiMutation('/api/sitecollections', {
      method: 'PATCH',
      body: { siteKey, siteData },
    })

  const addSiteCollection = async (siteCollectionData: SiteCollection) =>
    await useApiMutation('/api/sitecollections', {
      method: 'POST',
      body: { siteCollectionData },
    })

  // ---------- Setters ----------
  const setTenant = (tenantName: string) => {
    tenant.value = tenantName
  }

  const setActiveSite = (siteKey: string) => {
    activeSiteCollectionKey.value = siteKey
  }

  // ---------- Utils / Getters ----------
  const getSiteCollectionsByTenant = () => useGroupBy(siteCollections.value, 'tenant')

  const getSiteCollectionsSortedByEnvironment = (list: SiteCollection[]) =>
    useSortBy(list, [
      (site) => {
        switch (site.environment) {
          case SiteCollectionEnvironment.Dev:
            return 0
          case SiteCollectionEnvironment.Test:
            return 1
          case SiteCollectionEnvironment.Live:
            return 2
          case SiteCollectionEnvironment.Staging:
            return 3
          default:
            return -1
        }
      },
    ])

  const getSiteByKey = (siteKey: string) => siteCollectionsByKey.value[siteKey]

  const getActiveSiteCollectionForms = () => activeSiteCollection.value.forms || []

  const getActiveSiteCollectionModules = () => activeSiteCollection.value.modules || []

  const getSiteModules = (siteModule: string[]) =>
    siteModule.map((key) => moduleStore.moduleByKey[key])

  const getSiteCollectionUrl = (siteKey: string) => siteCollectionsByKey.value[siteKey]?.url || ''

  // ---------- Extra Site Collection Management ----------
  const addNewModuleToSiteCollectionWithName = async (siteKey: string, moduleName: string) => {
    const site = useCloneDeep(siteCollectionsByKey.value[siteKey])
    const newModuleKey = await moduleStore.createModule(moduleName, site.tenant)
    if (!newModuleKey) return
    site.modules = site.modules || []
    site.modules.push(newModuleKey)
    await updateSiteCollection(siteKey, site)
    return newModuleKey
  }

  const addNewFormToSiteCollectionWithName = async (siteKey: string, formName: string) => {
    const site = useCloneDeep(siteCollectionsByKey.value[siteKey])
    const newFormKey = await formStore.createNewForm(formName)
    site.forms = site.forms || []
    site.forms.push(newFormKey)
    await updateSiteCollection(siteKey, site)
    return newFormKey
  }

  const removeModuleFromSiteCollection = async (siteKey: string, moduleKey: string) => {
    const site = useCloneDeep(siteCollectionsByKey.value[siteKey])
    if (!site.modules) return
    site.modules = site.modules.filter((key) => key !== moduleKey)
    await updateSiteCollection(siteKey, site)
  }

  const removeFormFromSiteCollection = async (siteKey: string, formKey: string) => {
    const site = useCloneDeep(siteCollectionsByKey.value[siteKey])
    if (!site.forms) return
    site.forms = site.forms.filter((key) => key !== formKey)
    await updateSiteCollection(siteKey, site)
  }

  // ---------- Expose ----------
  return {
    // Callbacks
    siteCollectionsCallback,
    // State
    tenant,
    siteUrl,
    siteCollections,
    siteCollectionsByKey,
    siteCollectionsByTenant,
    siteCollectionsSortedByEnvironment,
    activeSiteCollection,

    // Mutations
    updateSiteCollection,
    addSiteCollection,

    // Setters
    setTenant,
    setActiveSite,

    // Getters
    getSiteByKey,
    getSiteModules,
    getSiteCollectionsByTenant,
    getSiteCollectionsSortedByEnvironment,
    getActiveSiteCollectionForms,
    getActiveSiteCollectionModules,
    getSiteCollectionUrl,

    // Management
    addNewModuleToSiteCollectionWithName,
    addNewFormToSiteCollectionWithName,
    removeModuleFromSiteCollection,
    removeFormFromSiteCollection,
  }
})
