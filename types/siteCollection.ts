import type { Register } from "./register";

export type SiteCollection = SiteCollectionConfig &
  SiteCollectionPermission &
  SiteCollectionTheme &
  SiteCollectionNavigation &
  SiteCollectionForm &
  SiteCollectionGroups &
  SiteCollectionDCC &
  SiteCollectionRCC & {
    tenant: string;
    key: string;
    cores: string[];
    modules: string[];
  };

export type SiteCollectionGroups = {
  groups: string[];
};

export type SiteCollectionConfig = {
  useCustomDateFormat: boolean;
  useCustomRejectButtonText: boolean;
  customDateFormat: string;
  customRejectButtonText: string;
  documentIdInitiation: boolean;
  environment: string;
  cores: string[];
  url: string;
  groups?: string[];
};

export type SiteCollectionPermission = {
  permissions: Permission[];
};

export type Permission = {
  groups: string[];
  name: string;
};

export type SiteCollectionTheme = {
  accentColor: string;
  buttonColor: string;
  buttonHoverColor: string;
  linkActiveTextColor: string;
  linkBackgroundColor: string;
  linkColor: string;
  menuGroupTitleColor: string;
  primaryColor: string;
  secondaryColor: string;
  submenuTitleColor: string;
  dashboardCardH1Color: string;
  dashboardCardH4Color: string;
};

export type SiteCollectionNavigation = {
  navigationHeader: string;
  navigations: Navigation[];
};

export type Navigation = {
  icon: string;
  iframe: boolean;
  link: string;
  title: string;
};

export enum SiteCollectionEnvironment {
  Dev = "Dev",
  Test = "Test",
  Live = "Live",
  Staging = "Staging",
}

export enum SiteCollectionPermissionsName {
  showConfig = "showConfig",
  showAssessmentTemplate = "showAssessmentTemplate",
  showChangeType = "showChangeType",
}

export type SiteCollectionForm = {
  forms: string[];
};

export type SiteCollectionDCC = {
  registers: Register[];
};

export type SiteCollectionRCC = {
  Rregisters: Register[];
};
