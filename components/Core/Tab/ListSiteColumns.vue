<script setup lang="ts">
import { CoreTabNames } from '~/constants/tabs';
import type { CoreList, CoreSiteColumn } from '~/types/core';

const coresStore = useCoresStore();
const { activeCore } = storeToRefs(coresStore);
const coreListsSetting = ref([] as CoreList[]);
const router = useRouter();
const route = useRoute();

onMounted(() =>
{
  router.replace({
    query: {
      ...route.query,
      tab: CoreTabNames.SITE_COLUMNS_LIST,
    },
  });
  initialiseCoreListSetting();
})

function initialiseCoreListSetting()
{
  let coreList = [] as CoreList[];
  if (activeCore.value.lists)
  {
    coreList = activeCore.value?.lists.map((list: CoreList, index) =>
    {
      return {
        ...list,
        id: index
      };
    });
  }
  coreListsSetting.value = coreList || [];
}

watch(activeCore, (newVal) =>
{
  if (newVal)
  {
    initialiseCoreListSetting();
  }
})
</script>

<template>
  <Card>
    <div class="coreTabListSiteColumns">
      <template v-for="list in coreListsSetting">
        <CoreTabListSiteColumnsRegister :list="list" />
      </template>
    </div>
  </Card>
</template>

<style lang="scss" scoped>
.coreTabListSiteColumns {
  display: grid;
  grid-template-columns: repeat(4, calc(25% - #{$unit * 0.75 }));
  gap: $unit;
  grid-auto-flow: row;
  width: 100%;
  padding: $margin;
  min-height: 60vh;
}
</style>