<script setup lang="ts">
import { FormTabNames } from '~/constants/tabs'

const formBuilderStore = useFormBuilderStore()
const router = useRouter()
const route = useRoute()
onMounted(() =>
{
    router.replace({
        query: {
            ...route.query,
            tab: FormTabNames.FORMS
        }
    })
    formBuilderStore.initialiseFormBuilderForForm()
})
</script>

<template>
    <FormBuilder />
</template>