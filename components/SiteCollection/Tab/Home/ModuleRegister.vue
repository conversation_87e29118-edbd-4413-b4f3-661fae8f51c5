<script setup lang="ts">
  const siteCollectionStore = useSiteCollectionsStore()
  const { activeSiteCollection, tenant } = storeToRefs(siteCollectionStore)
  const modulesStore = useModulesStore()

  const siteModules = computed(() => {
    return modulesStore.getSiteModules(activeSiteCollection.value?.modules || [])
  })

  const showAddModuleDrawer = ref(false)

  const columns = [
    { title: 'Name', key: 'name' },
    { title: 'Action', slot: 'action', width: 150, align: 'center' },
  ]

  function clickHandlerSetting(moduleKey: string) {
    navigateTo(`/tenants/${tenant.value}/${activeSiteCollection.value.key}/module/${moduleKey}`)
  }
</script>

<template>
  <SiteCollectionDrawerAddModule @close="showAddModuleDrawer = false" v-if="showAddModuleDrawer" />
  <div>
    <div class="module-header">
      <span>Modules</span>
      <BaseButtonAdd text="Module" appearance="accent" @click="showAddModuleDrawer = true" />
    </div>
    <BaseRegisterList :list="siteModules" :columns="columns">
      <template #action="{ row }">
        <BaseButton @click="clickHandlerSetting(row.key)">
          <Icon type="md-briefcase" />
        </BaseButton>
      </template>
    </BaseRegisterList>
  </div>
</template>

<style lang="scss" scoped>
  .module-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: $padding;
  }
</style>
