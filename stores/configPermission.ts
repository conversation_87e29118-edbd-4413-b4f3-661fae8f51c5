import type { Permission, PermissionSetting } from "~/types/permission";

export enum PermissionFor {
  WORKFLOW = "workflow",
  FORM = "form",
}

type OldPermission = {
  [key: string]: Permission;
};

export const useConfigPermissionStore = defineStore("permission", () => {
  const workflowStore = useWorkflowsStore();
  const { activeWorkflow } = storeToRefs(workflowStore);

  const formConfigStore = useFormConfigStore();
  const { activeFormConfig } = storeToRefs(formConfigStore);

  const activePermissionSetting = ref({} as PermissionSetting);
  const permissionFor = ref<PermissionFor>();

  const initialisePermissionStoreForWorkflow = () => {
    clearPermissionState();
    permissionFor.value = PermissionFor.WORKFLOW;
    activePermissionSetting.value = useCloneDeep(activeWorkflow.value);
    initialisePermissionData();
  };

  const initialisePermissionStoreForForm = () => {
    clearPermissionState();
    permissionFor.value = PermissionFor.FORM;
    activePermissionSetting.value = useCloneDeep(activeFormConfig.value);
    initialisePermissionData();
  };

  const clearPermissionState = () => {
    permissionFor.value = undefined;
    activePermissionSetting.value = {} as PermissionSetting;
  };

  async function saveHandler(permissionData: PermissionSetting) {
    if (permissionFor.value === PermissionFor.WORKFLOW) {
      await workflowStore.updateWorkflow(
        activeWorkflow.value.key,
        permissionData
      );
    } else if (permissionFor.value === PermissionFor.FORM) {
      await formConfigStore.updateFormConfig(
        activeFormConfig.value.key,
        permissionData
      );
    }
    initialisePermissionData();
  }

  function initialisePermissionData() {
    if (!activePermissionSetting.value.permissions) {
      activePermissionSetting.value.permissions = [];
    }
    const permissionArray: Permission[] = [];
    if (!Array.isArray(activePermissionSetting.value.permissions)) {
      const permissionData = activePermissionSetting.value
        .permissions as OldPermission;
      Object.keys(permissionData).forEach((key) => {
        permissionArray.push(permissionData[key]);
      });

      activePermissionSetting.value.permissions = useCloneDeep(permissionArray);
    } else {
      activePermissionSetting.value.permissions = useCloneDeep(
        activePermissionSetting.value.permissions
      );
    }
  }
  return {
    initialisePermissionStoreForWorkflow,
    initialisePermissionStoreForForm,
    activePermissionSetting,
    saveHandler,
  };
});
