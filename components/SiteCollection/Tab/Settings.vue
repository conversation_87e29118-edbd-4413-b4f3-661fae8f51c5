<script setup lang="ts">
  import { useSelectOptionsUtils } from '~/composables/useSelectOptionsUtils'
  import { SiteCollectionTabNames } from '~/constants/tabs'
  import type { SiteCollectionConfig } from '~/types/siteCollection'
  import { useSetRouteTab } from '~/composables/useSetRouteTab'

  const siteCollectionStore = useSiteCollectionsStore()
  const { activeSiteCollection } = storeToRefs(siteCollectionStore)
  const { updateSiteCollection } = siteCollectionStore

  const { environmentOptions, coreOptions } = useSelectOptionsUtils()

  const formSetting = ref<SiteCollectionConfig>({} as SiteCollectionConfig)

  const { setTab } = useSetRouteTab()

  onMounted(() => setTab(SiteCollectionTabNames.SETTINGS))

  const { resetHandler, submitHandler } = useSettingForm<SiteCollectionConfig>({
    form: formSetting,
    activeData: activeSiteCollection,
    updateHandler: async () => {
      await updateSiteCollection(activeSiteCollection.value.key, formSetting.value)
    },
  })
</script>

<template>
  <BaseCardPanel class="card">
    <template #title> Settings </template>

    <Form label-position="left">
      <FieldInputText label="Site Collection URL" v-model="formSetting.url" />

      <FieldInputSwitch
        label="Enable Custom Date Format"
        v-model="formSetting.useCustomDateFormat" />
      <FieldInputText
        label="Custom Date Format"
        v-model="formSetting.customDateFormat"
        v-if="formSetting.useCustomDateFormat" />

      <FieldInputSwitch
        label="Enable Custom Reject Button Text"
        v-model="formSetting.useCustomRejectButtonText" />
      <FieldInputText
        label="Custom Reject Button Text"
        v-model="formSetting.customRejectButtonText"
        v-if="formSetting.useCustomRejectButtonText" />

      <FieldInputSwitch
        label="Enable DocumentID in Initiation"
        v-model="formSetting.documentIdInitiation" />

      <FormItem label="Environment">
        <Select v-model="formSetting.environment" transfer>
          <Option v-for="env in environmentOptions" :key="env.value" :value="env.value">
            {{ env.label }}
          </Option>
        </Select>
      </FormItem>

      <FormItem label="Cores">
        <Select v-model="formSetting.cores" multiple transfer>
          <Option v-for="core in coreOptions" :key="core.value" :value="core.value">
            {{ core.label }}
          </Option>
        </Select>
      </FormItem>
    </Form>

    <BaseButtonSaveReset @reset="resetHandler" @save="submitHandler" />
  </BaseCardPanel>
</template>
