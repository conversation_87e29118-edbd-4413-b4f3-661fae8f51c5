export const getFormDigest = async (siteUrl: string, accessToken: string) => {
  const response = await fetch(`${siteUrl}/_api/contextinfo`, {
    method: "POST",
    headers: {
      Accept: "application/json; odata=verbose",
      "Content-Type": "application/json; odata=verbose",
      Authorization: `Bearer ${accessToken}`,
    },
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(`Failed to get Form Digest: ${error}`);
  }

  const data = await response.json();
  const commaIndex =
    data.d.GetContextWebInformation.FormDigestValue.indexOf(",");
  const formDigestValue = data.d.GetContextWebInformation.FormDigestValue.slice(
    0,
    commaIndex
  );
  return formDigestValue;
};
