import {
  provideFluentDesignSystem,
  fluentCard,
  fluentButton,
  fluentAccordion,
  fluentAccordionItem,
  fluentTextField,
  fluentListbox,
  fluentOption,
  fluentProgressRing,
  fluentTextArea,
  fluentSwitch,
  fluentTabs,
  fluentTab,
  fluentTabPanel,
  fluentSelect,
  fluentCombobox,
  fluentRadioGroup,
  fluentRadio,
  fluentDivider,
  fluentTooltip,
  fluentNumberField,
} from "@fluentui/web-components";

export default defineNuxtPlugin(() => {
  provideFluentDesignSystem().register(
    fluentCard(),
    fluentButton(),
    fluentAccordion(),
    fluentAccordionItem(),
    fluentTextField(),
    fluentNumberField(),
    fluentListbox(),
    fluentOption(),
    fluentProgressRing(),
    fluentTextArea(),
    fluentSwitch(),
    fluentTabs(),
    fluentTab(),
    fluentTabPanel(),
    fluentSelect(),
    fluentCombobox(),
    fluentRadioGroup(),
    fluentRadio(),
    fluentDivider(),
    fluentTooltip()
  );
});
