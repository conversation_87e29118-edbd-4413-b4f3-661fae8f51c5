<script setup type="ts">
  definePageMeta({
    layout: 'login',
  })
</script>

<template>
  <div class="loginPage">
    <div class="background" />
    <div class="logo">
      <img src="/inqlogo.png" width="200px" />
    </div>
    <AppLoginForm />
  </div>
</template>

<style lang="scss" scoped>
  .loginPage {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 200px;
    gap: $margin;
    height: 100%;

    .background {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      opacity: 0.64;
      mix-blend-mode: normal;
      z-index: -1;
      background: radial-gradient(
        circle at left bottom,
        rgb(133, 37, 206) 0%,
        var(--global-palette2, #ed8f0c) 33%,
        var(--global-palette2, #ed8f0c) 62%,
        rgb(133, 37, 206) 100%
      );
    }

    .logo {
      width: 200px;
      height: 200px;
      align-self: center;
    }
  }
</style>
