<script setup lang="ts">
  import type { Action, NewWorkflowAction } from '~/types/workflow'

  const props = defineProps({
    action: {
      type: Object as PropType<NewWorkflowAction>,
      required: true,
    },
    index: {
      type: Number,
      required: true,
    },
  })

  const uiStore = useUiStore()
  const workflowStore = useWorkflowsStore()
  const stageStore = useStagesStore()
  const { workflowStages } = storeToRefs(stageStore)

  const selectedStageKey = ref('')
  const showData = ref(false)
  const connectConfirmModal = ref(false)
  const deleteConfirmModal = ref(false)

  const isStageKeySelected = computed(() => selectedStageKey.value !== '')

  const actionData = ref([] as Action[])

  onMounted(() => {
    actionData.value = useClone(props.action.actions)
  })

  watch(
    () => props.action,
    (newVal) => {
      actionData.value = useClone(newVal.actions)
    },
  )

  function updateHandler(stageFirebaseKey: string) {
    selectedStageKey.value = stageFirebaseKey
  }

  function clickHandlerShowData() {
    showData.value = !showData.value
  }

  function clickHandlerConnect() {
    connectConfirmModal.value = true
  }

  function clickHandlerDelete() {
    deleteConfirmModal.value = true
  }

  async function handlerOkConnect() {
    uiStore.setLoading(true, 'Connecting Action...')
    connectConfirmModal.value = false
    await workflowStore.connectWorkflowAction(selectedStageKey.value, props.index)
    uiStore.setLoading(false)
  }
</script>

<template>
  <Modal v-model="connectConfirmModal" title="Confirm Connect Action" @on-ok="handlerOkConnect">
    <span>This action will replace action with stageKey: {{ selectedStageKey }}</span>
  </Modal>
  <Modal v-model="deleteConfirmModal" title="Confirm Delete Action"></Modal>
  <Card dis-hover>
    <template #title>
      <b>Stage Key not detected</b>
    </template>
    <span>
      Please select Stage to connect Action with name:
      <b
        ><i>{{ action.stageName }}</i></b
      >
    </span>

    <FieldInputSelect
      v-model="selectedStageKey"
      :options="workflowStages"
      v-slot="{ option }"
      value-tag="firebaseKey"
      label-tag="name">
      {{ option.name }}
    </FieldInputSelect>
    <span class="showData" @click="clickHandlerShowData">{{
      showData ? 'Close Data' : 'Show Data'
    }}</span>
    <div class="data" v-if="showData">
      <template v-for="(a, index) in actionData">
        <BaseObjectReader :object="actionData[index]" />
      </template>
    </div>
    <div class="buttons">
      <BaseButton appearance="accent" @click="clickHandlerConnect" :disabled="!isStageKeySelected"
        >Connect
      </BaseButton>
      or
      <BaseButton @click="clickHandlerDelete">Delete</BaseButton>
    </div>
  </Card>
</template>

<style lang="scss" scoped>
  .buttons {
    margin-top: $margin;
    display: flex;
    align-items: center;
    gap: $margin;
  }

  .showData {
    &:hover {
      text-decoration: underline;
      cursor: pointer;
    }
  }
</style>
