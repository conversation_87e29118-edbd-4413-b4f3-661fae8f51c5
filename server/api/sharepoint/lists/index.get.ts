import { useSharepointAPIUtils } from '~/composables/useSharepointAPIUtils'

export default defineEventHandler(async (event) => {
  const { fetchSharepointAPIWithTenant } = useSharepointAPIUtils()
  try {
    const query = getQuery(event)
    const { tenantUrl, tenantId, siteUrl } = query

    if (!tenantUrl || !siteUrl) {
      return {
        ok: false,
        errorMessage: `Missing required query parameters: 'tenant' or 'siteUrl'.`,
      }
    }

    const url = `${siteUrl}/_api/web/lists`

    const response = await fetchSharepointAPIWithTenant(
      url,
      tenantUrl as string,
      tenantId as string,
    )

    const data = await response.json()
    return {
      ok: true,
      data: data.d.results,
      message: `Fetched ${siteUrl} lists successfully.`,
    }
  } catch (error) {
    return {
      ok: false,
      errorMessage: `${error}`,
    }
  }
})
