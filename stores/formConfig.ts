import type { FormConfig, FormConfigForms, FormConfigSetting } from '~/types/formConfig'
import type { PermissionSetting } from '~/types/permission'
import type { RegisterSetting } from '~/types/register'
import type { SharepointCustomListSetting, SharepointListSetting } from '~/types/sharepointConfig'
import { useStoreUtils } from './utils/storeFetchUtils'
import { useApiMutation } from '~/stores/utils/apiMutation'

export const useFormConfigStore = defineStore('formConfig', () => {
  const { convertFirebaseDataToArray } = useStoreUtils()
  const formStore = useFormStore()
  const activeFormKey = computed(() => formStore.activeFormKey)

  // ----------------- State -----------------
  const formConfigs = ref<FormConfig[]>([])
  const activeFormConfigKey = ref<string>('')
  const activeFormConfig = computed(
    () => formConfigByKey.value[activeFormConfigKey.value] || ({} as FormConfig),
  )
  const formConfigByKey = computed(() => useKeyBy(formConfigs.value, 'key'))
  const formConfigByFormKey = computed(() => useGroupBy(formConfigs.value, 'formKey'))
  const formFormConfigByDateCreated = computed(() => getFormConfigByDateCreated())

  // ----------------- Callbacks -----------------
  const formConfigsCallback = (firebaseFormConfigData: FirebaseData<Partial<FormConfig>>): void => {
    formConfigs.value = convertFirebaseDataToArray(firebaseFormConfigData)
  }

  // ----------------- Mutations -----------------
  const createNewFormConfig = (formKey: string, formName: string) =>
    useApiMutation<string>('/api/formConfig', {
      method: 'POST',
      body: {
        formConfigData: {
          formKey,
          name: formName,
          created: new Date().toISOString(),
          version: '0.0.1',
        },
      },
    })

  const updateFormConfig = (
    formConfigKey: string,
    formConfigData:
      | FormConfigSetting
      | SharepointCustomListSetting
      | SharepointListSetting
      | RegisterSetting
      | PermissionSetting
      | FormConfigForms,
  ) =>
    useApiMutation('/api/formConfig', {
      method: 'PATCH',
      body: { formConfigKey, formConfigData },
    })

  const updateBulkInUseFormConfig = (formConfigs: FormConfig[]) =>
    useApiMutation('/api/formConfig/bulk/inUse', {
      method: 'PATCH',
      body: { formConfigs },
    })

  const deleteMultipleFormConfig = (formConfigKeys: string[]) =>
    useApiMutation('/api/formConfig/bulk', {
      method: 'DELETE',
      body: { formConfigKeys },
    })

  // ----------------- Business Logic -----------------
  const setInUseFormConfig = async (formConfigKey: string) => {
    const newFormConfigs = useCloneDeep(formFormConfigByDateCreated.value)
    newFormConfigs.forEach((config: FormConfig) => (config.inUse = config.key === formConfigKey))
    await updateBulkInUseFormConfig(newFormConfigs)
  }
  const setActiveFormConfigKey = (formConfigKey: string) => {
    activeFormConfigKey.value = formConfigKey
  }

  watch([formConfigs, activeFormKey], () => {
    const isFormConfigExist = formConfigByFormKey.value[activeFormKey.value] !== undefined
    if (isFormConfigExist) {
      const inUseFormConfigKey = findInUseFormConfig()
      if (inUseFormConfigKey === undefined) {
        setActiveFormConfigKey('')
      } else {
        setActiveFormConfigKey(inUseFormConfigKey)
      }
    } else {
      setActiveFormConfigKey('')
    }
  })

  function findInUseFormConfig() {
    const inUseFormConfig = formFormConfigByDateCreated.value.find(
      (formConfig: FormConfig) => formConfig.inUse,
    )
    if (inUseFormConfig !== undefined) {
      return inUseFormConfig.key
    }
    if (formFormConfigByDateCreated.value.length > 0) {
      return formFormConfigByDateCreated.value[0].key
    }
    return undefined
  }

  // ----------------- Helpers -----------------
  const getFormConfigsWithFormKey = (formKey: string) =>
    (formConfigByFormKey.value[formKey] || []).sort(
      (a, b) => new Date(b.created).getTime() - new Date(a.created).getTime(),
    )
  const getFormConfigByDateCreated = () => {
    if (!activeFormKey.value) return []
    if (!formConfigs.value) return []

    const formFormConfigs = formConfigByFormKey.value[activeFormKey.value]
    if (!formFormConfigs) return []
    return formFormConfigs.sort((a, b) => {
      return new Date(b.created).getTime() - new Date(a.created).getTime()
    })
  }

  // ----------------- Expose -----------------
  return {
    // callbacks
    formConfigsCallback,
    formConfigs,
    formFormConfigByDateCreated,
    activeFormConfig,
    activeFormConfigKey,

    createNewFormConfig,
    updateFormConfig,
    updateBulkInUseFormConfig,
    deleteMultipleFormConfig,
    setInUseFormConfig,

    getFormConfigsWithFormKey,
  }
})
