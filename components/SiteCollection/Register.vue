<script setup lang="ts">
  import { type Column } from '~/types/data'

  const columns: Column[] = [
    { title: 'Site Url', key: 'url' },
    { title: 'Key', key: 'key' },
    { title: 'Environment', slot: 'environment', align: 'center' },
    { title: 'Action', slot: 'action', width: 150, align: 'center' },
  ]

  const showAddFormDrawer = ref(false)
  const showAddModuleDrawer = ref(false)
  const showAddSiteDrawer = ref(false)

  const siteCollectionsStore = useSiteCollectionsStore()
  const { siteCollectionsSortedByEnvironment, tenant } = storeToRefs(siteCollectionsStore)

  function clickHandlerSetting(siteKey: string) {
    siteCollectionsStore.setActiveSite(siteKey)
    navigateTo(`/tenants/${tenant.value}/${siteKey}`)
  }

  function clickHandlerAddSite() {
    showAddSiteDrawer.value = true
  }

  function clickHandlerAddModule(siteKey: string) {
    siteCollectionsStore.setActiveSite(siteKey)
    showAddModuleDrawer.value = true
  }

  function clickHandlerAddForm(siteKey: string) {
    siteCollectionsStore.setActiveSite(siteKey)
    showAddFormDrawer.value = true
  }
</script>

<template>
  <SiteCollectionDrawerAddForm @close="showAddFormDrawer = false" v-if="showAddFormDrawer" />
  <SiteCollectionDrawerAddModule @close="showAddModuleDrawer = false" v-if="showAddModuleDrawer" />
  <SiteCollectionDrawerAddSite @close="showAddSiteDrawer = false" v-if="showAddSiteDrawer" />
  <div class="addSiteCollectionButton">
    <BaseButtonAdd @click="clickHandlerAddSite" text="Site Collection" />
  </div>
  <BaseRegisterList :list="siteCollectionsSortedByEnvironment" :columns="columns">
    <template #environment="{ row }">
      <SiteCollectionRegisterEnvironmentTag :environment="row.environment" />
    </template>
    <template #action="{ row }">
      <BaseButton @click="clickHandlerSetting(row.key)">
        <Icon type="md-briefcase" />
      </BaseButton>
      <Dropdown trigger="click" placement="bottom-end" transfer>
        <BaseButton>
          <Icon type="md-add" />
        </BaseButton>
        <template #list>
          <DropdownMenu>
            <DropdownItem
              class="dropdown__item"
              name="addModule"
              @click="clickHandlerAddModule(row.key)">
              <span>Add Module</span>
            </DropdownItem>
            <DropdownItem
              class="dropdown__item"
              name="addForm"
              @click="clickHandlerAddForm(row.key)">
              <span>Add Form</span>
            </DropdownItem>
          </DropdownMenu>
        </template>
      </Dropdown>
    </template>
  </BaseRegisterList>
</template>

<style lang="scss" scoped>
  .addSiteCollectionButton {
    display: flex;
    justify-content: flex-end;
    margin-right: $margin;
  }

  .dropdown__item {
    display: flex;
    align-items: center;
    gap: $unit;
  }
</style>
