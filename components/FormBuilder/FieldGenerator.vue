<script setup lang="ts">
import { FormFieldType, type FormFieldData } from '~/types/form';

defineProps({
    formElementData: {
        type: Object as PropType<FormFieldData>,
        required: true
    }
})

</script>

<template>
    <!-- If type is Text -->
    <FieldText :label="formElementData.label" :fontSize="formElementData.fontSize"
        v-if="formElementData.type === FormFieldType.TEXT" />

    <!-- If type is TextInput -->
    <FieldInputText :label="formElementData.label" v-if="formElementData.type === FormFieldType.TEXTINPUT" />

    <!-- If type is NumberInput -->
    <FieldInputNumber :label="formElementData.label" v-if="formElementData.type === FormFieldType.NUMBERINPUT" />

    <!-- If type is Select -->
    <FieldInputSelect :label="formElementData.label" :options="formElementData.options"
        v-if="formElementData.type === FormFieldType.SELECT" v-slot="{ option }" value-tag="name">
        <div>{{ option.name }}</div>
    </FieldInputSelect>

    <!-- If type is DatePicker -->
    <FieldInputDatePicker :label="formElementData.label" v-if="formElementData.type === FormFieldType.DATEPICKER" />

    <!-- If type is Radio Button -->
    <FieldInputRadioButton :label="formElementData.label" v-if="formElementData.type === FormFieldType.RADIOBUTTON">
        <Radio v-for="(option, index) in formElementData.options" :key="index" :value="option.name">
            {{ option.name }}
        </Radio>
    </FieldInputRadioButton>

    <!-- If type is PeoplePicker -->
    <FieldPeoplePicker :label="formElementData.label" v-if="formElementData.type === FormFieldType.PEOPLEPICKER" />

    <!-- If type is TextArea -->
    <FieldInputTextArea :label="formElementData.label" v-if="formElementData.type === FormFieldType.TEXTAREA" />

    <!-- If type is FileUpload -->
    <FieldInputFileUpload :label="formElementData.label" v-if="formElementData.type === FormFieldType.FILEUPLOAD" />

    <!-- If type is Image -->
    <FieldImage :label="formElementData.label" :url="formElementData.url" :width="formElementData.width"
        v-if="formElementData.type === FormFieldType.IMAGE" />

    <!-- If type is Hidden -->
    <h1 v-if="formElementData.type === FormFieldType.HIDDEN">{{ formElementData.label }}</h1>

    <!-- If type is Link -->
    <h1 v-if="formElementData.type === FormFieldType.LINK">
        {{ formElementData.label }} <span>( url: <span class="link">{{ formElementData.url }}</span>)</span>
    </h1>

    <!-- If type is TableInput -->
    <FieldTableInput :item="formElementData" v-if="formElementData.type === FormFieldType.TABLEINPUT" />

    <!-- If type is DocumentDisplay -->
    <FieldDocumentDisplay :label="formElementData.label"
        v-if="formElementData.type === FormFieldType.DOCUMENTDISPLAY" />

    <!-- If type is Action -->
    <FieldTableInput :item="formElementData" v-if="formElementData.type === FormFieldType.ACTION" />

    <!-- If type is TagsInput -->
    <FieldTagInput :label="formElementData.label" v-if="formElementData.type === FormFieldType.TAGSINPUT" />

    <!-- If type is BasicRiskTemplate -->
    <FieldTableInput :item="formElementData" v-if="formElementData.type === FormFieldType.BASICRISK" />

    <!-- If type is FMEATemplate -->
    <FieldTableInput :item="formElementData" v-if="formElementData.type === FormFieldType.FMEA" />

    <!-- If type is RPN -->
    <FieldTableInput :item="formElementData" v-if="formElementData.type === FormFieldType.RPN" />

    <!-- If type is FinalRPN -->
    <FieldTableInput :item="formElementData" v-if="formElementData.type === FormFieldType.FINALRPN" />


    <div v-if="formElementData.type === FormFieldType.DOCUMENTSELECT">
        <h1>{{ formElementData.label }}</h1>
        <FormBuilderFieldDocument :docs="formElementData.options" />
    </div>
    <div v-if="formElementData.type === FormFieldType.DOCUMENTLISTSELECT">
        <h1>Document List select - to be implemented</h1>
    </div>
    <div v-if="formElementData.type === FormFieldType.NEWDOCUMENTTABLE">
        <h1>{{ formElementData.label }}</h1>
        <FormBuilderFieldDocument :docs="formElementData.options" />
    </div>
    <div v-if="formElementData.type === FormFieldType.SEARCHORCREATE">
        <h1>{{ formElementData.label }}</h1>
    </div>
    <div v-if="formElementData.type === FormFieldType.ASSESSMENTS">
        <h1>{{ formElementData.label }}</h1>
    </div>
    <div v-if="formElementData.type === FormFieldType.CUSTOMCOMPONENT">
        <h1>{{ formElementData.label }}</h1>
    </div>
  

  

    <div v-if="formElementData.type === FormFieldType.MULTIFORM">
        <h1>{{ formElementData.label }}</h1>
    </div>

</template>

<style lang="scss" scoped>
h1 {
    font-size: 1.2rem;
    font-weight: 500;
    text-transform: none;
}

span {
    font-size: 10px;
}

.link {
    color: blue;
}
</style>
