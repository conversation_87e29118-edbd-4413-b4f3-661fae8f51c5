<script setup lang="ts">
  import type { StageSetting } from '@/types/data'
  import { Message } from 'view-ui-plus'

  const showDeleteModal = ref(false)
  const deleteConfirmation = ref('')
  const deleteConfirmationValue = computed(
    () => `${activeModule.value.name}-${activeStage.value.name}`,
  )
  const isDeleteConfirmationValid = computed(
    () => deleteConfirmation.value === deleteConfirmationValue.value,
  )
  const stagesStore = useStagesStore()
  const { activeStage, workflowStages } = storeToRefs(stagesStore)
  const moduleStore = useModulesStore()
  const { activeModule } = storeToRefs(moduleStore)
  const uiStore = useUiStore()
  const formBuilderStore = useFormBuilderStore()
  const fieldStore = useFieldStore()

  const formSetting = ref({} as StageSetting)
  const stageOptions = computed(() => fieldStore.getStageOptions())

  const { resetHandler, submitHandler } = useSettingForm<StageSetting>({
    form: formSetting,
    activeData: activeStage,
    updateHandler: async () => {
      formBuilderStore.resetActiveField()
      await stagesStore.updateStage(activeStage.value.firebaseKey, formSetting.value)
    },
  })

  function clickHandlerDelete() {
    if (workflowStages.value.length === 1) {
      Message.error('Could not delete stage: Workflow must have at least one stage')
      return
    }
    showDeleteModal.value = true
    deleteConfirmation.value = ''
  }

  async function deleteHandler() {
    uiStore.setLoading(true, `Deleting ${activeStage.value.name} stage...`)
    await stagesStore.deleteStage(activeStage.value.firebaseKey)
    uiStore.setLoading(false)
    showDeleteModal.value = false
  }
</script>

<template>
  <Modal title="Delete Confirmation" v-model="showDeleteModal" class-name="modal__center">
    <p>
      Type <b>"{{ `${activeModule.name}-${activeStage.name}` }}"</b> to continue
    </p>
    <Form label-position="top">
      <FieldInputTextNoWrapper v-model="deleteConfirmation" />
    </Form>
    <template #footer>
      <Button :disabled="!isDeleteConfirmationValid" type="error" @click="deleteHandler"
        >Delete</Button
      >
    </template>
  </Modal>
  <Card dis-hover>
    <template #title>
      <Icon type="ios-cog-outline" size="16" />
      <b>Stage Settings</b>
    </template>
    <div class="container">
      <Form label-position="top">
        <FieldInputText label="Stage Name" v-model="formSetting.name" />
        <FieldInputTextArea label="Stage Description" v-model="formSetting.description" :rows="4" />
      </Form>
      <Form label-position="left">
        <FieldInputSwitch label="Review?" v-model="formSetting.review" />
        <FieldInputSwitch
          label="Form to appear in My Actions Panel?"
          v-model="formSetting.myActionsPanel" />
        <FieldInputSwitch label="Enable Save Button" v-model="formSetting.saveButton" />
        <FieldInputSwitch label="Enable Reject Button" v-model="formSetting.rejectButton" />
        <FieldInput label="Reject Go To Stage" v-if="formSetting.rejectButton">
          <Select v-model="formSetting.rejectGoToStage" transfer>
            <Option v-for="stage in stageOptions" :key="stage.label" :value="stage.label">{{
              stage.label
            }}</Option>
          </Select>
        </FieldInput>
        <FieldInputSwitch
          label="Enable Reject in Review?"
          v-model="formSetting.rejectNormalButton" />
        <FieldInput label="Reject Go To Stage" v-if="formSetting.rejectNormalButton">
          <Select v-model="formSetting.rejectNormalGoToStage" transfer>
            <Option v-for="stage in stageOptions" :key="stage.label" :value="stage.label">{{
              stage.label
            }}</Option>
          </Select>
        </FieldInput>
        <FieldInputSwitch
          label="Enable Multiple Submission to Form Field?"
          v-model="formSetting.multipleSubmit" />
        <FieldInputSwitch
          label="Enable Dynamic Process Flow?"
          v-model="formSetting.dynamicProcessFlow" />
        <FieldInputTableFlowConditions
          v-model="formSetting.flowConditions"
          v-if="formSetting.dynamicProcessFlow" />
      </Form>

      <div class="buttons">
        <BaseButtonDeleteText text="Delete Stage" @click="clickHandlerDelete" />
        <BaseButton @click="resetHandler">Reset Stage</BaseButton>
        <BaseButton appearance="accent" @click="submitHandler">Save Stage Settings</BaseButton>
      </div>
    </div>
  </Card>
</template>

<style lang="scss" scoped>
  .container {
    margin: $margin;
    height: calc(100% - 300px);
    overflow: auto;
  }

  .buttons {
    gap: $margin;
    display: flex;
    justify-content: flex-end;
  }

  .delete-button {
    color: red;
  }
</style>
