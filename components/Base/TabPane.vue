<script setup lang="ts">
import { inject, onMounted, computed } from 'vue';

// Props for TabPane
const props = defineProps<{
  label: string;
  name: string;
}>();

// Inject `registerTab` from FluentTabs
const registerTab = inject('registerTab') as (tab: { label: string; name: string }) => void;
const selectedTab = inject('selectedTab') as { value: string | null };

// Register the tab with FluentTabs
onMounted(() =>
{
  registerTab({
    label: props.label,
    name: props.name,
  });
});

// Compute whether this TabPane is active
const isActive = computed(() => selectedTab.value === props.name);
</script>

<template>
  <!-- Render the slot only if this tab is active -->
  <div v-if="isActive">
    <slot />
  </div>
</template>
