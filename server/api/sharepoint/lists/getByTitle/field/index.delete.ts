import { getFormDigest } from '~/server/utils/formDigest'

export default defineEventHandler(async (event) => {
  try {
    const query = getQuery(event)
    const { listTitle, fieldId } = await readBody(event)
    const { tenantUrl, tenantId, siteUrl } = query
    const accessToken = await getAccessToken(tenantUrl as string, tenantId as string)
    const url = `${siteUrl}/_api/web/lists/getByTitle('${listTitle}')/fields('${fieldId}')`
    const formDigest = await getFormDigest(siteUrl as string, accessToken as string)
    const headers = {
      'content-type': 'application/json; odata=verbose',
      accept: 'application/json; odata=verbose',
      'X-HTTP-Method': 'DELETE',
      'X-RequestDigest': `${formDigest}`,
      Authorization: `Bearer ${accessToken}`,
    }
    console.log(url)
    const response = await fetch(url, {
      method: 'POST',
      headers,
    })
    if (!response.ok) {
      const error = await response.json()
      return {
        ok: false,
        errorMessage: `Failed to delete Field from List: ${error.error.message.value}`,
      }
    }

    return {
      ok: true,
      data: response,
      message: `Field deleted from List.`,
    }
  } catch (error) {
    return {
      ok: false,
      errorMessage: `Catched: Failed to delete Field from List: ${error}`,
    }
  }
})
