// CREATE Module
// body { moduleData: Module }

import { Module } from '~/types/data'
import { useAuthenticatedFirebase } from '~/server/utils/firebaseDatabase'

export default defineEventHandler(async (event) => {
  const { moduleData } = await readBody(event)

  if (!moduleData) {
    return {
      ok: false,
      errorMessage: 'moduleData is required',
    }
  }

  try {
    const { firebasePush } = useAuthenticatedFirebase(event)
    const newModuleKey = await firebasePush(`/modules`, moduleData)
    return {
      ok: true,
      message: `New module created successfully`,
      data: newModuleKey,
    }
  } catch (error) {
    return {
      ok: false,
      errorMessage: error,
    }
  }
})
