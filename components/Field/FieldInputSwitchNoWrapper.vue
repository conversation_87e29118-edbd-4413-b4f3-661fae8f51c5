<script setup lang="ts">
defineProps({
    modelValue: {
        type: <PERSON>olean
    },
})

const emit = defineEmits(['update:modelValue'])

function updateHandler(text: string)
{
    emit('update:modelValue', text)
}
</script>

<template>
    <Switch :modelValue="modelValue" @update:modelValue="updateHandler" size="large" true-color="#0f6cbd"
        false-color="#adadad">
        <template #open>
            Yes
        </template>
        <template #close>
            No
        </template>
    </Switch>
</template>

<style lang="scss" scoped>
.field {
    width: 100%;
    margin-bottom: $margin;
}
</style>