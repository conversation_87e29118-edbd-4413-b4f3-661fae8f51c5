// DELETE multiple workflows
// body { workflowKeys: string[] }

export default defineEventHandler(async (event) => {
  const { workflowKeys } = await readBody(event)
  const { db } = useFirebaseAdmin()
  if (!workflowKeys) {
    return {
      ok: false,
      errorMessage: `workflowKeys is required`,
    }
  }

  try {
    const updates: { [key: string]: null } = {}

    workflowKeys.forEach(async (workflowKey: string) => {
      updates[`workflows/${workflowKey}`] = null
    })
    await db.ref().update(updates)
    return {
      ok: true,
      message: `Sucessfully delete ${workflowKeys.length} workflows`,
    }
  } catch (error) {
    return {
      ok: false,
      errorMessage: error,
    }
  }
})
