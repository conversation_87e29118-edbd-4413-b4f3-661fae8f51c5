export enum ModuleTabNames {
  SETTINGS = 'settings',
  STAGES = 'stages',
  FORMS = 'forms',
  SHAREPOINT_CONFIG = 'sharepoint-config',
  REGISTERS = 'registers',
  ACTIONCONFIG = 'action-config',
  PERMISSIONS = 'permissions',
  REPORTS = 'reports',
  CONFIGSPECS = 'config-specs',
}

export enum FormTabNames {
  SETTINGS = 'settings',
  FORMS = 'forms',
  SHAREPOINT_CONFIG = 'sharepoint-config',
  PERMISSIONS = 'permission',
  REGISTERS = 'registers',
}

export enum SiteCollectionTabNames {
  HOME = 'home',
  SETTINGS = 'settings',
  PERMISSIONS = 'permissions',
  THEME = 'theme',
  CUSTOM_NAVIGATION = 'custom-navigation',
  DCC = 'dcc',
  RCC = 'rcc',
  GROUPS = 'groups',
  DEPLOYMENTS = 'deployments',
}

export enum CoreTabNames {
  SITE_COLUMNS = 'site-columns',
  LISTS = 'lists',
  SITE_COLUMNS_LIST = 'site-columns-list',
}
