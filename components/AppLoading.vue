<script setup lang="ts">
const uiStore = useUiStore();
const { loadingMessage } = storeToRefs(uiStore);
</script>

<template>
  <div class="loading-overlay">
    <div class="loading">
      <AppSpinner class="loading--ring" />
      <div class="loading--message">{{ loadingMessage }}</div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.loading-overlay {
  color: $primary-blue;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.8);
  /* Semi-transparent background */
  display: flex;
  /* Center spinner */
  justify-content: center;
  align-items: center;
  z-index: 10;
  /* Initially transparent */
  transition: opacity 0.3s ease;
  /* Smooth fade-in/out */
}

.loading {
  display: grid;
  align-items: center;
  justify-content: center;
  row-gap: $margin;

  &--ring {
    margin-right: auto;
    margin-left: auto;
  }

  &--message {
    font-size: 14px;
    color: $loading-blue-text;
  }
}
</style>
