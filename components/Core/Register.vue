<script setup lang='ts'>
import { storeToRefs } from 'pinia';
import type { Column } from '@/types/data';

const router = useRouter()
const coresStore = useCoresStore();
const { cores } = storeToRefs(coresStore);

const columns: Column[] = [
  { title: 'Name', key: 'name', sortType: 'asc' },
  {
    title: 'Action',
    slot: 'action',
    width: 150,
    align: 'center'
  }
]

function clickHandler(coreKey: string) {
  router.push('/cores/' + coreKey)
}

</script>

<template>
  <BaseRegisterList :list="cores" :columns="columns">
    <template #action="{ row }">
      <Button @click="clickHandler(row.key)">
        <Icon type="md-cog" />
      </Button>
    </template>
  </BaseRegisterList>
</template>