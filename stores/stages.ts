import type { Stage } from '~/types/data'
import { useStoreUtils } from './utils/storeFetchUtils'
import { useApiMutation } from './utils/apiMutation'

export const useStagesStore = defineStore('stages', () => {
  const uiStore = useUiStore()
  const { convertFirebaseDataToArray } = useStoreUtils()
  const workflowsStore = useWorkflowsStore()
  const activeWorkflowKey = computed(() => workflowsStore.activeWorkflowKey)

  const stages = ref([] as Stage[])
  const activeStageKey = ref<string>('')
  const stagesByWorkflowKey = computed(() => useGroupBy(stages.value, 'workflowKey'))
  const stageByFirebaseKey = computed(() => useKeyBy(stages.value, 'firebaseKey'))
  const workflowStages = computed(() => {
    const key = workflowsStore.activeWorkflowKey
    const groups = stagesByWorkflowKey.value

    if (!key || !groups || typeof groups !== 'object') {
      return []
    }

    const stagesForKey = groups[key]
    return Array.isArray(stagesForKey) ? getStagesByOrder(stagesForKey) : []
  })
  const activeStage = computed(
    () => stageByFirebaseKey.value[activeStageKey.value] || ({} as Stage),
  )

  // ----------------- Callbacks -----------------
  function stagesCallback(firebaseStageData: FirebaseData<Partial<Stage>>): void {
    stages.value = convertFirebaseDataToArray(firebaseStageData, 'firebaseKey')
  }

  function resetActiveStage() {
    activeStageKey.value = ''
  }

  function setActiveStageKey(key: string) {
    activeStageKey.value = key
  }

  const isStageKeyFoundWithStageName = (name: string) => {
    return workflowStages.value.some((stage) => stage.name === name)
  }

  watch([stages, () => workflowsStore.activeWorkflowKey], () => {
    if (!stages.value) return

    const key = workflowsStore.activeWorkflowKey

    if (!key) {
      console.log('activeWorkflowKey is undefined in stages.ts watcher')
      return
    }

    const isStageExist =
      stagesByWorkflowKey.value[key] !== undefined && stagesByWorkflowKey.value[key].length > 0

    if (isStageExist) {
      const firstStage = workflowStages.value?.[0]
      if (firstStage) {
        setActiveStageKey(firstStage.firebaseKey)
      }
    } else {
      resetActiveStage()
    }
  })

  function getStagesByOrder(stages: Stage[]) {
    if (!stages) return []
    return useSortBy(stages, [
      function (stage) {
        return stage.order
      },
    ])
  }

  function getStageByFirebaseKey(firebaseKey: string) {
    return stageByFirebaseKey.value[firebaseKey]
  }

  function getWorkflowStagesByWorkflowKey(workflowKey: string) {
    const filteredStages = stages.value.filter((stage) => stage.workflowKey === workflowKey)
    return getStagesByOrder(filteredStages)
  }

  function getNextStageOrder() {
    const stages = workflowStages.value
    if (stages.length === 0) return 0
    return stages.length - 1
  }

  function getStageFirebaseKeyFromStageName(stageName: string) {
    // Get stage firebase key from active Workflow Stages
    const stage = workflowStages.value.find((stage) => stage.name === stageName)
    return stage?.firebaseKey
  }

  const updateStage = async (stageKey: string, stageData: Partial<Stage>) => {
    await useApiMutation('/api/stages', {
      method: 'PATCH',
      body: {
        stageKey,
        stageData,
      },
    })
  }

  const updateStagesOrder = async (stages: Stage[]) => {
    const reorderedStages = stages.map((stage, index) => {
      return { ...stage, order: index }
    })
    await useApiMutation('/api/stages/order', {
      method: 'POST',
      body: {
        stages: reorderedStages,
      },
    })
  }

  async function createNewStage(workflowKey: string, stageName: string) {
    const newStage = {
      workflowKey,
      order: getNextStageOrder(),
      name: stageName,
    }
    await useApiMutation('/api/stages', { method: 'POST', body: { stageData: newStage } })
  }

  async function createStageBulk(stages: Stage[]) {
    await useApiMutation('/api/stages/bulk', { method: 'POST', body: { stages } })
  }

  async function deleteStage(stageKey: string) {
    await useApiMutation('/api/stages', { method: 'DELETE', body: { stageKey } })
  }

  async function deleteStageBulk(stageKeys: string[]) {
    await useApiMutation('/api/stages/bulk', { method: 'DELETE', body: { stageKeys } })
  }

  async function copyWorkflowStages(selectedWorkflowKey: string) {
    uiStore.setLoading(true)
    const selectedWorkflowStages = getWorkflowStagesByWorkflowKey(selectedWorkflowKey)
    const newStages = selectedWorkflowStages.map((stage) => {
      return convertStagetoWorkflow(stage, activeWorkflowKey.value)
    })

    await deleteStageBulk(workflowStages.value.map((stage) => stage.firebaseKey))
    await createStageBulk(newStages)
    uiStore.setLoading(false)
  }

  function convertStagetoWorkflow(stage: Stage, workflowKey: string) {
    // Strip stage keys
    stage.firebaseKey = null as any
    stage.key = null as any
    stage.workflowKey = workflowKey
    return stage
  }

  return {
    stagesCallback,
    isStageKeyFoundWithStageName,

    stages,
    workflowStages,
    stagesByWorkflowKey,
    stageByFirebaseKey,
    activeStage,
    updateStage,
    deleteStage,
    deleteStageBulk,
    updateStagesOrder,
    setActiveStageKey,
    getWorkflowStagesByWorkflowKey,
    getStageFirebaseKeyFromStageName,
    createNewStage,
    getStageByFirebaseKey,
    copyWorkflowStages,
  }
})
