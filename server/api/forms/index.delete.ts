// DELETE form
// body { formKey: string }

import { useAuthenticatedFirebase } from '~/server/utils/firebaseDatabase'

export default defineEventHandler(async (event) => {
  const { formKey } = await readBody(event)

  if (!formKey) {
    return {
      ok: false,
      errorMessage: 'formKey is required',
    }
  }

  try {
    const { firebaseRemove } = useAuthenticatedFirebase(event)
    await firebaseRemove(`/forms/${formKey}`)
    return {
      ok: true,
      data: null,
      message: `Form with key: ${formKey} deleted successfully`,
    }
  } catch (error) {
    return {
      ok: false,
      errorMessage: error,
    }
  }
})
