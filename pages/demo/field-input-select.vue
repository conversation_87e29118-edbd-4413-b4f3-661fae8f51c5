<script setup lang="ts">
  definePageMeta({
    layout: 'default',
  })

  // Sample data for demonstrations
  const simpleOptions = [
    '<PERSON>',
    'Banana',
    '<PERSON>',
    'Date',
    '<PERSON>berry',
    'Fig',
    'Grape',
    'Honeydew',
  ]

  const complexOptions = [
    { id: 1, name: '<PERSON>', role: 'Developer', department: 'Engineering' },
    { id: 2, name: '<PERSON>', role: 'Designer', department: 'Design' },
    { id: 3, name: '<PERSON>', role: 'Manager', department: 'Engineering' },
    { id: 4, name: '<PERSON>', role: 'Analyst', department: 'Business' },
    { id: 5, name: '<PERSON>', role: 'Developer', department: 'Engineering' },
    { id: 6, name: '<PERSON>', role: 'Designer', department: 'Design' },
    { id: 7, name: '<PERSON>', role: 'QA Engineer', department: 'Quality' },
    { id: 8, name: '<PERSON>', role: 'Product Manager', department: 'Product' },
    { id: 9, name: '<PERSON>', role: '<PERSON><PERSON>ps Engineer', department: 'Engineering' },
    { id: 10, name: '<PERSON>', role: 'UX Researcher', department: 'Design' },
  ]

  const countryOptions = [
    { code: 'US', name: 'United States', continent: 'North America' },
    { code: 'CA', name: 'Canada', continent: 'North America' },
    { code: 'UK', name: 'United Kingdom', continent: 'Europe' },
    { code: 'DE', name: 'Germany', continent: 'Europe' },
    { code: 'FR', name: 'France', continent: 'Europe' },
    { code: 'JP', name: 'Japan', continent: 'Asia' },
    { code: 'AU', name: 'Australia', continent: 'Oceania' },
    { code: 'BR', name: 'Brazil', continent: 'South America' },
    { code: 'IN', name: 'India', continent: 'Asia' },
    { code: 'ZA', name: 'South Africa', continent: 'Africa' },
  ]

  // Reactive form data
  const formData = reactive({
    simpleFruit: '',
    employee: '',
    country: '',
    readonlyValue: 'Apple',
    clearableValue: 'Banana',
    nonSearchableValue: '',
  })

  // Demo functions
  function resetForm() {
    Object.keys(formData).forEach((key) => {
      if (key !== 'readonlyValue') {
        formData[key as keyof typeof formData] = ''
      }
    })
  }

  function showFormData() {
    alert(JSON.stringify(formData, null, 2))
  }
</script>

<template>
  <div class="demo-page">
    <div class="demo-header">
      <h1>Enhanced FieldInputSelect Component Demo</h1>
      <p>
        This page demonstrates the enhanced FieldInputSelect component with search functionality,
        autocomplete behavior, improved styling, and proper z-index handling.
      </p>
      <div class="demo-features">
        <div class="feature">
          ✨ <strong>Autocomplete:</strong> Selected values appear in search input
        </div>
        <div class="feature">
          🔍 <strong>Smart Search:</strong> Debounced filtering with 300ms delay
        </div>
        <div class="feature">
          🎯 <strong>Click to Edit:</strong> Click dropdown to search and modify selection
        </div>
        <div class="feature">
          ⚡ <strong>High Z-Index:</strong> Dropdown appears above all other elements
        </div>
      </div>
    </div>

    <div class="demo-sections">
      <!-- Basic Usage -->
      <section class="demo-section">
        <h2>Basic Usage</h2>
        <p>Simple string array options with search functionality</p>
        <div class="demo-form">
          <FieldInputSelect
            v-model="formData.simpleFruit"
            label="Select a Fruit"
            placeholder="Choose your favorite fruit..."
            :options="simpleOptions" />
          <div class="selected-value">Selected: {{ formData.simpleFruit || 'None' }}</div>
        </div>
      </section>

      <!-- Complex Objects with Tags -->
      <section class="demo-section">
        <h2>Complex Objects with Value/Label Tags</h2>
        <p>Using objects with valueTag and labelTag properties</p>
        <div class="demo-form">
          <FieldInputSelect
            v-model="formData.employee"
            label="Select Employee"
            placeholder="Search employees..."
            :options="complexOptions"
            valueTag="id"
            labelTag="name">
            <template #default="{ option }">
              <div class="employee-option">
                <strong>{{ option.name }}</strong>
                <div class="employee-details">{{ option.role }} - {{ option.department }}</div>
              </div>
            </template>
          </FieldInputSelect>
          <div class="selected-value">Selected ID: {{ formData.employee || 'None' }}</div>
        </div>
      </section>

      <!-- Custom Slot Content -->
      <section class="demo-section">
        <h2>Custom Slot Content</h2>
        <p>Rich content in dropdown options</p>
        <div class="demo-form">
          <FieldInputSelect
            v-model="formData.country"
            label="Select Country"
            placeholder="Search countries..."
            :options="countryOptions"
            valueTag="code"
            labelTag="name">
            <template #default="{ option }">
              <div class="country-option">
                <div class="country-flag">{{ option.code }}</div>
                <div class="country-info">
                  <div class="country-name">{{ option.name }}</div>
                  <div class="country-continent">{{ option.continent }}</div>
                </div>
              </div>
            </template>
          </FieldInputSelect>
          <div class="selected-value">Selected: {{ formData.country || 'None' }}</div>
        </div>
      </section>

      <!-- Special States -->
      <section class="demo-section">
        <h2>Special States</h2>
        <div class="demo-form-grid">
          <div class="demo-form">
            <FieldInputSelect
              v-model="formData.readonlyValue"
              label="Readonly Select"
              :options="simpleOptions"
              readonly />
            <div class="selected-value">Value: {{ formData.readonlyValue }}</div>
          </div>

          <div class="demo-form">
            <FieldInputSelect
              v-model="formData.clearableValue"
              label="Clearable Select"
              placeholder="Select with clear option..."
              :options="simpleOptions"
              clearable />
            <div class="selected-value">Value: {{ formData.clearableValue || 'None' }}</div>
          </div>

          <div class="demo-form">
            <FieldInputSelect
              v-model="formData.nonSearchableValue"
              label="Non-searchable Select"
              placeholder="No search functionality..."
              :options="simpleOptions"
              :searchable="false" />
            <div class="selected-value">Value: {{ formData.nonSearchableValue || 'None' }}</div>
          </div>
        </div>
      </section>

      <!-- Actions -->
      <section class="demo-section">
        <h2>Actions</h2>
        <div class="demo-actions">
          <Button type="primary" @click="showFormData">Show Form Data</Button>
          <Button @click="resetForm">Reset Form</Button>
        </div>
      </section>
    </div>
  </div>
</template>

<style lang="scss" scoped>
  .demo-page {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
    font-family: $fluentui-font-stack;
  }

  .demo-header {
    text-align: center;
    margin-bottom: 3rem;

    h1 {
      color: $primary-text;
      margin-bottom: 1rem;
    }

    p {
      color: #666;
      font-size: 1.1rem;
      max-width: 600px;
      margin: 0 auto 2rem;
    }

    .demo-features {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 1rem;
      max-width: 800px;
      margin: 0 auto;

      .feature {
        background: #f8f9fa;
        padding: 1rem;
        border-radius: 6px;
        font-size: 0.9rem;
        text-align: left;
        border-left: 3px solid $primary-lightblue;
      }
    }
  }

  .demo-sections {
    display: flex;
    flex-direction: column;
    gap: 3rem;
  }

  .demo-section {
    background: $primary-white;
    border-radius: 8px;
    padding: 2rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    h2 {
      color: $primary-text;
      margin-bottom: 0.5rem;
    }

    p {
      color: #666;
      margin-bottom: 1.5rem;
    }
  }

  .demo-form {
    max-width: 400px;

    .selected-value {
      margin-top: 0.5rem;
      padding: 0.5rem;
      background: #f8f9fa;
      border-radius: 4px;
      font-size: 0.9rem;
      color: #666;
    }
  }

  .demo-form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
  }

  .demo-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
  }

  // Custom option styles
  .employee-option {
    .employee-details {
      font-size: 0.85rem;
      color: #666;
      margin-top: 0.25rem;
    }
  }

  .country-option {
    display: flex;
    align-items: center;
    gap: 0.75rem;

    .country-flag {
      background: $primary-lightblue;
      color: white;
      padding: 0.25rem 0.5rem;
      border-radius: 4px;
      font-size: 0.75rem;
      font-weight: bold;
      min-width: 2rem;
      text-align: center;
    }

    .country-info {
      flex: 1;

      .country-name {
        font-weight: 500;
      }

      .country-continent {
        font-size: 0.85rem;
        color: #666;
        margin-top: 0.125rem;
      }
    }
  }

  @media (max-width: 768px) {
    .demo-page {
      padding: 1rem;
    }

    .demo-form-grid {
      grid-template-columns: 1fr;
    }
  }
</style>
