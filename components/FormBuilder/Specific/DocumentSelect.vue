<script setup lang="ts">
const { documentSelectField } = useFormBuilderFieldSetting()
</script>

<template>
    <FormBuilderFieldSettingLabel>Document Select Settings</FormBuilderFieldSettingLabel>
    <template v-if="documentSelectField">
        <Form label-position="top">
            <FieldInputText v-model="documentSelectField.folders" label="Folders (comma seperated)" />
            <FieldInputSwitch v-model="documentSelectField.useCustomChangeType" label="Use Custom Change Type" />
            <FormBuilderSpecificOptionSettings v-model="documentSelectField.options" header-label="Document Names" />
        </Form>
    </template>
</template>