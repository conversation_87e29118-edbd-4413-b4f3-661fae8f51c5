<script setup lang="ts">
import { FormFieldType } from '~/types/form';
const { activeEditField } = useFormBuilderFieldSetting()
const ignoreFieldname = [FormFieldType.IMAGE, FormFieldType.TEXT]
const ignoreWidth = [FormFieldType.IMAGE]
</script>

<template>
  <div class="fieldSettingGeneral">
    <Form label-position="left">
      <FieldInputText v-model="activeEditField.label" label="Label" />
      <FieldInputText v-model="activeEditField.fieldName" label="Field Name"
        v-if="!ignoreFieldname.includes(activeEditField.type)" />
      <FieldInputNumber v-model="activeEditField.width" label="Width (%)"
        v-if="!ignoreWidth.includes(activeEditField.type)" />
      <FieldInputSwitch v-model="activeEditField.noValidation" label="No Validation Needed" />
      <FieldInputSwitch v-model="activeEditField.readOnly" label="Read Only" />
    </Form>
  </div>
</template>