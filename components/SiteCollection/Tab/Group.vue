<script lang="ts" setup>
import { SiteCollectionTabNames } from '~/constants/tabs'

const router = useRouter()
const route = useRoute()

onMounted(() =>
{
  router.replace({
    query: {
      ...route.query,
      tab: SiteCollectionTabNames.GROUPS
    }
  })
})
</script>

<template>
  <div class="siteCollectionTabGroups">
    <SiteCollectionTabGroupManager />
    <SiteCollectionTabGroupUsers />
  </div>
</template>

<style lang="scss" scoped>
.siteCollectionTabGroups {
  display: grid;
  grid-template-columns: 255px 1fr;
  column-gap: $margin;
}
</style>