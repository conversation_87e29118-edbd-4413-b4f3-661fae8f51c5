// stores/sharepointAPI.ts

import { defineStore } from 'pinia'
import { useApiFetch } from '~/stores/utils/apiFetch'
import { useApiMutation } from '~/stores/utils/apiMutation'
import { type CustomOptionData, type FormFieldData } from '~/types/form'
import type { SiteColumnField } from '~/types/sharepointConfig'
import type { SharepointField, SiteGroup, SiteList, SiteUser } from '~/types/sharepointAPI'

// ---------- State ----------
export const useSharepointAPIStore = defineStore('sharepointAPI', () => {
  const { checkFieldCanBeAdded, convertFormFieldToSharepointField } = useSharepointFieldUtils()
  const tenantId = ref('')
  const tenantUrl = ref('')
  const siteUrl = ref('')
  const activeGroup = ref<SiteGroup | null>(null)
  const userInActiveGroup = ref<SiteUser[]>([])
  const groupsExisting = ref<SiteGroup[]>([])
  const listsExisting = ref<SiteList[]>([])
  const usersExisting = ref<SiteUser[]>([])
  const siteColumnsExisting = ref<SharepointField[]>([])
  const storeLoading = ref(false)

  const resetSharepointAPIState = () => {
    tenantId.value = ''
    tenantUrl.value = ''
    siteUrl.value = ''
    activeGroup.value = null
    userInActiveGroup.value = []
    groupsExisting.value = []
    listsExisting.value = []
    usersExisting.value = []
    siteColumnsExisting.value = []
    storeLoading.value = false
  }

  const setSharepointAPIState = (
    tenantUrlValue: string,
    tenantIdValue: string,
    siteUrlValue: string,
  ) => {
    tenantUrl.value = tenantUrlValue
    tenantId.value = tenantIdValue
    siteUrl.value = `${tenantUrlValue}${siteUrlValue}`
  }
  // ---------- Fetch (GET) ----------

  const fetchSiteGroups = async () => {
    await useApiFetch<SiteGroup[]>(
      '/api/sharepoint/groups',
      (data) => {
        groupsExisting.value = data
      },
      { tenantUrl: tenantUrl.value, tenantId: tenantId.value, siteUrl: siteUrl.value },
      {
        errorNotice: { title: 'Sharepoint Site Groups Error' },
      },
    )
  }

  const fetchLists = async () => {
    await useApiFetch<SiteList[]>(
      '/api/sharepoint/lists',
      (data) => {
        listsExisting.value = data
      },
      { tenantUrl: tenantUrl.value, tenantId: tenantId.value, siteUrl: siteUrl.value },
      {
        errorNotice: { title: 'Sharepoint Lists Error' },
      },
    )
  }

  const fetchUsers = async () => {
    await useApiFetch<SiteUser[]>(
      '/api/sharepoint/users',
      (data) => {
        usersExisting.value = data
      },
      { tenantUrl: tenantUrl.value, tenantId: tenantId.value, siteUrl: siteUrl.value },
      {
        errorNotice: { title: 'Sharepoint User Error' },
      },
    )
  }

  const fetchSiteColumns = async () => {
    await useApiFetch<SharepointField[]>(
      '/api/sharepoint/sitecolumns',
      (data) => {
        siteColumnsExisting.value = data
      },
      { tenantUrl: tenantUrl.value, tenantId: tenantId.value, siteUrl: siteUrl.value },
      {
        errorNotice: { title: 'Sharepoint Site Columns Error' },
      },
    )
  }

  const fetchDeferUri = async (uri: string) => {
    storeLoading.value = true
    let result: any[] = []
    await useApiFetch<any[]>(
      '/api/sharepoint/defer',
      (data) => {
        result = data
      },
      { uri, tenantUrl: tenantUrl.value, tenantId: tenantId.value },
      {
        errorNotice: { title: 'Sharepoint Defer Uri Error' },
      },
    )
    storeLoading.value = false
    return result
  }

  const fetchSiteColumnsInList = async (listTitle: string) => {
    let result: SiteColumnField[] = []
    await useApiFetch<SiteColumnField[]>(
      '/api/sharepoint/sitecolumns/getByTitle',
      (data) => {
        console.log(data)
        result = data
      },
      { tenantUrl: tenantUrl.value, tenantId: tenantId.value, siteUrl: siteUrl.value, listTitle },
      {
        errorNotice: { title: 'Sharepoint Site Columns Error' },
      },
    )
    return result
  }

  const fetchDocumentLibraries = async () => {
    let result: any[] = []
    await useApiFetch<any[]>(
      'api/sharepoint/documentLibrary',
      (data) => {
        result = data
        console.log(data)
      },
      { tenantUrl: tenantUrl.value, tenantId: tenantId.value, siteUrl: siteUrl.value },
      {
        errorNotice: { title: 'Sharepoint Document Libraries Error' },
      },
    )
    return result
  }

  // ---------- Mutation (POST/PATCH/DELETE) ----------
  const addSiteGroup = async (groupName: string) =>
    await useApiMutation('/api/sharepoint/groups', {
      method: 'POST',
      params: { tenantUrl: tenantUrl.value, tenantId: tenantId.value, siteUrl: siteUrl.value },
      body: { group: { __metadata: { type: 'SP.Group' }, Title: groupName } },
    })

  const removeUserFromGroup = async (groupId: number, userId: number) => {
    storeLoading.value = true
    const res = await useApiMutation('/api/sharepoint/groups/user', {
      method: 'DELETE',
      params: { tenantUrl: tenantUrl.value, tenantId: tenantId.value, siteUrl: siteUrl.value },
      body: { groupId, userId },
    })
    storeLoading.value = false
    return res
  }

  const addUserToGroup = async (groupId: number, userLoginName: string) => {
    storeLoading.value = true
    const res = await useApiMutation('/api/sharepoint/groups/user', {
      method: 'POST',
      params: { tenantUrl: tenantUrl.value, tenantId: tenantId.value, siteUrl: siteUrl.value },
      body: { groupId, user: { __metadata: { type: 'SP.User' }, LoginName: userLoginName } },
    })
    storeLoading.value = false
    return res
  }

  const addSiteList = async (listTitle: string) =>
    await useApiMutation('/api/sharepoint/lists', {
      method: 'POST',
      params: { tenantUrl: tenantUrl.value, tenantId: tenantId.value, siteUrl: siteUrl.value },
      body: {
        list: {
          __metadata: { type: 'SP.List' },
          Title: listTitle,
          BaseTemplate: 100,
          ContentTypesEnabled: true,
          AllowContentTypes: true,
        },
      },
      refetch: async () => await fetchLists(),
    })

  const addFieldToList = async (field: AVAILABLEFIELDTYPES, listTitle: string) =>
    await useApiMutation('/api/sharepoint/lists/getByTitle/field', {
      method: 'POST',
      params: { tenantUrl: tenantUrl.value, tenantId: tenantId.value, siteUrl: siteUrl.value },
      body: { listTitle, field: convertFormFieldToSharepointField(field) },
    })

  const addSystemFieldToList = async (fieldName: string, listTitle: string) =>
    await useApiMutation('/api/sharepoint/lists/getByTitle/field', {
      method: 'POST',
      params: { tenantUrl: tenantUrl.value, tenantId: tenantId.value, siteUrl: siteUrl.value },
      body: {
        listTitle,
        field: { __metadata: { type: 'SP.FieldText' }, Title: fieldName, FieldTypeKind: 2 },
      },
    })

  const deleteFieldFromList = async (listTitle: string, fieldId: string) =>
    await useApiMutation('/api/sharepoint/lists/getByTitle/field', {
      method: 'DELETE',
      params: { tenantUrl: tenantUrl.value, tenantId: tenantId.value, siteUrl: siteUrl.value },
      body: { listTitle, fieldId },
    })

  // ---------- Utils ----------
  const getListByInternalName = (title: string) =>
    listsExisting.value.find((list) => list.Title === title) || ({} as SiteList)

  const checkListExist = (listName: string) =>
    listsExisting.value.some((list) => list.Title === listName)

  const checkGroupExist = (groupName: string) =>
    groupsExisting.value.some((group) => group.Title === groupName)

  const isFieldCanBeAdded = (field: FormFieldData | CustomOptionData) =>
    checkFieldCanBeAdded(field.type)

  const setActiveGroup = async (group: SiteGroup) => {
    storeLoading.value = true
    activeGroup.value = group
    userInActiveGroup.value = await fetchDeferUri(group.Users.__deferred.uri)
    storeLoading.value = false
  }

  const clearActiveGroup = () => {
    activeGroup.value = null
    userInActiveGroup.value = []
  }

  const setActiveGroupWithTitle = async (title: string) => {
    const group = groupsExisting.value.find((group) => group.Title === title)
    if (group) await setActiveGroup(group)
  }

  // ---------- Expose ----------
  return {
    // State
    tenantId,
    siteUrl,
    activeGroup,
    userInActiveGroup,
    groupsExisting,
    listsExisting,
    usersExisting,
    siteColumnsExisting,
    storeLoading,

    // Fetch
    fetchSiteGroups,
    fetchLists,
    fetchUsers,
    fetchSiteColumns,
    fetchDeferUri,
    fetchSiteColumnsInList,
    fetchDocumentLibraries,

    // Mutations
    addSiteGroup,
    removeUserFromGroup,
    addUserToGroup,
    addSiteList,
    addFieldToList,
    addSystemFieldToList,
    deleteFieldFromList,

    // Utils
    setSharepointAPIState,
    getListByInternalName,
    checkListExist,
    checkGroupExist,
    isFieldCanBeAdded,
    resetSharepointAPIState,
    setActiveGroup,
    clearActiveGroup,
    setActiveGroupWithTitle,
  }
})
