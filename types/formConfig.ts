import type { FormFieldData } from "./form";
import type { PermissionSetting } from "./permission";
import type { RegisterSetting } from "./register";
import type { SharepointSetting } from "./sharepointConfig";
import type { UpVersionSetting } from "./upVersion";

export type FormConfig = FormConfigSetting &
  SharepointSetting &
  RegisterSetting &
  PermissionSetting &
  FormConfigForms &
  UpVersionSetting & {
    //firebase key
    key: string;

    //key that is listed in siteCollection.forms
    formKey: string;
    created: string;
    version: string;
    inUse: boolean;
  };

export type FormConfigForms = {
  form: FormFieldData[];
};

export type FormConfigSetting = {
  name: string;
  description: string;
  icon: string;
  type: string;
  workspaceTitle: string;
  workspaceIcon: string;
  registerHeaderName: string;
  prefix: string;
  formRegister: string;
  showInMenu: boolean;
};
