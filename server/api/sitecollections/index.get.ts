// GET ALL SiteCollection
import { useAuthenticatedFirebase } from '~/server/utils/firebaseDatabase'

export default defineEventHandler(async (event) => {
  try {
    const { firebaseFetch } = useAuthenticatedFirebase(event)
    const siteCollections = await firebaseFetch('/siteCollections')
    return {
      ok: true,
      message: 'Site Collections fetched successfully',
      data: siteCollections,
    }
  } catch (error) {
    return {
      ok: false,
      errorMessage: error,
    }
  }
})
