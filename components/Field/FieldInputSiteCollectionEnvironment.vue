<script setup lang="ts">
  defineProps({
    modelValue: {
      type: String,
      default: 'Dev',
    },
    label: {
      type: String,
    },
  })

  const { environmentOptions } = useSelectOptionsUtils()
  const emit = defineEmits(['update:modelValue'])
</script>

<template>
  <FieldInput :label="label">
    <Select
      :modelValue="modelValue"
      @update:modelValue="$emit('update:modelValue', $event)"
      transfer>
      <Option v-for="option in environmentOptions" :key="option.value" :value="option.value">
        <span>{{ option.label }}</span>
      </Option>
    </Select>
  </FieldInput>
</template>
