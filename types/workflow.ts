import type { PermissionSetting } from "./permission";
import type { RegisterSetting } from "./register";
import type { SharepointSetting } from "./sharepointConfig";

export type Key = {
  key: string;
};

export type Created = {
  created: string;
};

export type InUse = {
  inUse: boolean;
};

export type UpVersionSetting = Key &
  Created &
  InUse & {
    changeLog: string;
    version: string;
  };

export type Workflow = UpVersionSetting &
  WorkflowSetting &
  SharepointSetting &
  PermissionSetting &
  RegisterSetting &
  WorkflowReports &
  Key &
  Created &
  InUse &
  (OldWorkflowAction | WorkflowAction) & {
    name: string;
    moduleKey: string;
  };

export type WorkflowSetting = {
  icon: string;
  type: string;
  qmspublishing: string;
  tenant: string;
  tenantUrl: string;
  siteCollection: string;
  moduleSubsite: string;
  prefix: string;
  registerHeaderName: string;
  mainRegisterList: string;
  notificationStatus: boolean;
  eSignatureRequired: boolean;
  assignedActionOverride: boolean;
  hideUploadDocument: boolean;
  multiLevelRevisionNumber: boolean;
};

// Workflow Action

export type WorkflowAction = {
  actions: NewWorkflowAction[];
};

export enum ActionType {
  GROUP = "Group",
  PERSON = "Person",
  FIELDNAME = "FieldName",
  LIST = "List",
  CL = "CL",
}

export enum ActionStatus {
  NOTSTARTED = "Not Started",
  INREVIEW = "In Review",
  PENDING = "Pending",
}

export enum ActionProcess {
  S = "S",
  P = "P",
}

export type Action = {
  ActionDescription: string;
  AssignedToId: string[] | string;
  Stage: string;
  Status: ActionStatus;
  Type: ActionType;
  Process?: ActionProcess;
  ActiveCondition?: boolean;
};

export type OldWorkflowAction = {
  actions: { [name: string]: Action[] }[];
};

export type NewWorkflowAction = {
  // stageName only used to identify which stage the action belongs to
  // (if stageName can't be found with corresponding stageKey)
  stageName?: string;
  stageKey?: string;
  actions: Action[];
};

export type WorkflowInUse = {
  inUse: boolean;
};

export type WorkflowReports = {
  reports: Report[];
};

export type Report = {
  datasource: string;
  linkedList: string;
  name: string;
  rows: ReportRow[];
};

export type ReportRow = {
  type: RowType;
  items: ReportItem[];
};

export enum RowType {
  NORMAL = "normal",
  BREAK = "break",
  TIGHT = "tight",
  CUSTOMLISTS = "customlists",
  TEXTAREA = "textarea",
  ACTIONS = "actions",
}

export type ReportItem = {
  fieldName: string;
  label: string;
  type: ItemType;
  flex: string;
  width: number;
};

export enum ItemType {
  TEXT = "text",
  DATE = "date",
  PEOPLE = "people",
}
