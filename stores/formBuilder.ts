import { FormFieldType, type FormFieldData } from "~/types/form";
import { v4 as uuidv4 } from "uuid";
import type { Stage } from "~/types/data";

export enum FormBuilderFor
{
  FORM = "form",
  MODULE = "module",
}

export const useFormBuilderStore = defineStore("formBuilder", () =>
{
  const stageStore = useStagesStore();
  const formBuilderActiveStage = ref({} as Stage);

  const formConfigStore = useFormConfigStore();
  const { activeFormConfig } = storeToRefs(formConfigStore);

  const FORMFIELDS_KEY_STATE = ref<string[]>([]);

  const activeFormFields = ref([] as FormFieldData[]);
  const originalFormFields = ref([] as FormFieldData[]);

  // refs: for reset form field settings
  const activeField = ref({} as FormFieldData);

  // refs: what shows on form builder field settings
  const activeEditField = ref({} as FormFieldData);
  const activeEditFieldIndex = ref(null as number | null);

  const formBuilderFor = ref<FormBuilderFor>();
  const enableReview = ref(false);
  const enableMultipleSubmit = ref(false);

  const isFormBuilderEdited = computed(() =>
  {
    return !isEqual(activeFormFields.value, originalFormFields.value);
  });

  const isEditFieldSelected = computed(
    () => activeEditFieldIndex.value !== null
  );

  // MUST ONLY BE CALLED ONCE except adding component in canvas
  function initialiseFormFields(fields: FormFieldData[])
  {
    fields.map((field) => initialiseForm(field));
    activeFormFields.value = useCloneDeep(fields);
    originalFormFields.value = useCloneDeep(fields);
  }

  // Used for Form Builder Tab component selector
  function initialiseForm(field: FormFieldData)
  {
    if (typeof field === 'string') return;
    field.key = uuidv4();
    FORMFIELDS_KEY_STATE.value.push(field.key);
    field.width = field.width ? Number(field.width) : 100;
    field.conditions = field.conditions ? [...field.conditions] : [];
    field.conditionsToHide = field.conditionsToHide
      ? [...field.conditionsToHide]
      : [];
    if (field.type === FormFieldType.DATEPICKER)
    {
      field.daysAhead = field.daysAhead ? Number(field.daysAhead) : 0;
    }
    return field;
  }

  // Alternate initialiseFormFields re-initialise new form fields with key state
  function refreshFormFieldsWithKeyState(fields: FormFieldData[])
  {
    fields.map((field, index) => refreshForm(field, index));
    activeFormFields.value = useCloneDeep(fields);
  }

  // Alternate initialiseForm re-initialise new form field with key state
  function refreshForm(field: FormFieldData, index: number)
  {
    if (!field.key)
    {
      field.key = uuidv4();
      FORMFIELDS_KEY_STATE.value.splice(index, 0, field.key);
    }
    field.key = FORMFIELDS_KEY_STATE.value[index];
    field.width = field.width ? Number(field.width) : 100;
    field.conditions = field.conditions ? [...field.conditions] : [];
    field.conditionsToHide = field.conditionsToHide
      ? [...field.conditionsToHide]
      : [];
    if (field.type === FormFieldType.DATEPICKER)
    {
      field.daysAhead = field.daysAhead ? Number(field.daysAhead) : 0;
    }
    return field;
  }
  function initialiseFormBuilderForModule()
  {
    resetFormBuilderState();
    formBuilderFor.value = FormBuilderFor.MODULE;
    initialiseFormFields(formBuilderActiveStage.value.form || []);
    enableReview.value = formBuilderActiveStage.value.review;
    enableMultipleSubmit.value = formBuilderActiveStage.value.multipleSubmit;
  }

  function initialiseFormBuilderForForm()
  {
    resetFormBuilderState();
    formBuilderFor.value = FormBuilderFor.FORM;
    initialiseFormFields(activeFormConfig.value.form || []);

    enableReview.value = false;
    enableMultipleSubmit.value = false;
  }

  function refreshFormBuilder()
  {
    if (formBuilderFor.value === FormBuilderFor.MODULE)
    {
      resetFormBuilderState();
      initialiseFormFields(formBuilderActiveStage.value.form || []);
    }
    if (formBuilderFor.value === FormBuilderFor.FORM)
    {
      resetFormBuilderState();
      initialiseFormFields(activeFormConfig.value.form || []);
    }
  }

  function setActiveField(field: FormFieldData)
  {
    activeField.value = field;
  }

  function setEditField(field: FormFieldData, index: number)
  {
    activeEditField.value = field;
    activeEditFieldIndex.value = index;
  }

  function resetActiveField()
  {
    activeField.value = {} as FormFieldData;
  }

  function resetEditField()
  {
    activeEditField.value = {} as FormFieldData;
    activeEditFieldIndex.value = null;
  }

  function resetFormFieldKeyState()
  {
    FORMFIELDS_KEY_STATE.value = [];
  }

  function resetFormBuilderState()
  {
    resetActiveField();
    resetEditField();
    resetFormFieldKeyState();
    enableReview.value = false;
    enableMultipleSubmit.value = false;
  }

  function removeKeyFromField(field: FormFieldData)
  {
    const formData = useCloneDeep(field);
    formData.key = undefined as any;
    return formData as FormFieldData;
  }

  function setActiveStage(stage: Stage)
  {
    formBuilderActiveStage.value = useCloneDeep(stage);
  }

  function resetFormBuilder()
  {
    if (formBuilderFor.value === FormBuilderFor.MODULE)
    {
      initialiseFormBuilderForModule();
    } else
    {
      initialiseFormBuilderForForm();
    }
  }

  function deleteCanvasField(index: number)
  {
    activeFormFields.value.splice(index, 1);
    FORMFIELDS_KEY_STATE.value.splice(index, 1);
  }

  async function saveHandler()
  {
    const rawFields = useCloneDeep(activeFormFields.value);
    rawFields.map((field) => removeKeyFromField(field));
    if (formBuilderFor.value === FormBuilderFor.MODULE)
    {
      await stageStore.updateStage(formBuilderActiveStage.value.firebaseKey, {
        form: rawFields,
      });
      refreshFormBuilder();
    } else if (formBuilderFor.value === FormBuilderFor.FORM)
    {
      await formConfigStore.updateFormConfig(activeFormConfig.value.key, {
        form: rawFields,
      });
      refreshFormBuilder();
    }
  }

  return {
    // New
    formBuilderActiveStage,
    setActiveField,
    setEditField,
    activeFormFields,
    activeEditField,
    isEditFieldSelected,
    enableMultipleSubmit,
    enableReview,
    initialiseFormBuilderForForm,
    initialiseFormBuilderForModule,
    setActiveStage,
    saveHandler,
    resetFormBuilder,
    resetActiveField,
    resetEditField,
    isFormBuilderEdited,
    refreshFormFieldsWithKeyState,
    deleteCanvasField,
  };
});
