<script setup lang="ts">
const props = defineProps<{
  primaryColor: string, secondaryColor: string, accentColor: string
}>()

const emit = defineEmits<{
  (e: 'update:primaryColor', value: string): void
  (e: 'update:secondaryColor', value: string): void
  (e: 'update:accentColor', value: string): void
}>()
</script>

<template>
  <div>
    <div class="sectionLabel">Main</div>
    <div class="themeMenu">
      <FormItem label="primary">
        <ColorPicker :modelValue="primaryColor" @update:modelValue="emit('update:primaryColor', $event)" transfer />
      </FormItem>
      <FormItem label="secondary">
        <ColorPicker :modelValue="secondaryColor" @update:modelValue="emit('update:secondaryColor', $event)" transfer />
      </FormItem>
      <FormItem label="accent">
        <ColorPicker :modelValue="accentColor" @update:modelValue="emit('update:accentColor', $event)" transfer />
      </FormItem>
    </div>
  </div>
</template>

<style scoped lang="scss">
.themeMenu {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  align-items: start;
  justify-items: start;
}
.sectionLabel {
  font-size: 1rem;
  font-weight: 600;
}
</style>

