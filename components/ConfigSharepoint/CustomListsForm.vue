<script setup lang="ts">
import type { PropType } from 'vue';
import { CONFIG_SHAREPOINT_NEW_CUSTOM_LIST } from '~/constants/default-values';
import { CustomListType, type SharepointCustomList } from '~/types/sharepointConfig';

const props = defineProps({
    modelValue: {
        type: Object as PropType<SharepointCustomList[]>,
        default: () => []
    }
})
const emit = defineEmits(['update:modelValue'])

const fieldStore = useFieldStore()

function clickHandlerDelete(index: number)
{
    const newModelValue = [...props.modelValue]
    newModelValue.splice(index, 1)
    emit('update:modelValue', newModelValue)
}

function clickHandlerAdd()
{
    const newModelValue = [...props.modelValue]
    newModelValue.push(CONFIG_SHAREPOINT_NEW_CUSTOM_LIST)
    emit('update:modelValue', newModelValue)
}

const customListTypeOption = computed(() =>
{
    return Object.values(CustomListType).map((value) =>
    {
        return {
            label: value,
            value
        }
    })
})

const listOption = computed(() =>
{
    return fieldStore.getListOptions()
})

function updateTypeHandler(type: CustomListType, index: number)
{
    const newModelValue = [...props.modelValue]
    newModelValue[index].type = type
    emit('update:modelValue', newModelValue)
}

function updateListNameTableInputHandler(listname: string, index: number)
{
    const newModelValue = [...props.modelValue]
    newModelValue[index].listname = listname
    emit('update:modelValue', newModelValue)
}

</script>

<template>
    <Form>
        <template v-for="l, index in modelValue">
            <BaseLayoutRowForm gridLayout="25px 1fr 1fr 1fr 50px">
                <BaseNumberTag class="formTag">{{ index + 1 }}</BaseNumberTag>
                <FieldInput class="field">
                    <Select :model-value="modelValue[index].type" @update:model-value="updateTypeHandler($event, index)"
                        clearable transfer class="select">
                        <Option v-for="option in customListTypeOption" :value="option.value" :key="option.value"
                            :label="option.label">
                            <span>
                                {{ option.label }}</span>
                        </Option>
                    </Select>
                </FieldInput>
                <FieldInputText v-model="modelValue[index].fieldName" />
                <template v-if="modelValue[index].type === CustomListType.TABLEINPUT">
                    <FieldInput class="field">
                        <Select :model-value="modelValue[index].listname"
                            @update:model-value="updateListNameTableInputHandler($event, index)" transfer
                            class="select">
                            <Option v-for="option in listOption" :value="option.value" :key="option.value"
                                :label="option.label">
                                <span>
                                    {{ option.label }}</span>
                            </Option>
                        </Select>
                    </FieldInput>
                </template>
                <template v-else>
                    <FieldInputText v-model="modelValue[index].listname" />
                </template>
                <BaseButtonDelete @click="clickHandlerDelete(index)" class="formButton" />
            </BaseLayoutRowForm>
        </template>
    </Form>
    <BaseButtonAdd @click="clickHandlerAdd" text="Custom List" />
</template>

<style lang="scss" scoped>
.field {
    width: 100%;
    max-width: 100%;


    .select {
        overflow: auto;
        text-overflow: ellipsis;
    }
}
</style>