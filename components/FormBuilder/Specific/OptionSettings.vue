<script setup lang="ts">
  import { FORM_NEW_OPTION } from '~/constants/default-values'
  import type { FieldOption } from '~/types/form'

  const emit = defineEmits(['update:modelValue'])
  const props = defineProps({
    modelValue: {
      type: Array as PropType<FieldOption[]>,
      default: () => [],
    },
    headerLabel: {
      type: String,
      default: 'Option',
    },
  })

  function addHandler() {
    const newModelValue = useClone(props.modelValue)
    newModelValue.push(useClone(FORM_NEW_OPTION))
    emit('update:modelValue', newModelValue)
  }

  function clickHandlerDelete(index: number) {
    const newModelValue = useClone(props.modelValue)
    newModelValue.splice(index, 1)
    emit('update:modelValue', newModelValue)
  }
</script>

<template>
  <FormBuilderSpecificOptionHeader :label="headerLabel" @add="addHandler" />
  <template v-for="(option, index) in modelValue">
    <div class="optionField">
      <FieldInputTextNoWrapper v-model="modelValue[index].name" class="field" />
      <BaseButtonDelete @click="clickHandlerDelete(index)" />
    </div>
  </template>
</template>

<style lang="scss" scoped>
  .field {
    width: 100%;
  }

  .optionField {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-direction: row;
    margin-bottom: $margin;
  }
</style>
