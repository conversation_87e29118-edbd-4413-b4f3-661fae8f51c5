<script setup lang="ts" generic="T">
  const emit = defineEmits(['update:modelValue'])
  const props = defineProps({
    modelValue: {
      type: Array as PropType<string[]>,
      default: () => [],
    },
    options: {
      type: Array as PropType<string[]>,
      default: () => [],
    },
  })

  const isOptionEmpty = computed(() => props.options.length === 0)

  function addHandler() {
    const newModelValue = [...props.modelValue]
    newModelValue.push('')
    emit('update:modelValue', newModelValue)
  }

  function clickHandlerDelete(index: number) {
    const newModelValue = [...props.modelValue]
    newModelValue.splice(index, 1)
    emit('update:modelValue', newModelValue)
  }

  function updateHandler(option: string, index: number) {
    const newModelValue = [...props.modelValue]
    newModelValue[index] = option
    emit('update:modelValue', newModelValue)
  }
</script>

<template>
  <FormBuilderSpecificOptionHeader label="Filter Groups" @add="addHandler" />
  <BaseWarning v-if="isOptionEmpty">No group found in SiteCollection</BaseWarning>
  <template v-for="(option, index) in modelValue">
    <div class="optionField">
      <FieldInputSelect v-model="option" :options="options" v-slot="{ option }" :no-margin="true">
        {{ option }}
      </FieldInputSelect>
      <BaseButtonDelete @click="clickHandlerDelete(index)" />
    </div>
  </template>
</template>

<style lang="scss" scoped>
  .field {
    width: 100%;
  }

  .optionField {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-direction: row;
    margin-bottom: $margin;
  }
</style>
