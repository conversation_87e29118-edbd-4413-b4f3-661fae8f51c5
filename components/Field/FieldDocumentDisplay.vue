<script setup lang="ts">
defineProps({
  label: { type: String, required: true },
})

const columns = [
  {
    title: 'File Name',
    slot: 'name'
  },
  {
    title: 'Document Type',
    key: 'DocumentType'
  },
  {
    title: 'Uploaded By',
    slot: 'uploaded'
  }
]
</script>

<template>
  <div class="fieldDocumentDisplay">
    <h2>{{ label }} (Uploaded Documents)</h2>
    <Table stripe :columns="columns">
      <template #name="{ row }">
        <a target="_blank">
          {{ row.File.Name }}
        </a>
      </template>
      <template #uploaded="{ row }">
        <strong>{{ row.Author.Title }}</strong>
      </template>
    </Table>
  </div>
</template>

<style lang="scss" scoped>
h2 {
  font-size: 1.1rem;
  font-weight: 400;
  color: #03787c;
}
</style>