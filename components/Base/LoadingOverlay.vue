<template>
  <div class="loading-overlay">
    <div class="spinner"></div>
  </div>
</template>

<style lang="scss" scoped>
/* Overlay styling */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.2);
  /* Semi-transparent background */
  display: flex;
  /* Center spinner */
  justify-content: center;
  align-items: center;
  z-index: 10;
  /* Initially transparent */
  transition: opacity 0.3s ease;
  /* Smooth fade-in/out */
}

/* Spinner styling */
.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid transparent;
  border-top: 4px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Spinner animation */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}
</style>