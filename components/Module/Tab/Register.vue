<script setup lang="ts">
  import { ModuleTabNames } from '~/constants/tabs'
  const registerStore = useConfigRegisterStore()
  const workflowStore = useWorkflowsStore()
  const { activeWorkflow } = storeToRefs(workflowStore)

  const router = useRouter()
  const route = useRoute()

  onMounted(() => {
    router.replace({
      query: {
        ...route.query,
        tab: ModuleTabNames.REGISTERS,
      },
    })
    registerStore.initialiseRegisterFor(RegisterFor.WORKFLOW)
  })

  watch(
    () => activeWorkflow.value,
    () => {
      registerStore.initialiseRegisterFor(RegisterFor.WORKFLOW)
    },
  )
</script>

<template>
  <ConfigRegisterPage />
</template>
