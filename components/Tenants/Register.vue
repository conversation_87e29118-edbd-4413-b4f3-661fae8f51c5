<script setup lang="ts">
  import { storeToRefs } from 'pinia'
  import type { Column, Tenant } from '@/types/data'

  const columns: Column[] = [
    { title: 'Name', key: 'name', sortType: 'asc' },
    { title: 'Tenant Url', key: 'tenant' },
    { title: 'Tenant ID', key: 'tenantId' },
    {
      title: 'Action',
      slot: 'action',
      width: 150,
      align: 'center',
    },
  ]

  const router = useRouter()
  const tenantsStore = useTenantsStore()
  const { tenants } = storeToRefs(tenantsStore)

  const showTenantConfig = ref(false)
  const tenant = ref()

  function closeHandler() {
    showTenantConfig.value = false
  }

  function clickHandlerTenant(name: string) {
    router.push({ path: `/tenants/${name}` })
  }

  function clickHandlerSettings(row: Tenant) {
    tenant.value = row
    showTenantConfig.value = true
  }
</script>

<template>
  <TenantsDrawerConfig :tenant="tenant" @close="closeHandler" v-if="tenant && showTenantConfig" />
  <BaseRegisterList :list="tenants" :columns="columns">
    <template #action="{ row }">
      <BaseButton @click="clickHandlerTenant(row.name)">
        <Icon type="md-briefcase" />
      </BaseButton>
      <BaseButton @click="clickHandlerSettings(row)">
        <Icon type="md-cog" />
      </BaseButton>
    </template>
  </BaseRegisterList>
</template>
