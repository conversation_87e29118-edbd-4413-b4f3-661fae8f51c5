<script setup lang="ts">
const { logout } = useAuth();

const user = useAuthUser()
const userAvatarText = computed(() =>
{
  if (!user.value?.email) return "U";
  return user.value.email.slice(0, 2).toUpperCase();
})

async function handleLogout()
{
  useUiStore().setLoading(true);
  await logout();
  useUiStore().setLoading(false);
  navigateTo("/");
}

</script>

<template>
  <Header class="layoutHeader">
    <div class="header__title">
      <div class="header__title__logo">
        <img src="/inqlogo.png" width="30px" />
      </div>
      <div class="header__title__text">
        Workbench
      </div>

    </div>
    <Dropdown trigger="click" placement="bottom-end">
      <Avatar class="avatar">
        <span class="avatar__text">
          {{ userAvatarText }}
        </span>
      </Avatar>
      <template #list>
        <DropdownMenu>
          <DropdownItem class="dropdown__item" name="signout" @click="handleLogout">
            <Icon type="ios-log-out" size="16" />
            <span>Log Out</span>
          </DropdownItem>
        </DropdownMenu>
      </template>
    </Dropdown>
  </Header>
</template>

<style lang="scss" scoped>
$header-height: 50px;

.layoutHeader {
  height: $header-height;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 $margin;
  background: white;
  border-bottom: 1px solid #cfcfcf;
}

.header__title {
  font-size: 1.25rem;
  font-weight: 400;
  display: flex;
  align-items: center;
  gap: $unit;

  &__logo {
    display: flex;
    align-items: center;
    border-right: 2px solid black;
    padding-right: $margin;
  }

  &__text {
    color: black;

  }
}

.dropdown__item {
  display: flex;
  align-items: center;
  gap: $unit;
}

.avatar {
  cursor: pointer;

  &__text {
    font-weight: 500;
    color: #686868;
  }
}
</style>