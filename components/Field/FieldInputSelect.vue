<script setup lang="ts" generic="T">
const props = defineProps({
  label: {
    type: String,
    default: ''
  },
  modelValue: {
    type: String
  },
  placeholder: {
    type: String,
    default: 'Please select an option'
  },
  options: {
    type: Array<T>,
    default: []
  },
  readonly: {
    type: Boolean,
    default: false
  },
  id: {
    type: String,
    default: ''
  },
  noMargin: {
    type: Boolean,
    default: false
  },
  valueTag: {
    type: String,
    default: ''
  },
  labelTag: {
    type: String,
    default: ''
  }
})

// Note: Don't provide valueTag and labelTag if using modelValue and @update:model-value on this component :)
const emit = defineEmits(['update:modelValue'])
const dropdownActive = ref(false)
const filterText = ref('')

function clickHandlerSelect(option: T) {
  if (props.valueTag !== '') {
    const prop = props.valueTag as keyof T
    emit('update:modelValue', option[prop])
  } else {
    emit('update:modelValue', option)
  }
}

function clickHandler(event: MouseEvent) {
  if (props.readonly) return
  const element = event.target as HTMLElement
  if (element.className === 'filterInput')
    dropdownActive.value = true
  else
    dropdownActive.value = !dropdownActive.value
  if (dropdownActive.value) {
    const inputElement = document.getElementById(props.id)
    if (!inputElement) return;
    setTimeout(() => { inputElement.focus() }, 100)
  }
}

function findModelValueLabel() {
  const valueTag = props.valueTag as keyof T
  const labelTag = props.labelTag as keyof T
  const option = props.options.find((option) => option[valueTag] === props.modelValue)
  return option ? option[labelTag] : props.modelValue
}

const isTagGiven = computed(() => ((props.labelTag !== '') && (props.valueTag !== '')))
</script>

<template>
  <FieldInputWrapper :noMargin="noMargin">
    <template #label>
      <div v-if="label">{{ label }}</div>
    </template>
    <div class="dropdown" :class="{ active: dropdownActive }" @click="clickHandler">
      <div class="select">
        <div v-show="dropdownActive">
          <input :id="id" class="filterInput" type="text" :placeholder="placeholder ? placeholder : 'Select'"
            v-model="filterText" />
        </div>
        <div class="value" v-show="!dropdownActive">
          <template v-if="isTagGiven">
            {{ findModelValueLabel() }}
          </template>
          <template v-else>
            {{ modelValue }}
          </template>
        </div>
        <BaseIconChevronDown class="icon" />
      </div>
      <div class="dropdown__menu" :style="{ display: dropdownActive ? 'block' : 'none' }">
        <template v-for="option in options">
          <div class="dropdown__menu__item" @click="clickHandlerSelect(option)">
            <slot :option="option" />
          </div>
        </template>
      </div>
    </div>
  </FieldInputWrapper>

</template>

<style lang="scss" scoped>
$field-height: 30px;
$border-radius: 4px;
$box-shadow: 0 0 4px rgb(204, 204, 204);
$primary-input-hover: #f2f2f2;
$primary-input-active: #e2e2e2;


.filterInput {
  border: none;
  width: 100%;

  &:focus {
    outline: none;
  }
}

.icon {
  height: 1rem;
  width: 1rem;
  transition: all .5s ease
}

.value {
  height: 24px;
}

/*Styling Selectbox*/
.dropdown {
  // width: 100%;
  min-width: 100%;
  height: $field-height;
  display: inline-block;
  background-color: $primary-white;
  border-radius: $border-radius;
  transition: all .5s ease;
  position: relative;
  border: 1px solid #dcdee2;

  .select {
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: $field-height;
    padding: 0 $unit;
  }

  &:hover {
    box-shadow: $box-shadow;
  }


  &.active {
    box-shadow: $box-shadow;

    >.select>.icon {
      transform: rotate(90deg);
      transition: all .5s ease
    }
  }
}

.dropdown__menu {
  position: absolute;
  background: $primary-white;
  width: 100%;
  margin-top: 1px;
  overflow: hidden;
  overflow-y: auto;
  z-index: 100;
  box-shadow: $box-shadow;
  max-height: 200px;

  &__item {
    padding: $sub-unit $unit;
    transition: all .2s ease-in-out;
    cursor: pointer;

    &:hover {
      background-color: $primary-input-hover;
    }

    &:active {
      background-color: $primary-input-active;
    }
  }
}
</style>
