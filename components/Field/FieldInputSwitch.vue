<script setup lang="ts">

defineProps({
  label: {
    type: String
  },
  modelValue: {
    type: Boolean
  }
})

const emit = defineEmits(['update:modelValue'])

function updateHandler(text: string) {
  emit('update:modelValue', text)
}

</script>

<template>
  <FieldInput :label="label">
    <Switch :modelValue="modelValue" @update:modelValue="updateHandler" size="large" true-color="#0f6cbd"
      false-color="#adadad">
      <template #open>
        Yes
      </template>
      <template #close>
        No
      </template>
    </Switch>
  </FieldInput>
</template>

<style lang="scss" scoped>
.field {
  width: 100%;
  margin-bottom: $margin;
}
</style>