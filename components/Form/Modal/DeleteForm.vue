<script setup lang="ts">
import type { Option } from '~/types'

const props = defineProps({
    modelValue: {
        type: Boolean,
        default: false
    }
})

const isVisible = ref(props.modelValue)
const isLoading = ref(false)
const indeterminate = ref(false)
const checkAll = ref(false)
const emit = defineEmits(['close', 'update:modelValue'])
const selectedFormConfigsToDelete = ref<string[]>([])
const formConfigOptions = ref<Option[]>([])
const formConfigStore = useFormConfigStore()
const { activeFormConfig } = storeToRefs(formConfigStore)
const formStore = useFormStore()
const { activeForm } = storeToRefs(formStore)
const uiStore = useUiStore()
const siteCollectionStore = useSiteCollectionsStore()
const { activeSiteCollection, tenant } = storeToRefs(siteCollectionStore)

function handlerCheckAll() {
    if (indeterminate.value) {
        checkAll.value = false
    } else {
        checkAll.value = !checkAll.value
    }
    indeterminate.value = false

    if (checkAll.value) {
        selectedFormConfigsToDelete.value = formConfigOptions.value.map((formConfig) => formConfig.value)
    } else {
        selectedFormConfigsToDelete.value = []
    }
}

function checkAllHandler() {
    if (selectedFormConfigsToDelete.value.length === formConfigOptions.value.length) {
        checkAll.value = true;
        indeterminate.value = false;
    } else if (selectedFormConfigsToDelete.value.length === 0) {
        checkAll.value = false;
        indeterminate.value = false;
    } else {
        checkAll.value = false;
        indeterminate.value = true;
    }
}

async function onOkHandler() {
    uiStore.setLoading(true, 'Deleting form configs...');
    emit('close')
    await formConfigStore.deleteMultipleFormConfig(selectedFormConfigsToDelete.value)
    uiStore.setLoadingMessage('Removing form from site collection...')
    await siteCollectionStore.removeFormFromSiteCollection(activeSiteCollection.value.key, activeForm.value.key)
    uiStore.setLoadingMessage('Deleting form...')
    await formStore.deleteForm(activeForm.value.key)
    // await formConfigStore.deleteFormConfig(activeFormConfig.value.key)
    uiStore.setLoading(false)
    navigateTo(`/tenants/${tenant.value}/${activeSiteCollection.value.key}`)
}
function onCancelHandler() {
    selectedFormConfigsToDelete.value = [];
    emit('close');
}

async function initialiseModal() {
    isLoading.value = true
    const formConfigs = formConfigStore.getFormConfigsWithFormKey(activeForm.value.key)
    formConfigOptions.value = formConfigs.map((formConfig) => ({
        label: formConfig.name,
        value: formConfig.key,
        description: formConfig.version
    }))
    isLoading.value = false
}

onMounted(async () => {
    initialiseModal()
})

watch(isVisible, (newValue) => {
    emit('update:modelValue', newValue);
});

watch(() => props.modelValue, (newValue) => {
    isVisible.value = newValue;
});

watch(activeForm, async () => {
    await initialiseModal();
})

</script>

<template>
    <Modal v-model="isVisible" @close="emit('close')" @on-ok="onOkHandler" @on-cancel="onCancelHandler">
        <template #title>
            {{ activeFormConfig.name }}
        </template>
        <BaseLoadingOverlay v-if="isLoading" />
        <div class="container">
            <div>Confirm Delete <b>{{ activeFormConfig.name }}</b> Form Config?</div>
            <div>Select workflow to delete:</div>
            <Checkbox :indeterminate="indeterminate" v-model="checkAll" @click.prevent="handlerCheckAll">
                Select All
            </Checkbox>
            <CheckboxGroup v-model="selectedFormConfigsToDelete" @on-change="checkAllHandler">
                <div style="display: grid;">
                    <Checkbox v-for="formConfig in formConfigOptions" :key="formConfig.value" :label="formConfig.value">
                        {{ formConfig.label }} - {{ formConfig.description }}
                    </Checkbox>
                </div>
            </CheckboxGroup>
            <BaseWarning>This action cannot be reverted</BaseWarning>
        </div>
    </Modal>
</template>

<style lang="scss" scoped>
.container {
    display: grid;
    row-gap: $margin;
}
</style>