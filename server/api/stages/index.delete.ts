// DELETE Stage
// body { stageKey: string }

import { useAuthenticatedFirebase } from '~/server/utils/firebaseDatabase'

export default defineEventHandler(async (event) => {
  const { stageKey } = await readBody(event)

  if (!stageKey) {
    return {
      ok: false,
      errorMessage: `stageKey is required`,
    }
  }

  try {
    const { firebaseRemove } = useAuthenticatedFirebase(event)
    await firebaseRemove(`/stages/${stageKey}`)
    return {
      ok: true,
      data: null,
      message: `Successfully delete stage with key ${stageKey}`,
    }
  } catch (error) {
    return {
      ok: false,
      errorMessage: error,
    }
  }
})
