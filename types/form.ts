export type FormFieldData =
  //Basic Components
  | TextField
  | TextInputField
  | NumberInputField
  | SelectField
  | DatepickerField
  | RadioButtonField
  | PeoplePickerField
  | TextAreaField
  | FileUploadField
  | ImageField
  | HiddenField
  | LinkField
  // Special Components
  | TableInputField
  | DocumentDisplayField
  | ActionField
  | TagsInputField
  | DocumentSelectField
  | DocumentListSelectField
  | NewDocumentTableField
  | SearchOrCreateField
  | AssessmentsField
  | CustomComponentField
  | BasicRiskField
  | FMEAField
  | RPNField
  | FinalRPNField
  | MultiFormField

export type TableInputFieldType =
  | TableInputField
  | ActionField
  | FMEAField
  | BasicRiskField
  | RPNField
  | FinalRPNField

export type FieldData = {
  key: string
  noValidation: boolean
  readOnly: boolean
  review: boolean
  reviewReadOnly: boolean
  label: string
  fieldName: string
  width: number
  conditions: Condition[]
  conditionsToHide: Condition[]
  // forms: Form[];
  multipleSubmit: boolean
  delimiter: string
  type: FormFieldType
}

export type ConditionOption = {
  fieldName: string
  stageName: string
  options: FieldOption[]
}

export type Condition = {
  fieldName: string
  value: string
}

export type ConditionToHide = {
  fieldName: string
  value: string
}

export type MultiFormForm = {
  formKey: string
  actionDescription: string
  groups: string
}

export enum FormFieldType {
  // Basic components
  TEXT = 'Text',
  TEXTINPUT = 'Input',
  NUMBERINPUT = 'InputNumber',
  SELECT = 'Select',
  DATEPICKER = 'DatePicker',
  RADIOBUTTON = 'Radio Buttons',
  PEOPLEPICKER = 'Peoplepicker',
  TEXTAREA = 'TextArea',
  FILEUPLOAD = 'FileUpload',
  IMAGE = 'Image',
  HIDDEN = 'HiddenField',
  LINK = 'Link',

  // Special components
  TABLEINPUT = 'TableInput',
  DOCUMENTDISPLAY = 'DocumentsDisplay',
  ACTION = 'Actions',
  TAGSINPUT = 'TagsInput',
  DOCUMENTSELECT = 'DocumentSelect',
  DOCUMENTLISTSELECT = 'DocumentListSelect',
  NEWDOCUMENTTABLE = 'NewDocumentTableInput',
  SEARCHORCREATE = 'SearchOrCreate',
  ASSESSMENTS = 'Assessments',
  CUSTOMCOMPONENT = 'CustomComponent',
  BASICRISK = 'BasicRiskTemplate',
  FMEA = 'FMEATemplate',
  RPN = 'RPNAssessment',
  FINALRPN = 'FinalRPNAssessment',
  MULTIFORM = 'MultiForm',
}

// ----------- Basic Components ------------

export interface TextField extends FieldData {
  type: FormFieldType.TEXT
  value: string
  fontSize: string
}

export interface TextInputField extends FieldData {
  type: FormFieldType.TEXTINPUT
  value: string
}

export interface NumberInputField extends FieldData {
  type: FormFieldType.NUMBERINPUT
  value: string
}

export interface SelectField extends FieldData {
  type: FormFieldType.SELECT
  value: string
  options: FieldOption[]
  multiple: boolean
  checkbox: boolean
  useDataFromList: boolean
  listname: string
  itemvalue: string
  itemtext: string
  addOtherValue: boolean
}

export interface DatepickerField extends FieldData {
  type: FormFieldType.DATEPICKER
  value: string
  disableAfterToday: boolean
  disableBeforeToday: boolean
  defaultValue: DatepickerDefaultValue
  daysAhead: number
}

export enum DatepickerDefaultValue {
  Blank = 'Blank',
  Today = 'Today',
  Custom = 'Custom',
}

export interface RadioButtonField extends FieldData {
  type: FormFieldType.RADIOBUTTON
  value: string
  index: number
  options: FieldOption[]
}

export interface PeoplePickerField extends FieldData {
  type: FormFieldType.PEOPLEPICKER
  value: string
  filterGroups: string[]
  defaultValue: PeoplePickerDefaultValue
}

export enum PeoplePickerDefaultValue {
  BLANK = 'Blank',
  CURRENTUSER = 'CurrentUser',
}

export interface TextAreaField extends FieldData {
  type: FormFieldType.TEXTAREA
  value: string
}

export interface FileUploadField extends FieldData {
  type: FormFieldType.FILEUPLOAD
  noMetadata: boolean
  singleFile: boolean
}

export interface ImageField extends FieldData {
  type: FormFieldType.IMAGE
  width: number
  url: string
}

export interface HiddenField extends FieldData {
  type: FormFieldType.HIDDEN
  width: number
  fieldName: string
}

export interface LinkField extends FieldData {
  type: FormFieldType.LINK
  width: number
  url: string
}

export interface NumberInputField extends FieldData {
  type: FormFieldType.NUMBERINPUT
  prefix: string
}

export type FieldOption = {
  name: string
}

export enum CustomOptionType {
  TEXT = 'Text',
  INPUT = 'Input',
  INPUTNUMBER = 'InputNumber',
  SELECT = 'Select',
  TEXTAREA = 'TextArea',
  DATEPICKER = 'DatePicker',
  PEOPLEPICKER = 'PeoplePicker',
  MODULES = 'Modules',
  MODULEITEMS = 'ModuleItems',
  MODULEITEMSFIELD = 'ModuleItemsField',
}

export type CustomOptionData =
  | TextCustomOption
  | InputCustomOption
  | InputNumberCustomOption
  | SelectCustomOption
  | TextAreaCustomOption
  | DatePickerCustomOption
  | PeoplePickerCustomOption
  | ModulesCustomOption
  | ModuleItemsCustomOption
  | ModuleItemsFieldCustomOption

export type CustomOption = {
  name: string
  fieldName: string
  type: CustomOptionType
  customColSpan: boolean
  colSpan: number
  hidden: boolean
  span: number
}

export interface TextCustomOption extends CustomOption {
  type: CustomOptionType.TEXT
}

export interface InputCustomOption extends CustomOption {
  type: CustomOptionType.INPUT
}

export interface InputNumberCustomOption extends CustomOption {
  type: CustomOptionType.INPUTNUMBER
}

export interface SelectCustomOption extends CustomOption {
  type: CustomOptionType.SELECT
  options: string
}

export interface TextAreaCustomOption extends CustomOption {
  type: CustomOptionType.TEXTAREA
}

export interface DatePickerCustomOption extends CustomOption {
  type: CustomOptionType.DATEPICKER
  disableAfterToday: boolean
  disableBeforeToday: boolean
  defaultValue: DatepickerDefaultValue
  daysAhead: number
}

export interface PeoplePickerCustomOption extends CustomOption {
  type: CustomOptionType.PEOPLEPICKER
  selectFromResponsibleGroup: boolean
}

export interface ModulesCustomOption extends CustomOption {
  type: CustomOptionType.MODULES
  options: string
}

export interface ModuleItemsCustomOption extends CustomOption {
  type: CustomOptionType.MODULEITEMS
  moduleFieldName: string
  itemFieldName: string
}

export interface ModuleItemsFieldCustomOption extends CustomOption {
  type: CustomOptionType.MODULEITEMSFIELD
  moduleFieldName: string
  relatedItemFieldName: string
}

// ----------- Special Components ------------

export interface TableInputField extends FieldData {
  type: FormFieldType.TABLEINPUT
  value: string[]
  defaultTableRows: number
  listname: string
  options: CustomOptionData[]
  removeAddButton: boolean
  addButton: boolean
  deleteButton: boolean
  disableButton: boolean
  rejectButton: boolean
  autoSerialize: boolean
}

export interface DocumentDisplayField extends FieldData {
  type: FormFieldType.DOCUMENTDISPLAY
  value: string[]
  options: FieldOption[]
}

export type DocumentDisplayOption = {
  id: number
  name: string
  fieldName: string
  type: string
}

export interface ActionField extends FieldData {
  type: FormFieldType.ACTION
  value: string[]
  defaultTableRows: number
  options: CustomOptionData[]
  actionType: string
  actionDescription: string
  actionStage: string
  removeAddButton: boolean
  addButton: boolean
  deleteButton: boolean
  disableButton: boolean
  rejectButton: boolean
  autoSerialize: boolean
}

export interface TagsInputField extends FieldData {
  type: FormFieldType.TAGSINPUT
  value: string
}

export interface DocumentSelectField extends FieldData {
  type: FormFieldType.DOCUMENTSELECT
  value: string[]
  options: FieldOption[]
  folders: string
  useCustomChangeType: boolean
}

export type DocumentSelectOption = {
  id: number
  name: string
  fieldName: string
  type: string
}

export interface DocumentListSelectField extends FieldData {
  type: FormFieldType.DOCUMENTLISTSELECT
  value: string[]
  options: FieldOption[]
  folders: string
}

export type DocumentListSelectOption = {
  id: number
  name: string
  fieldName: string
  type: string
}

export interface NewDocumentTableField extends FieldData {
  type: FormFieldType.NEWDOCUMENTTABLE
  value: string[]
  listname: string
  options: FieldOption[]
  departmentDocumentOwner: boolean
}

export type NewDocumentTableOption = {
  id: number
  name: string
  fieldName: string
  type: string
}

export interface SearchOrCreateField extends FieldData {
  type: FormFieldType.SEARCHORCREATE
  value: string
  fieldName: string
  listname: string
  formConfigKey: string
  customlist: string
  itemText: string
  itemValue: string
  createOnly: boolean
  disableAddButton: boolean
}

export interface AssessmentsField extends FieldData {
  type: FormFieldType.ASSESSMENTS
  value: string[]
  listname: string
  assessmentListname: string
}

export interface CustomComponentField extends FieldData {
  type: FormFieldType.CUSTOMCOMPONENT
  value: string[]
  listname: string
  fieldName: string
}

export type TableInputVariantOption = {
  name: string
  customColSpan: boolean
  colSpan: number
  hidden?: boolean
}

export interface BasicRiskField extends FieldData {
  type: FormFieldType.BASICRISK
  value: string[]
  fieldName: string
  listname: string
  options: CustomOptionData[]
  autoSerialize?: boolean
}

export interface FMEAField extends FieldData {
  type: FormFieldType.FMEA
  value: string[]
  fieldName: string
  listname: string
  options: CustomOptionData[]
  autoSerialize?: boolean
}

export interface RPNField extends FieldData {
  type: FormFieldType.RPN
  value: string[]
  listname: string
  fieldName: string
  options: CustomOptionData[]
  autoSerialize?: boolean
}

export interface FinalRPNField extends FieldData {
  type: FormFieldType.FINALRPN
  value: string[]
  listname: string
  fieldName: string
  options: CustomOptionData[]
  autoSerialize?: boolean
}

export interface MultiFormField extends FieldData {
  type: FormFieldType.MULTIFORM
  fieldName: string
  forms: MultiFormForm[]
}

export type TableFlowCondition = {
  fieldName: string
  value: string
  goToStage: string
}

export type TableFlowConditionOption = {
  fieldName: string
  options: FieldOption[]
}
