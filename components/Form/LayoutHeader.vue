<script setup lang="ts">
const emit = defineEmits(["click"])
const formConfigStore = useFormConfigStore()
const { activeFormConfig } = storeToRefs(formConfigStore)
const indicatorStore = useIndicatorStore()
const isFormConfigHasNoInUse = computed(() => indicatorStore.getNoInUseFormConfigIndicator())

function handleClick()
{
  emit("click")
}

</script>

<template>
  <div class="layoutHeaderModule">
    <div>
      <AppBreadCrumbsText />
    </div>
    <div class="versionButton">
      <Tooltip content="No Form Config is in use" transfer>
        <div v-if="isFormConfigHasNoInUse" class="warningIndicator">
          <Icon type="md-help" />
        </div>
      </Tooltip>
      <h6 @click="handleClick">
        {{ activeFormConfig.version }}
      </h6>
    </div>

  </div>
</template>

<style lang="scss" scoped>
h6 {
  color: $primary-lightblue;

  &:hover {
    cursor: pointer;
    color: $primary-blue ;
  }
}

.layoutHeaderModule {
  display: grid;
  grid-auto-flow: column;
  width: auto;
  justify-content: space-between;
  align-items: baseline;
  margin-right: $margin;
}

.versionButton {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  gap: $padding;
}

.warningIndicator {
  color: white;
  background-color: #f7630c;
  border-radius: 100%;
  width: 24px;
  height: 24px;
  align-items: center;
  display: flex;
  justify-content: center;
}
</style>