import { vi } from 'vitest'
import { config } from '@vue/test-utils'

// Mock Nuxt composables
vi.mock('#app', () => ({
  useNuxtApp: () => ({
    $auth: {},
    $db: {},
  }),
  useRuntimeConfig: () => ({
    public: {
      FIREBASE_API_KEY: 'test-api-key',
      FIREBASE_AUTH_DOMAIN: 'test.firebaseapp.com',
      FIREBASE_PROJECT_ID: 'test-project',
      FIREBASE_STORAGE_BUCKET: 'test.appspot.com',
      FIREBASE_MESSAGING_SENDER_ID: '123456789',
      FIREBASE_APP_ID: '1:123456789:web:abcdef',
      FIREBASE_MEASUREMENT_ID: 'G-ABCDEF',
      FIREBASE_DATABASE_URL: 'https://test-project.firebaseio.com',
    },
  }),
  navigateTo: vi.fn(),
  useRoute: () => ({
    params: {},
    query: {},
    path: '/',
  }),
  useRouter: () => ({
    push: vi.fn(),
    replace: vi.fn(),
    back: vi.fn(),
  }),
  useCookie: vi.fn(() => ref(null)),
  useRequestHeaders: vi.fn(() => ({})),
  useHead: vi.fn(),
  definePageMeta: vi.fn(),
}))

// Mock Pinia stores
vi.mock('pinia', () => ({
  defineStore: vi.fn(() => () => ({})),
  storeToRefs: vi.fn(() => ({})),
}))

// Mock Firebase
vi.mock('firebase/app', () => ({
  initializeApp: vi.fn(),
  getApps: vi.fn(() => []),
  getApp: vi.fn(),
}))

vi.mock('firebase/auth', () => ({
  getAuth: vi.fn(),
  signInWithCustomToken: vi.fn(),
  onAuthStateChanged: vi.fn(),
}))

vi.mock('firebase/database', () => ({
  getDatabase: vi.fn(),
  ref: vi.fn(),
  onValue: vi.fn(),
  off: vi.fn(),
}))

// Mock View UI Plus components
const mockComponent = {
  template: '<div><slot /></div>',
}

config.global.components = {
  Layout: mockComponent,
  Header: mockComponent,
  Content: mockComponent,
  Sider: mockComponent,
  Menu: mockComponent,
  MenuItem: mockComponent,
  Submenu: mockComponent,
  Button: mockComponent,
  Input: mockComponent,
  Select: mockComponent,
  Option: mockComponent,
  Table: mockComponent,
  Modal: mockComponent,
  Drawer: mockComponent,
  Tabs: mockComponent,
  TabPane: mockComponent,
  Form: mockComponent,
  FormItem: mockComponent,
  Icon: mockComponent,
  Avatar: mockComponent,
  Dropdown: mockComponent,
  DropdownMenu: mockComponent,
  DropdownItem: mockComponent,
  Spin: mockComponent,
  Alert: mockComponent,
  Card: mockComponent,
  Row: mockComponent,
  Col: mockComponent,
  Divider: mockComponent,
  Badge: mockComponent,
  Tag: mockComponent,
  Tooltip: mockComponent,
  Popover: mockComponent,
  Progress: mockComponent,
  Steps: mockComponent,
  Step: mockComponent,
  Breadcrumb: mockComponent,
  BreadcrumbItem: mockComponent,
  DatePicker: mockComponent,
  TimePicker: mockComponent,
  Upload: mockComponent,
  Switch: mockComponent,
  Checkbox: mockComponent,
  CheckboxGroup: mockComponent,
  Radio: mockComponent,
  RadioGroup: mockComponent,
  InputNumber: mockComponent,
  Rate: mockComponent,
  Slider: mockComponent,
  Tree: mockComponent,
  Transfer: mockComponent,
  ColorPicker: mockComponent,
  Cascader: mockComponent,
  AutoComplete: mockComponent,
}

// Mock global properties
config.global.mocks = {
  $t: (key: string) => key,
  $route: {
    params: {},
    query: {},
    path: '/',
  },
  $router: {
    push: vi.fn(),
    replace: vi.fn(),
    back: vi.fn(),
  },
}

// Setup global test environment
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}))

global.IntersectionObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}))

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(),
    removeListener: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
})

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}
vi.stubGlobal('localStorage', localStorageMock)

// Mock sessionStorage
const sessionStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}
vi.stubGlobal('sessionStorage', sessionStorageMock)
