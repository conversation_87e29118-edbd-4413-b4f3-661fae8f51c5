<script setup lang="ts">
import { FormTabNames } from '~/constants/tabs';
import type { FormConfigSetting } from '~/types/formConfig';

const formConfigStore = useFormConfigStore();
const { activeFormConfig } = storeToRefs(formConfigStore);
const formStore = useFormStore();
const { activeForm } = storeToRefs(formStore);
const router = useRouter();
const route = useRoute();

const formConfigSettings = ref<FormConfigSetting>({} as FormConfigSetting);

const { resetHandler, submitHandler } = useSettingForm<FormConfigSetting>({
    form: formConfigSettings,
    activeData: activeFormConfig,
    updateHandler: async () =>
    {
        await formStore.updateForm(activeForm.value.key, { name: formConfigSettings.value.name });
        await formConfigStore.updateFormConfig(activeFormConfig.value.key, formConfigSettings.value);
    }
});

const showDeleteModal = ref(false);

function clickHandlerDelete()
{
    showDeleteModal.value = true;
}

function closeHandlerDelete()
{
    showDeleteModal.value = false;
}

onMounted(() =>
{
    router.replace({
        query: {
            ...route.query,
            tab: FormTabNames.SETTINGS
        }
    })
})
</script>

<template>
    <FormModalDeleteForm v-model="showDeleteModal" @close="closeHandlerDelete" />
    <Card dis-hover>
        <template #title>
            <div class="title">
                <b>Form Settings</b>
                <BaseButtonDeleteText @click="clickHandlerDelete" text="Delete Form" />
            </div>
        </template>
        <Form label-position="top">
            <FieldInputText v-model="formConfigSettings.name" label="Name" />
            <FieldInputText v-model="formConfigSettings.description" label="Description" />
            <FieldInputIcon v-model="formConfigSettings.icon" label="Icon" />
            <FieldInputText v-model="formConfigSettings.type" label="Type" />
            <FieldInputText v-model="formConfigSettings.workspaceTitle" label="Workspace Title" />
            <FieldInputIcon v-model="formConfigSettings.workspaceIcon" label="Workspace Icon" />
            <FieldInputText v-model="formConfigSettings.formRegister" label="Form Register Name" />
            <FieldInputSwitch v-model="formConfigSettings.showInMenu" label="Show in Menu" />
            <BaseButtonSaveReset @save="submitHandler" @reset="resetHandler" />
        </Form>
    </Card>
</template>

<style lang="scss" scoped>
.title {
    display: flex;
    justify-content: space-between;
    align-self: center;
}
</style>`;