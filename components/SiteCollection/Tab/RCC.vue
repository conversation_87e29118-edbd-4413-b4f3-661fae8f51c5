<script setup lang="ts">
  import { SiteCollectionTabNames } from '~/constants/tabs'
  import { RegisterFor } from '~/stores/configRegister'
  const registerStore = useConfigRegisterStore()
  const siteCollectionStore = useSiteCollectionsStore()
  const { activeSiteCollection } = storeToRefs(siteCollectionStore)

  const router = useRouter()
  const route = useRoute()
  onMounted(() => {
    router.replace({
      query: {
        ...route.query,
        tab: SiteCollectionTabNames.RCC,
      },
    })
    registerStore.initialiseRegisterFor(RegisterFor.SITERCC)
  })

  watch(
    () => activeSiteCollection.value,
    () => {
      registerStore.initialiseRegisterFor(RegisterFor.SITERCC)
    },
  )
</script>

<template>
  <ConfigRegisterPage />
</template>
