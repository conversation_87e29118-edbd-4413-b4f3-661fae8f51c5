<script setup lang="ts">
import type { SiteGroup } from '~/types/sharepointAPI';
import type { SiteCollectionGroups } from '~/types/siteCollection';

const siteCollectionsStore = useSiteCollectionsStore();
const { activeSiteCollection, tenant, siteUrl } = storeToRefs(siteCollectionsStore);
const sharepointAPIStore = useSharepointAPIStore();
const { groupsExisting, activeGroup, storeLoading } = storeToRefs(sharepointAPIStore);

const siteGroups = ref<SiteCollectionGroups>({} as SiteCollectionGroups)
const showAddForm = ref(false)
const newGroup = ref('')
const isLoading = ref(false)

onMounted(async () => {
  await initialiseGroup()
})

watch(activeSiteCollection.value, async () => {
  await initialiseGroup()
});

async function initialiseGroup() {
  isLoading.value = true
  if (activeSiteCollection.value.groups) {
    siteGroups.value = useCloneDeep(activeSiteCollection.value)
    for (const group of siteGroups.value.groups) {
      if (checkGroupExist(group)) {
        console.log("Group is active");
        await setActiveGroup(group)
        break
      }
    }
  } else {
    siteGroups.value.groups = []
  }
  isLoading.value = false

}

function isGroupActive(groupName: string) {
  if (!activeGroup.value) return false
  return activeGroup.value.Title === groupName;
}

function clickHandlerAdd() {
  showAddForm.value = true
}
function clickHandlerCancel() {
  showAddForm.value = false
}

async function clickHandlerAddToSharepoint(groupName: string) {
  isLoading.value = true
  await sharepointAPIStore.addSiteGroup(groupName, tenant.value, siteUrl.value)
  isLoading.value = false
  console.log("Group added successfully");
}

async function clickHandlerSubmit() {
  if (!newGroup.value) return
  isLoading.value = true
  siteGroups.value.groups.push(newGroup.value)
  await siteCollectionsStore.updateSiteCollection(activeSiteCollection.value.key, siteGroups.value)
  showAddForm.value = false
  newGroup.value = ''
  isLoading.value = false
}

function checkGroupExist(groupName: string) {
  if (!groupsExisting.value) return false
  return groupsExisting.value.find((group: SiteGroup) => group.Title === groupName) !== undefined
}

async function clickHandlerGroup(groupName: string) {
  await setActiveGroup(groupName)
}

async function setActiveGroup(groupName: string) {
  const selectedGroup = groupsExisting.value.find((group: SiteGroup) => group.Title === groupName)
  if (!selectedGroup) {
    isLoading.value = false
    return
  }
  await sharepointAPIStore.setActiveGroup(selectedGroup)
}

async function clickHandlerDelete(index: number) {
  isLoading.value = true
  const group = siteGroups.value.groups.splice(index, 1)
  await siteCollectionsStore.updateSiteCollection(activeSiteCollection.value.key, siteGroups.value)
  if (activeGroup.value) {
    if (group[0] === activeGroup.value.Title) {
      await sharepointAPIStore.clearActiveGroup()
    }
  }
  isLoading.value = false
}

</script>

<template>
  <BaseCardPanel>
    <BaseLoadingOverlay v-if="isLoading" />
    <template #title>
      Groups
    </template>
    <template v-for="group, index in siteGroups.groups">
      <Tooltip :content="group" :delay="1000" transfer>
        <div class="group__container">
          <div class="groups" @click="clickHandlerGroup(group)">
            <div class="group"
              :class="{ 'active': isGroupActive(group), 'disabled': (!checkGroupExist(group) || storeLoading) }">
              <span class="group__name">{{ group }}</span>
              <div class="icon icon--check" v-if="checkGroupExist(group)">
                <BaseIconCheck />
              </div>
              <div class="icon icon--add" v-else @click="clickHandlerAddToSharepoint(group)">
                <BaseIconAdd />
              </div>
            </div>
          </div>
          <BaseButtonDelete @click="clickHandlerDelete(index)" />
        </div>
      </Tooltip>
    </template>
    <template v-if="showAddForm">
      <Form label-position="left" :label-width="120" inline onsubmit="return false">
        <FieldInputText label="Group Name" v-model="newGroup"></FieldInputText>
      </Form>
      <BaseButtonSaveCancel @cancel="clickHandlerCancel" @save="clickHandlerSubmit" />
    </template>
    <template v-else>
      <BaseButtonAdd text="Group" @click="clickHandlerAdd" />
    </template>
  </BaseCardPanel>
</template>

<style lang="scss" scoped>
.box {
  border: 1px solid #ccc;
  padding: $unit;
  font-size: 0.9rem;
}

.active {
  background-color: #f0f0f0;
}

.disabled {
  cursor: not-allowed;

  :hover {
    cursor: not-allowed !important;
  }
}

.group__container {
  display: grid;
  grid-template-columns: 1fr 50px;
  align-items: center;
  column-gap: $unit;
}

.groups {
  display: grid;
  row-gap: $unit;

  :hover {
    cursor: pointer
  }

  & .disabled {
    cursor: not-allowed;
  }

  .group {
    font-size: 0.8rem;
    border: 1px solid #ccc;
    padding: $unit;
    display: flex;
    justify-content: space-between;
  }

}

.group__name {
  width: 148px;
  overflow: hidden;
  text-overflow: ellipsis;
  text-wrap: nowrap;
}



.icon {
  width: 1rem;
  display: flex;
  align-items: center;

  &--check {
    color: green;
  }

  &--add {
    color: red;
  }
}
</style>