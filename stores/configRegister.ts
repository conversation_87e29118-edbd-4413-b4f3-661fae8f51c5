import { CONFIG_REGISTER_NEW_REGISTER } from '~/constants/default-values'
import type { FieldNameOption, Option } from '~/types'
import type { FormConfig } from '~/types/formConfig'
import {
  RegisterFilterOperator,
  RegisterFilterType,
  type Register,
  type RegisterSetting,
} from '~/types/register'
import type { SiteCollectionDCC, SiteCollectionRCC } from '~/types/siteCollection'
import type { Workflow } from '~/types/workflow'

export enum RegisterFor {
  SITEDCC = 'SiteDCC',
  SITERCC = 'SiteRCC',
  WORKFLOW = 'Workflow',
  FORM = 'Form',
}

export const useConfigRegisterStore = defineStore('register', () => {
  const siteCollectionStore = useSiteCollectionsStore()
  const { activeSiteCollection } = storeToRefs(siteCollectionStore)

  const workflowStore = useWorkflowsStore()
  const { activeWorkflow } = storeToRefs(workflowStore)

  const formConfigStore = useFormConfigStore()
  const { activeFormConfig } = storeToRefs(formConfigStore)

  const sharepointAPIStore = useSharepointAPIStore()
  const { listsExisting } = storeToRefs(sharepointAPIStore)

  const fieldStore = useFieldStore()

  // ---------- State ----------
  const activeRegisterSetting = ref({} as RegisterSetting)
  const activeRegister = ref({} as Register)
  const activeRegisterIndex = ref<number>(0)
  const registerFor = ref<RegisterFor>()

  // ---------- Initialise ----------

  const clearRegisterState = () => {
    activeRegisterSetting.value = {} as RegisterSetting
    activeRegister.value = {} as Register
    activeRegisterIndex.value = 0
    registerFor.value = undefined
  }

  const initialiseFirstRegister = () => {
    if (!activeRegisterSetting.value.registers) activeRegisterSetting.value.registers = []

    if (activeRegisterSetting.value.registers.length > 0)
      setActiveRegister(activeRegisterSetting.value.registers[0], 0)
  }

  const initialiseRegisterFor = (type: RegisterFor) => {
    clearRegisterState()
    registerFor.value = type

    switch (type) {
      case RegisterFor.SITEDCC:
        activeRegisterSetting.value = useCloneDeep(activeSiteCollection.value)
        break
      case RegisterFor.SITERCC:
        activeRegisterSetting.value = useCloneDeep({
          registers: activeSiteCollection.value.Rregisters,
        })
        break
      case RegisterFor.WORKFLOW:
        activeRegisterSetting.value = useCloneDeep(activeWorkflow.value)
        break
      case RegisterFor.FORM:
        activeRegisterSetting.value = useCloneDeep(activeFormConfig.value)
        break
    }
    initialiseFirstRegister()
  }

  // ---------- Register Management ----------

  const setActiveRegister = (register: Register, index: number) => {
    activeRegister.value = register
    activeRegisterIndex.value = index
  }

  function resetActiveRegister() {
    activeRegister.value = {} as Register
    activeRegisterIndex.value = 0
  }

  const saveRegisterSetting = async (data: RegisterSetting) => {
    switch (registerFor.value) {
      case RegisterFor.SITEDCC:
        await siteCollectionStore.updateSiteCollection(activeSiteCollection.value.key, {
          registers: data.registers,
        } as SiteCollectionDCC)
        break
      case RegisterFor.SITERCC:
        await siteCollectionStore.updateSiteCollection(activeSiteCollection.value.key, {
          Rregisters: data.registers,
        } as SiteCollectionRCC)
        break
      case RegisterFor.FORM:
        await formConfigStore.updateFormConfig(activeFormConfig.value.key, data)
        break
      case RegisterFor.WORKFLOW:
        await workflowStore.updateWorkflow(activeWorkflow.value.key, data)
        break
    }
  }

  const addNewRegister = async () => {
    const data = useCloneDeep(activeRegisterSetting.value)
    data.registers.push(CONFIG_REGISTER_NEW_REGISTER)
    await saveRegisterSetting(data)
  }

  const duplicateRegister = async () => {
    const data = useCloneDeep(activeRegisterSetting.value)
    const copy = useCloneDeep(activeRegister.value)
    copy.name += ' (Copy)'
    data.registers.push(copy)
    await saveRegisterSetting(data)
  }

  const deleteRegister = async () => {
    const data = useCloneDeep(activeRegisterSetting.value)
    data.registers.splice(activeRegisterIndex.value, 1)
    await saveRegisterSetting(data)
    resetActiveRegister()
    initialiseFirstRegister()
  }

  const updateRegister = async (register: Register) => {
    const data = useCloneDeep(activeRegisterSetting.value)
    data.registers[activeRegisterIndex.value] = useCloneDeep(register)
    await saveRegisterSetting(data)
  }

  // ---------- Options ----------

  function getListOptions() {
    if (registerFor.value === RegisterFor.WORKFLOW) {
      return registerGetListOption(activeWorkflow.value)
    } else if (registerFor.value === RegisterFor.FORM) {
      return registerGetListOption(activeFormConfig.value)
    } else if (registerFor.value === RegisterFor.SITEDCC) {
      return fieldStore.getDCCRCCListOptions()
    } else if (registerFor.value === RegisterFor.SITERCC) {
      return fieldStore.getDCCRCCListOptions()
    }
    return [] as Option[]
  }

  function getSiteCollectionListOptions() {
    return listsExisting.value.map((list) => ({
      value: list.Title,
      label: list.Title,
    }))
  }

  function registerGetListOption(data: Workflow | FormConfig) {
    const listOptions = [] as Option[]

    if (!data.lists) return listOptions
    data.lists.forEach((list: { DisplayName: string }) => {
      if (sharepointAPIStore.checkListExist(list.DisplayName)) {
        listOptions.push({
          value: list.DisplayName,
          label: list.DisplayName,
        } as Option)
      }
    })
    return listOptions
  }

  function getLinkedListOptions() {
    if (registerFor.value === RegisterFor.WORKFLOW) {
      return registerGetLinkedListOptions(activeWorkflow.value)
    } else if (registerFor.value === RegisterFor.FORM) {
      return registerGetLinkedListOptions(activeFormConfig.value)
    } else if (registerFor.value === RegisterFor.SITEDCC) {
      return getSiteCollectionListOptions()
    } else if (registerFor.value === RegisterFor.SITERCC) {
      return getSiteCollectionListOptions()
    }
    return getListOptions()
  }

  function registerGetLinkedListOptions(data: Workflow | FormConfig) {
    const linkedListOptions = getListOptions()
    if (!data.customlists) return linkedListOptions
    data.customlists.forEach((list: { fieldName: string }) => {
      linkedListOptions.push({
        value: list.fieldName,
        label: list.fieldName,
      } as Option)
    })
    return linkedListOptions
  }

  const getColumnToShowOption = async (
    listDisplayName: string,
    linkedListName?: string,
  ): Promise<FieldNameOption[]> => {
    const fields = await fieldStore.getFieldinSharepointList(listDisplayName)
    const options =
      fields?.map((field) => ({
        label: field.InternalName,
        value: field.InternalName,
        description: listDisplayName,
      })) || []

    if (linkedListName && sharepointAPIStore.checkListExist(linkedListName)) {
      const linkedFields = await fieldStore.getFieldinSharepointList(linkedListName)
      options.push(
        ...(linkedFields?.map((field) => ({
          label: field.InternalName,
          value: field.InternalName,
          description: linkedListName,
        })) || []),
      )
    }

    return options
  }

  const getRegisterFilterTypeOptions = () =>
    Object.values(RegisterFilterType).map((value) => ({ label: value, value }))

  const getRegisterFilterOperatorOptions = () =>
    Object.values(RegisterFilterOperator).map((value) => ({ label: value, value }))

  return {
    initialiseRegisterFor,
    activeRegisterSetting,
    activeRegisterIndex,
    activeRegister,
    setActiveRegister,
    resetActiveRegister,
    deleteRegister,
    updateRegister,
    addNewRegister,
    duplicateRegister,
    getListOptions,
    getLinkedListOptions,
    getColumnToShowOption,
    getRegisterFilterOperatorOptions,
    getRegisterFilterTypeOptions,
    registerFor,
  }
})
