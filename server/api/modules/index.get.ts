// GET ALL Modules
import { useAuthenticatedFirebase } from '~/server/utils/firebaseDatabase'

export default defineEventHandler(async (event) => {
  try {
    const { firebaseFetch } = useAuthenticatedFirebase(event)
    const modules = await firebaseFetch('/modules')
    return {
      ok: true,
      message: 'Modules fetched successfully',
      data: modules,
    }
  } catch (error) {
    return {
      ok: false,
      errorMessage: error,
    }
  }
})
