<script setup lang="ts" generic="T">
defineProps({
    label: {
        type: String
    },
    modelValue: {
        type: String
    },
    type: {
        type: String,
        default: 'button'
    },
    options: {
        type: Array<T>,
        default: []
    }
})
const emit = defineEmits(['update:modelValue'])

function updateHandler(text: string) {
    emit('update:modelValue', text)
}
</script>

<template>
    <FieldInput :label="label">
        <RadioGroup :modelValue="modelValue" @update:modelValue="updateHandler" :type="type">
            <slot />
        </RadioGroup>
    </FieldInput>
</template>