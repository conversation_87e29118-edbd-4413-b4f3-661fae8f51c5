<script setup lang="ts">
  defineProps({
    modelValue: {
      type: Number,
    },
  })
  const emit = defineEmits(['update:modelValue'])

  function inputHandler(event: Event) {
    const target = event.target as HTMLInputElement
    emit('update:modelValue', Number(target.value))
  }
</script>

<template>
  <fluent-number-field type="number" :value="modelValue" @input="inputHandler" class="field" />
</template>

<style lang="scss" scoped>
  .field {
    width: 100%;
  }
</style>
