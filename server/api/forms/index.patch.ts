import { Form } from '~/types/data'
import { useAuthenticatedFirebase } from '~/server/utils/firebaseDatabase'

export default defineEventHandler(async (event) => {
  const { formKey, formData } = await readBody(event)

  try {
    const { firebaseUpdate } = useAuthenticatedFirebase(event)
    await firebaseUpdate(`forms/${formKey}`, formData)
    return {
      ok: true,
      message: `Form ${formKey} updated successfully`,
    }
  } catch (error) {
    return {
      ok: false,
      errorMessage: `Error updating form ${formKey}: ${error}`,
    }
  }
})
