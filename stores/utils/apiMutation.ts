import { Message } from 'view-ui-plus'
import type { ServerResponse } from '~/types/api'

export const useApiMutation = async <T = unknown>(
  url: string,
  options: {
    method?: 'POST' | 'PATCH' | 'DELETE'
    body?: any
    params?: Record<string, any>
    onSuccessMessage?: string
    refetch?: () => Promise<void>
  } = {},
): Promise<T | undefined> => {
  const { method = 'POST', body, params, onSuccessMessage, refetch } = options

  const response = (await $fetch<T>(url, { method, body, params })) as ServerResponse

  if (!response.ok) {
    Message.error(response.errorMessage)
    return
  }

  if (onSuccessMessage || response.message) {
    Message.success(onSuccessMessage || response.message)
  }

  if (refetch) await refetch()

  return response.data
}
