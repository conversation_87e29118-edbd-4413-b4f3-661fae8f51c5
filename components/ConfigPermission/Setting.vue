<script setup lang="ts">
  import { PermissionType, type Permission } from '~/types/permission'

  const props = defineProps({
    modelValue: {
      type: Object as PropType<Permission[]>,
      default: () => [],
    },
  })

  const emit = defineEmits(['update:modelValue'])

  const permissionOption = computed(() => {
    return Object.values(PermissionType).map((permission) => {
      return {
        label: permission,
        value: permission,
      }
    })
  })

  const { groupOptions } = useSelectOptionsUtils()

  function clickHandlerDelete(index: number) {
    props.modelValue.splice(index, 1)
  }
</script>

<template>
  <Form>
    <template v-for="(p, index) in modelValue">
      <BaseLayoutRowForm grid-layout="1fr 1fr 50px">
        <Select v-model="modelValue[index].name" transfer>
          <Option v-for="permission in permissionOption" :value="permission.value"
            >{{ permission.label }}
          </Option>
        </Select>
        <Select multiple v-model="modelValue[index].groups" transfer>
          <Option v-for="group in groupOptions" :value="group.value">{{ group.label }}</Option>
        </Select>
        <BaseButtonDelete @click="clickHandlerDelete(index)" />
      </BaseLayoutRowForm>
    </template>
  </Form>
</template>

<style lang="scss" scoped>
  .addButton {
    margin-top: $margin;
  }

  .formWrapper {
    margin-top: $margin;
    display: grid;
    grid-template-columns: 1fr 1fr 50px;
    column-gap: $margin;
  }
</style>
