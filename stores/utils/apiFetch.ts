import { Message, Notice } from 'view-ui-plus'
import type { ServerResponse } from '~/types/api'

interface ApiFetchOptions {
  params?: Record<string, any>
  errorNotice?: { title: string } // optional custom error notice
}

export const useApiFetch = async <T>(
  url: string,
  onSuccess: (data: T) => void,
  params?: Record<string, any>,
  options?: ApiFetchOptions,
): Promise<void> => {
  const response: ServerResponse = await $fetch(url, { params })
  console.log(response)
  if (!response.ok) {
    if (options?.errorNotice) {
      Notice.error({
        title: options.errorNotice.title,
        desc: response.errorMessage,
        duration: 10,
      })
    } else {
      Message.error(response.errorMessage)
    }
    return
  }

  onSuccess(response.data)
}
