<script setup lang="ts">
const { finalRPNField } = useFormBuilderFieldSetting()
</script>

<template>
    <FormBuilderFieldSettingLabel>Final RPN Assessment Settings</FormBuilderFieldSettingLabel>
    <template v-if="finalRPNField">
        <Form label-position="top">
            <FormBuilderSpecificFinalRPNAssessmentSetting v-model="finalRPNField.options" />
        </Form>
    </template>
</template>