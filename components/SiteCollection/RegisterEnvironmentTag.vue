<script setup lang="ts">
  import { SiteCollectionEnvironment } from '~/types/siteCollection'

  const props = defineProps({
    environment: {
      type: String,
      required: false,
      default: SiteCollectionEnvironment.Dev,
    },
  })

  const tagColor = computed(() => {
    switch (props.environment) {
      case SiteCollectionEnvironment.Dev:
        return '#0f6cbd'
      case SiteCollectionEnvironment.Test:
        return '#eaa300'
      case SiteCollectionEnvironment.Live:
        return '#13a10e'
      case SiteCollectionEnvironment.Staging:
        return 'orange'
      default:
        return 'primary'
    }
  })

  const tagText = computed(() => props.environment)
</script>

<template>
  <Tag :color="tagColor">{{ tagText }}</Tag>
</template>
