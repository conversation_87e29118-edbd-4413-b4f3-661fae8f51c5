// DELETE multiple workflows
// body { workflowKeys: string[] }

export default defineEventHandler(async (event) => {
  const { db } = useFirebaseAdmin()
  const { formConfigKeys } = await readBody(event)
  if (!formConfigKeys) {
    return {
      ok: false,
      errorMessage: `formConfig keys are required`,
    }
  }

  try {
    const updates: { [key: string]: null } = {}

    formConfigKeys.forEach(async (formConfigKey: string) => {
      updates[`formConfigs/${formConfigKey}`] = null
    })
    await db.ref().update(updates)
    return {
      ok: true,
      message: `Sucessfully delete multiple formConfigs`,
    }
  } catch (error) {
    return {
      ok: false,
      errorMessage: error,
    }
  }
})
