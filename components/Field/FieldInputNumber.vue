<script setup lang="ts">
defineProps({
    label: {
        type: String
    },
    modelValue: {
        type: Number
    },
})
const emit = defineEmits(['update:modelValue'])

function inputHandler(event: Event) {
    const target = event.target as HTMLInputElement
    if (isNaN(Number(target.value))) {
        return
    }
    emit('update:modelValue', Number(target.value))
}
</script>

<template>
    <FieldInput :label="label">
        <input type="number" :value="modelValue" @input="inputHandler" class="field" />
    </FieldInput>
</template>

<style lang="scss" scoped>
.field {
    width: 100%;
}
</style>