import { Form } from '~/types/data'
import { useAuthenticatedFirebase } from '~/server/utils/firebaseDatabase'

export default defineEventHandler(async (event) => {
  const { formData } = await readBody(event)

  if (!formData) {
    return {
      ok: false,
      errorMessage: 'No form data provided',
    }
  }

  try {
    const { firebasePush } = useAuthenticatedFirebase(event)
    const newFormKey = await firebasePush('/forms', formData)
    return {
      ok: true,
      data: newFormKey,
      message: 'Form created successfully',
    }
  } catch (error) {
    return {
      ok: false,
      errorMessage: error,
    }
  }
})
