<script setup lang="ts">
  const emit = defineEmits(['close'])
  const siteCollectionsStore = useSiteCollectionsStore()
  const { activeSiteCollection } = storeToRefs(siteCollectionsStore)
  const uiStore = useUiStore()
  const newModuleName = ref()

  const isFormValid = computed(() => !!newModuleName.value)

  async function clickHandlerSubmit() {
    uiStore.setLoading(true, `Adding ${newModuleName.value} to ${activeSiteCollection.value.url}`)
    await siteCollectionsStore.addNewModuleToSiteCollectionWithName(
      activeSiteCollection.value.key,
      newModuleName.value,
    )
    uiStore.setLoading(false)
    emit('close')
  }

  function closeHandler() {
    emit('close')
  }
</script>

<template>
  <Drawer
    :title="activeSiteCollection.url ?? 'url'"
    :model-value="true"
    width="800"
    :mask-closable="false"
    @on-close="closeHandler">
    <Form>
      <FieldInputText v-model="newModuleName" label="Module Name" required />
    </Form>
    <BaseButtonSaveCancel
      @cancel="closeHandler"
      @save="clickHandlerSubmit"
      :disable-submit="!isFormValid" />
  </Drawer>
</template>
