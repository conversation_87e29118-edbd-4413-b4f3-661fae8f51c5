<script setup lang="ts">
  const sharepointConfigStore = useConfigSharepointStore()
  const { activeSharepointConfig } = storeToRefs(sharepointConfigStore)
</script>
<template>
  <section class="deployment__list">
    <template v-for="list in activeSharepointConfig.lists">
      <ConfigSharepointDeploymentListTableInput
        v-if="sharepointConfigStore.checkIfListIsTableInput(list.InternalName)"
        :list="list" />
      <ConfigSharepointDeploymentListMainRegister :list="list" v-else />
    </template>
  </section>
</template>

<style lang="scss" scoped>
  .deployment__list {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    column-gap: $unit;
  }
</style>
