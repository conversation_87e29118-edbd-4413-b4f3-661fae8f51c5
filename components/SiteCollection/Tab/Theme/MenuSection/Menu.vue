<script setup lang="ts">
const props = defineProps<{
  menuGroupTitleColor: string, submenuTitleColor: string
}>()

const emit = defineEmits<{
  (e: 'update:menuGroupTitleColor', value: string): void
  (e: 'update:submenuTitleColor', value: string): void
}>()
</script>

<template>
  <div>
    <div class="sectionLabel">Menu</div>
    <div class="themeMenu">
      <FormItem label="menuGroupTitle">
        <ColorPicker :modelValue="menuGroupTitleColor" @update:modelValue="emit('update:menuGroupTitleColor', $event)" transfer />
      </FormItem>
      <FormItem label="submenuTitle">
        <ColorPicker :modelValue="submenuTitleColor" @update:modelValue="emit('update:submenuTitleColor', $event)" transfer />
      </FormItem>
    </div>
  </div>
</template>

<style scoped lang="scss">
.themeMenu {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  align-items: start;
  justify-items: start;
}
.sectionLabel {
  font-size: 1rem;
  font-weight: 600;
}
</style>

