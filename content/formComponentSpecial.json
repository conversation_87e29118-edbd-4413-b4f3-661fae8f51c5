{"formSpecialComponent": [{"label": "Table Input", "type": "TableInput", "value": [], "listName": "", "options": [{"id": 1, "name": "Col", "fieldName": "", "type": "Input"}]}, {"label": "Document Display", "type": "DocumentsDisplay", "value": [], "options": [{"id": 1, "name": "FileName", "fieldName": "", "type": "Input"}]}, {"label": "Actions", "type": "Actions", "value": [], "options": [{"id": 1, "name": "FileName", "fieldName": "", "type": "Input"}]}, {"label": "Tags Input", "type": "TagsInput", "value": ""}, {"label": "Document Select", "type": "DocumentSelect", "value": [], "options": [{"id": 1, "name": "Col", "fieldName": "", "type": "Input"}]}, {"label": "Document List Select", "type": "DocumentListSelect", "value": [], "options": [{"id": 1, "name": "Col", "fieldName": "", "type": "Input"}]}, {"label": "New Document Table", "type": "NewDocumentTableInput", "value": [], "listName": "", "options": [{"id": 1, "name": "Col", "fieldName": "", "type": "Input"}]}, {"label": "Search or Create", "type": "SearchOrCreate", "value": "", "listname": "", "formConfigKey": "", "fieldName": "SearchOrCreate"}, {"label": "Assessments", "type": "Assessments", "value": [], "listname": ""}, {"label": "Custom Component", "fieldName": "CustomComponent", "type": "CustomComponent", "value": [], "listname": ""}, {"label": "Basic Risk Template", "fieldName": "BasicRiskTemplate", "type": "BasicRiskTemplate", "value": [], "options": [{"fieldName": "Risk", "id": 1, "name": "Risk", "span": "2", "type": "Input"}, {"fieldName": "Area", "id": 1, "name": "Area Affected", "span": "3", "type": "Input"}, {"fieldName": "Severity", "id": 1, "name": "Severity", "options": ["Low", "Moderate", "High"], "span": "2", "type": "Select"}, {"fieldName": "Likelihood", "id": 1, "name": "Likelihood", "options": ["Unlikely", "Possible", "Likely"], "span": "2", "type": "Select"}, {"fieldName": "RiskImpact", "id": 1, "name": "Risk Impact", "options": ["Very Low", "Low", "Medium", "High", "Very High"], "span": "3", "type": "Select"}, {"fieldName": "Controls", "id": 1, "name": "Control Actions", "span": "3", "type": "Input"}, {"fieldName": "SeverityJustified", "id": 1, "name": "Severity Justified", "options": ["Low", "Moderate", "High"], "span": "2", "type": "Select"}, {"fieldName": "LikelihoodJustified", "id": 1, "name": "Likelihood Justified", "options": ["Unlikely", "Possible", "Likely"], "span": "2", "type": "Select"}, {"fieldName": "FinalRI", "id": 1, "name": "Risk Impact Final", "options": ["Very Low", "Low", "Medium", "High", "Very High"], "span": "2", "type": "Select"}], "listname": ""}, {"label": "FMEA Template", "fieldName": "FMEATemplate", "type": "FMEATemplate", "options": [{"fieldName": "PSOF", "id": 1, "name": "Processing Step or Function", "span": "3", "type": "Input"}, {"fieldName": "Failure", "id": 1, "name": "Potential Failure(s)", "span": "3", "type": "Input"}, {"fieldName": "Effect", "id": 1, "name": "Potential Effect(s) of Failure", "span": "3", "type": "Input"}, {"fieldName": "Cause", "id": 1, "name": "Potential Causes of Failure", "span": "3", "type": "Input"}, {"fieldName": "Controls", "id": 1, "name": "Current Process/Design Control(s)", "span": "4", "type": "Input"}, {"fieldName": "RPN", "id": 1, "name": "RPN", "span": "2", "type": "RPN"}, {"fieldName": "Severity", "id": 1, "name": "Severity", "span": "2", "hidden": true, "type": "Input"}, {"fieldName": "<PERSON><PERSON><PERSON><PERSON>", "id": 1, "name": "<PERSON><PERSON><PERSON><PERSON>", "span": "2", "hidden": true, "type": "Input"}, {"fieldName": "Detection", "id": 1, "name": "Detection", "span": "2", "hidden": true, "type": "Input"}, {"fieldName": "FAdditionalControls", "id": 1, "name": "Recommended Additional Control Action(s) ", "span": "4", "type": "Input"}, {"fieldName": "nRPN", "id": 1, "name": "New RPN", "span": "2", "type": "RPN"}, {"fieldName": "NSeverity", "id": 1, "name": "New Severity", "span": "2", "hidden": true, "type": "Input"}, {"fieldName": "<PERSON><PERSON><PERSON><PERSON>", "id": 1, "name": "New Occurence", "span": "2", "hidden": true, "type": "Input"}, {"fieldName": "NDetection", "id": 1, "name": "New Detection", "span": "2", "hidden": true, "type": "Input"}], "value": [], "listname": ""}, {"label": "RPN Assessment", "fieldName": "RPNAssessment", "type": "RPNAssessment", "options": [{"fieldName": "Severity", "id": 1, "name": "Severity", "span": "4", "type": "Input"}, {"fieldName": "<PERSON><PERSON><PERSON><PERSON>", "id": 1, "name": "<PERSON><PERSON><PERSON><PERSON>", "span": "4", "type": "Input"}, {"fieldName": "Detectability", "id": 1, "name": "Detectability", "span": "4", "type": "Input"}, {"fieldName": "RPN", "id": 1, "name": "RPN", "span": "4", "type": "Input"}, {"fieldName": "RiskCategory", "id": 1, "name": "RiskCategory", "options": ["Low", "Medium", "High", "Critical"], "span": "4", "type": "Select"}], "value": [], "listname": ""}, {"label": "Final RPN Assessment", "fieldName": "FinalRPNAssessment", "type": "FinalRPNAssessment", "options": [{"fieldName": "FinalSeverity", "id": 1, "name": "Final Severity", "span": "4", "type": "Input"}, {"fieldName": "FinalOccurence", "id": 1, "name": "Final Occurence", "span": "4", "type": "Input"}, {"fieldName": "FinalDetectability", "id": 1, "name": "Final Detectability", "span": "4", "type": "Input"}, {"fieldName": "FinalRPN", "id": 1, "name": "Final RPN", "span": "4", "type": "Input"}, {"fieldName": "FinalRiskCategory", "id": 1, "name": "Final RiskCategory", "options": ["Low", "Medium", "High", "Critical"], "span": "4", "type": "Select"}], "value": [], "listname": ""}, {"label": "Multi Form Component", "fieldName": "MultiFormComponent", "type": "MultiForm", "value": []}]}