<script setup lang="ts">
  import type { SharepointList } from '~/types/sharepointConfig'
  const props = defineProps({
    modelValue: {
      type: Object as PropType<SharepointList>,
      required: true,
    },
  })

  const sharepointConfigStore = useConfigSharepointStore()
  const { activeTableInputFields } = storeToRefs(sharepointConfigStore)

  const emit = defineEmits(['update:modelValue'])
  const count = ref(0)

  watch(
    activeTableInputFields,
    () => {
      count.value = 0
      activeTableInputFields.value.forEach((field) => {
        if (field.fieldName === props.modelValue.InternalName) {
          count.value++
        }
      })
    },
    { immediate: true },
  )

  function getTooltipText(fieldName: string) {
    if (count.value > 1) {
      return `${count.value} instances found`
    }
    return `List is connected to a table input.`
  }
</script>

<template>
  <FieldInputText v-model="modelValue.DisplayName" />
  <div class="internal__name">
    <FieldInputText v-model="modelValue.InternalName" />
    <div
      class="table__icon"
      v-if="sharepointConfigStore.checkIfListIsTableInput(modelValue.InternalName)">
      <Tooltip :content="getTooltipText(modelValue.InternalName)" transfer>
        <Icon type="ios-grid" size="20" class="icon" />
        <span class="count" :class="{ no__instances: count === 0 }">{{ count }}</span>
      </Tooltip>
    </div>
  </div>
</template>

<style lang="scss" scoped>
  .internal__name {
    display: grid;
    grid-template-columns: 1fr auto;
    column-gap: $unit;
  }
  .table__icon {
    height: 32px;
    background: $primary-charcoal-10;
    border-radius: 50%;
    .icon {
      padding-top: 6px;
    }
    .count {
      position: absolute;
      top: 0;
      right: -4px;
      font-weight: bolder;
      color: $primary-blue;
      font-size: 0.7rem;
      padding: 1px 2px;
      border-radius: 50%;

      &.no__instances {
        color: red;
      }
    }
  }
</style>
