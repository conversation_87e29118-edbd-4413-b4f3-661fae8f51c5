export default defineNuxtConfig({
  devtools: { enabled: false },
  ssr: false,
  runtimeConfig: {
    public: {
      FIREBASE_DATABASE_URL: process.env.FIREBASE_DATABASE_URL,
      FIREBASE_API_KEY: process.env.FIREBASE_API_KEY,
      FIREBASE_AUTH_DOMAIN: process.env.FIREBASE_AUTH_DOMAIN,
      FIREBASE_PROJECT_ID: process.env.FIREBASE_PROJECT_ID,
      FIREBASE_STORAGE_BUCKET: process.env.FIREBASE_STORAGE_BUCKET,
      FIREBASE_MESSAGING_SENDER_ID: process.env.FIREBASE_MESSAGING_SENDER_ID,
      FIREBASE_APP_ID: process.env.FIREBASE_APP_ID,
      FIREBASE_MEASUREMENT_ID: process.env.FIREBASE_MEASUREMENT_ID,
    },
    FIREBASE_PROJECT_ID: process.env.FIREBASE_PROJECT_ID,
    FIREBASE_CLIENT_EMAIL: process.env.FIREBASE_CLIENT_EMAIL,
    FIREBASE_PRIVATE_KEY: process.env.FIREBASE_PRIVATE_KEY,
    FIREBASE_API_KEY: process.env.FIREBASE_API_KEY,
    FIREBASE_DATABASE_URL: process.env.FIREBASE_DATABASE_URL,
    WORKBENCH_KEY: process.env.WORKBENCH_KEY,
    SHAREPOINT_CLIENT_ID: process.env.SHAREPOINT_CLIENT_ID,
  },
  routeRules: {
    '/api/**': {
      cors: true,
    },
  },
  vue: {
    compilerOptions: {
      isCustomElement: (tag) => {
        return tag.startsWith('fluent-') || ['span__url'].includes(tag)
      },
    },
  },
  modules: [
    '@pinia/nuxt',
    [
      '@nuxtjs/google-fonts',
      {
        display: 'swap',
        families: {
          QuickSand: {
            wght: [100, 300, 400, 500, 600, 900],
          },
          Cardo: {
            wght: [100, 300, 400, 500, 600, 900],
          },
        },
      },
    ],
    'nuxt-lodash',
    '@vueuse/nuxt',
  ],
  css: ['@/assets/main.scss'],
  vite: {
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: '@import "@/assets/global.scss";',
        },
        less: {
          javascriptEnabled: true,
        },
      },
    },
  },
  $production: {
    nitro: {
      preset: 'vercel',
    },
  },
  nitro: {
    esbuild: {
      options: {
        target: 'esnext',
      },
    },
  },
})
