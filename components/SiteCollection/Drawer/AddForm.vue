<script setup lang="ts">
  const emit = defineEmits(['close'])
  const siteCollectionsStore = useSiteCollectionsStore()
  const { activeSiteCollection } = storeToRefs(siteCollectionsStore)
  const uiStore = useUiStore()
  const newFormName = ref()
  const isFormValid = computed(() => !!newFormName.value)

  async function clickHandlerSubmit() {
    uiStore.setLoading(true, `Adding ${newFormName.value} to ${activeSiteCollection.value.url}`)
    await siteCollectionsStore.addNewFormToSiteCollectionWithName(
      activeSiteCollection.value.key,
      newFormName.value,
    )
    uiStore.setLoading(false)
    emit('close')
  }

  function closeHandler() {
    emit('close')
  }
</script>

<template>
  <Drawer
    :title="activeSiteCollection.url ?? 'url'"
    :model-value="true"
    width="800"
    :mask-closable="false"
    @on-close="closeHandler">
    <Form>
      <FieldInputText v-model="newFormName" label="Form Name" required />
    </Form>
    <BaseButtonSaveCancel
      @cancel="closeHandler"
      @save="clickHandlerSubmit"
      :disable-submit="!isFormValid" />
  </Drawer>
</template>
