import type {
  FormFieldData,
  ActionField,
  AssessmentsField,
  BasicRiskField,
  CustomComponentField,
  DatepickerField,
  DocumentDisplayField,
  DocumentListSelectField,
  DocumentSelectField,
  FMEAField,
  FileUploadField,
  FinalRPNField,
  HiddenField,
  ImageField,
  LinkField,
  MultiFormField,
  NewDocumentTableField,
  NumberInputField,
  PeoplePickerField,
  RadioButtonField,
  RPNField,
  SearchOrCreateField,
  SelectField,
  TableInputField,
  TagsInputField,
  TextAreaField,
  TextField,
  TextInputField,
} from "~/types/form";
import { FormFieldType } from "~/types/form";
export const useFormBuilderFieldSetting = () => {
  const formBuilderStore = useFormBuilderStore();
  const { activeEditField } = storeToRefs(formBuilderStore);

  // Type Guards
  function isActionField(
    field: FormFieldData | null | undefined
  ): field is ActionField {
    return field?.type === FormFieldType.ACTION;
  }

  function isAssessmentsField(
    field: FormFieldData | null | undefined
  ): field is AssessmentsField {
    return field?.type === FormFieldType.ASSESSMENTS;
  }

  function isBasicRiskField(
    field: FormFieldData | null | undefined
  ): field is BasicRiskField {
    return field?.type === FormFieldType.BASICRISK;
  }

  function isCustomComponentField(
    field: FormFieldData | null | undefined
  ): field is CustomComponentField {
    return field?.type === FormFieldType.CUSTOMCOMPONENT;
  }

  function isDatepickerField(
    field: FormFieldData | null | undefined
  ): field is DatepickerField {
    return field?.type === FormFieldType.DATEPICKER;
  }

  function isDocumentDisplayField(
    field: FormFieldData | null | undefined
  ): field is DocumentDisplayField {
    return field?.type === FormFieldType.DOCUMENTDISPLAY;
  }

  function isDocumentListSelectField(
    field: FormFieldData | null | undefined
  ): field is DocumentListSelectField {
    return field?.type === FormFieldType.DOCUMENTLISTSELECT;
  }

  function isDocumentSelectField(
    field: FormFieldData | null | undefined
  ): field is DocumentSelectField {
    return field?.type === FormFieldType.DOCUMENTSELECT;
  }

  function isFMEAField(
    field: FormFieldData | null | undefined
  ): field is FMEAField {
    return field?.type === FormFieldType.FMEA;
  }

  function isFileUploadField(
    field: FormFieldData | null | undefined
  ): field is FileUploadField {
    return field?.type === FormFieldType.FILEUPLOAD;
  }

  function isFinalRPNField(
    field: FormFieldData | null | undefined
  ): field is FinalRPNField {
    return field?.type === FormFieldType.FINALRPN;
  }

  function isHiddenField(
    field: FormFieldData | null | undefined
  ): field is HiddenField {
    return field?.type === FormFieldType.HIDDEN;
  }

  function isImageField(
    field: FormFieldData | null | undefined
  ): field is ImageField {
    return field?.type === FormFieldType.IMAGE;
  }

  function isLinkField(
    field: FormFieldData | null | undefined
  ): field is LinkField {
    return field?.type === FormFieldType.LINK;
  }

  function isMultiFormField(
    field: FormFieldData | null | undefined
  ): field is MultiFormField {
    return field?.type === FormFieldType.MULTIFORM;
  }

  function isNewDocumentTableField(
    field: FormFieldData | null | undefined
  ): field is NewDocumentTableField {
    return field?.type === FormFieldType.NEWDOCUMENTTABLE;
  }

  function isNumberInputField(
    field: FormFieldData | null | undefined
  ): field is NumberInputField {
    return field?.type === FormFieldType.NUMBERINPUT;
  }

  function isPeoplePickerField(
    field: FormFieldData | null | undefined
  ): field is PeoplePickerField {
    return field?.type === FormFieldType.PEOPLEPICKER;
  }

  function isRadioButtonField(
    field: FormFieldData | null | undefined
  ): field is RadioButtonField {
    return field?.type === FormFieldType.RADIOBUTTON;
  }

  function isRPNField(
    field: FormFieldData | null | undefined
  ): field is RPNField {
    return field?.type === FormFieldType.RPN;
  }

  function isSearchOrCreateField(
    field: FormFieldData | null | undefined
  ): field is SearchOrCreateField {
    return field?.type === FormFieldType.SEARCHORCREATE;
  }

  function isSelectField(
    field: FormFieldData | null | undefined
  ): field is SelectField {
    return field?.type === FormFieldType.SELECT;
  }

  function isTableInputField(
    field: FormFieldData | null | undefined
  ): field is TableInputField {
    return field?.type === FormFieldType.TABLEINPUT;
  }

  function isTagsInputField(
    field: FormFieldData | null | undefined
  ): field is TagsInputField {
    return field?.type === FormFieldType.TAGSINPUT;
  }

  function isTextAreaField(
    field: FormFieldData | null | undefined
  ): field is TextAreaField {
    return field?.type === FormFieldType.TEXTAREA;
  }

  function isTextField(
    field: FormFieldData | null | undefined
  ): field is TextField {
    return field?.type === FormFieldType.TEXT;
  }

  function isTextInputField(
    field: FormFieldData | null | undefined
  ): field is TextInputField {
    return field?.type === FormFieldType.TEXTINPUT;
  }

  // Computed Properties
  const actionField = computed(() =>
    isActionField(activeEditField.value) ? activeEditField.value : null
  );
  const assessmentsField = computed(() =>
    isAssessmentsField(activeEditField.value) ? activeEditField.value : null
  );
  const basicRiskField = computed(() =>
    isBasicRiskField(activeEditField.value) ? activeEditField.value : null
  );
  const customComponentField = computed(() =>
    isCustomComponentField(activeEditField.value) ? activeEditField.value : null
  );
  const datepickerField = computed(() =>
    isDatepickerField(activeEditField.value) ? activeEditField.value : null
  );
  const documentDisplayField = computed(() =>
    isDocumentDisplayField(activeEditField.value) ? activeEditField.value : null
  );
  const documentListSelectField = computed(() =>
    isDocumentListSelectField(activeEditField.value)
      ? activeEditField.value
      : null
  );
  const documentSelectField = computed(() =>
    isDocumentSelectField(activeEditField.value) ? activeEditField.value : null
  );
  const fmeaField = computed(() =>
    isFMEAField(activeEditField.value) ? activeEditField.value : null
  );
  const fileUploadField = computed(() =>
    isFileUploadField(activeEditField.value) ? activeEditField.value : null
  );
  const finalRPNField = computed(() =>
    isFinalRPNField(activeEditField.value) ? activeEditField.value : null
  );
  const hiddenField = computed(() =>
    isHiddenField(activeEditField.value) ? activeEditField.value : null
  );
  const imageField = computed(() =>
    isImageField(activeEditField.value) ? activeEditField.value : null
  );
  const linkField = computed(() =>
    isLinkField(activeEditField.value) ? activeEditField.value : null
  );
  const multiFormField = computed(() =>
    isMultiFormField(activeEditField.value) ? activeEditField.value : null
  );
  const newDocumentTableField = computed(() =>
    isNewDocumentTableField(activeEditField.value)
      ? activeEditField.value
      : null
  );
  const numberInputField = computed(() =>
    isNumberInputField(activeEditField.value) ? activeEditField.value : null
  );
  const peoplePickerField = computed(() =>
    isPeoplePickerField(activeEditField.value) ? activeEditField.value : null
  );
  const radioButtonField = computed(() =>
    isRadioButtonField(activeEditField.value) ? activeEditField.value : null
  );
  const rpnField = computed(() =>
    isRPNField(activeEditField.value) ? activeEditField.value : null
  );
  const searchOrCreateField = computed(() =>
    isSearchOrCreateField(activeEditField.value) ? activeEditField.value : null
  );
  const selectField = computed(() =>
    isSelectField(activeEditField.value) ? activeEditField.value : null
  );
  const tableInputField = computed(() =>
    isTableInputField(activeEditField.value) ? activeEditField.value : null
  );
  const tagsInputField = computed(() =>
    isTagsInputField(activeEditField.value) ? activeEditField.value : null
  );
  const textAreaField = computed(() =>
    isTextAreaField(activeEditField.value) ? activeEditField.value : null
  );
  const textField = computed(() =>
    isTextField(activeEditField.value) ? activeEditField.value : null
  );
  const textInputField = computed(() =>
    isTextInputField(activeEditField.value) ? activeEditField.value : null
  );

  return {
    activeEditField,
    actionField,
    assessmentsField,
    basicRiskField,
    customComponentField,
    datepickerField,
    documentDisplayField,
    documentListSelectField,
    documentSelectField,
    fmeaField,
    fileUploadField,
    finalRPNField,
    hiddenField,
    imageField,
    linkField,
    multiFormField,
    newDocumentTableField,
    numberInputField,
    peoplePickerField,
    radioButtonField,
    rpnField,
    searchOrCreateField,
    selectField,
    tableInputField,
    tagsInputField,
    textAreaField,
    textField,
    textInputField,
  };
};
