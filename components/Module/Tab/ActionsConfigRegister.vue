<script setup lang="ts">
import type { PropType } from 'vue';
import { ActionStatus, ActionType, type Action, type NewWorkflowAction } from '~/types/workflow';
import type { Column, Stage } from '~/types/data'; import { MODULE_NEW_ACTION_CONFIG } from '~/constants/default-values';
;

const props = defineProps({
    modelValue: {
        type: Object as PropType<NewWorkflowAction>,
        required: true
    },
    stageKey: {
        type: String,
        required: true
    }
})
const emit = defineEmits(['update:modelValue', 'submit'])
const stageStore = useStagesStore();
const stage = computed<Stage>(() => stageStore.getStageByFirebaseKey(props.stageKey))
const editDrawer = ref(false)
// const editModal = ref(false)
const activeAction = ref<Action>({} as Action)
const activeIndex = ref(0)
const NEW_ACTION: Action = {
    ...MODULE_NEW_ACTION_CONFIG, Stage: props.stageKey
}

const columns: Column[] = [
    { title: 'Action', key: 'ActionDescription' },
    { title: 'Status', key: 'Status' },
    { title: 'Type', key: 'Type' },
    { title: 'Assigned To', key: 'AssignedToId' },
    {
        title: 'Action', slot: 'action', width: 150
    }
]

const isActionEmpty = computed(() =>
{
    if (!props.modelValue.actions)
    {
        return true
    }
    return props.modelValue.actions.length === 0
})


function clickHandlerAdd()
{
    const newModelValue = useCloneDeep(props.modelValue)
    newModelValue.actions.push(NEW_ACTION)
    emit('update:modelValue', newModelValue)
    activeAction.value = useCloneDeep(NEW_ACTION)
    activeIndex.value = props.modelValue.actions.length
}

function clickHandlerDelete(index: number)
{
    const newModelValue = useCloneDeep(props.modelValue)
    newModelValue.actions.splice(index, 1)
    emit('update:modelValue', newModelValue)
}

function clickHandlerEdit(index: number)
{
    editDrawer.value = true
    activeIndex.value = index
    activeAction.value = useCloneDeep(props.modelValue.actions[index])
}

function clickHandlerCancel()
{
    editDrawer.value = false
}

function clickHandlerSubmitDrawer()
{
    editDrawer.value = false
    const newModelValue = useCloneDeep(props.modelValue)
    newModelValue.actions[activeIndex.value] = useCloneDeep(activeAction.value)
    emit('update:modelValue', newModelValue)
    emit('submit')
}

function clickHandlerSubmit()
{
    editDrawer.value = false
    emit('submit')
}

function clickHandlerEditModal()
{
    editDrawer.value = true
}
</script>

<template>
    <Drawer :title="stage.name" placement="right" v-model="editDrawer" width="50%">
        <ModuleTabActionsConfigSetting v-model="activeAction" />
        <BaseButtonSaveCancel @cancel="clickHandlerCancel" @save="clickHandlerSubmitDrawer" />
    </Drawer>
    <Card dis-hover class="action--card">
        <template #title>
            <div class="card--header">
                <b>{{ stage.name }}</b>
                <div class="buttons">
                    <BaseButtonAdd @click="clickHandlerAdd" appearance="default" text="Action" />
                    <BaseButtonSubmit @click="clickHandlerSubmit" />
                </div>
            </div>
        </template>
        <div class="form__wrapper">
            <template v-if="isActionEmpty">
                {{ 'No actions found' }}
            </template>
            <BaseRegisterList :list="modelValue.actions" :columns="columns" v-else>
                <template #action="{ row, index }">
                    <div class="action--button">
                        <BaseButtonEdit @click="clickHandlerEdit(index)" />
                        <BaseButtonDelete @click="clickHandlerDelete(index)" />
                    </div>
                </template>
            </BaseRegisterList>
        </div>
    </Card>
</template>

<style lang="scss" scoped>
.card--header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .buttons {
        display: flex;
        gap: $unit;
    }
}

.description {
    padding-left: $padding;
    padding-right: $padding;
}

.action--button {
    display: flex;
    gap: $unit;
}

.action--card {
    padding-bottom: $margin;
}
</style>