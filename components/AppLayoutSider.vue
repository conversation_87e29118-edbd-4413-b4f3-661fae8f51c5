<script setup lang="ts">
  const route = useRoute()

  const siderCollapsed = ref(false)

  const showSiteCollectionMenu = computed(() => {
    return route.params.tenant
  })

  function clickHandlerSiderToggle() {
    siderCollapsed.value = !siderCollapsed.value
  }
</script>
<template>
  <Sider
    class="sider"
    width="325"
    hide-trigger
    collapsible
    :collapsed-width="25"
    ref="sider"
    v-model="siderCollapsed">
    <template v-if="!siderCollapsed">
      <AppSiderMenuItem to="/tenants" title="Tenants" />
      <div class="tenant__title" @click="$router.push(`/tenants/${route.params.tenant}`)">
        {{ route.params.tenant }}
      </div>
      <AppSiderMenuSiteCollections v-if="showSiteCollectionMenu" />
      <AppSiderMenuItem to="/cores" title="Cores" />
    </template>
    <div
      class="collapse__button"
      @click="clickHandlerSiderToggle"
      :class="{ collapsed: siderCollapsed }">
      <div class="icon">
        <BaseIconChevronRight v-if="siderCollapsed" />
        <BaseIconChevronLeft v-else />
      </div>
    </div>
  </Sider>
</template>

<style scoped lang="scss">
  $sider-width: 325px;
  $collapsed-width: 25px;

  .sider {
    height: calc(100vh - 90px);
    overflow-y: auto;
    background: $sider-bg;
    padding-top: $margin;
  }

  .tenant__title {
    margin: 0 $margin;
    font-size: 0.875rem;
    font-weight: 400;
    color: #161616;
    background: $sider-onpress-color;

    &:hover {
      text-decoration: underline;
      cursor: pointer;
    }
  }

  .collapse__button {
    position: fixed;
    bottom: 0;
    width: $sider-width;
    display: flex;
    justify-content: center;
    background: $fluent-foreground3;
    color: white;
    padding: $unit;
    cursor: pointer;

    .icon {
      height: 1.5rem;
      width: 1.5rem;
    }

    &.collapsed {
      width: $collapsed-width;
      padding: $unit 0;
    }
  }
</style>
