import { createPrivate<PERSON><PERSON> } from 'node:crypto'
import { ConfidentialClientApplication, Configuration } from '@azure/msal-node'

export const getAccessToken = async (tenantUrl: string, tenantId: string) => {
  try {
    console.log('getAccessToken called with:')
    console.log('- tenantUrl:', tenantUrl)
    console.log('- tenantId:', tenantId)

    const runtimeConfig = useRuntimeConfig()
    console.log('Runtime config loaded')

    const rawKey = runtimeConfig.WORKBENCH_KEY
    if (!rawKey) {
      console.error('WORKBENCH_KEY is not set in runtime config')
      throw new Error('WORKBENCH_KEY is not set')
    }
    console.log('WORKBENCH_KEY found in runtime config')

    try {
      const privateKeyObject = createPrivateKey({
        key: rawKey.replace(/\\n/g, '\n'),
        format: 'pem',
      })
      console.log('Private key object created successfully')
      console.log('Key format:', privateKeyObject.asymmetricKeyType)
      console.log('Key type:', privateKeyObject.type)

      const privateKey = privateKeyObject.export({
        format: 'pem',
        type: 'pkcs8',
      })
      console.log('Private key exported successfully')

      // test wb clientId: b407b248-fad4-4822-8d05-456441adaa99
      // prod wb clientId: 0c6e6a75-2629-4285-b8a6-d64908bbad5a
      const clientId = 'b407b248-fad4-4822-8d05-456441adaa99'

      const config: Configuration = {
        auth: {
          authority: `https://login.microsoftonline.com/${tenantId}`,
          clientId: clientId,
          clientCertificate: {
            thumbprint: '5D656A5A81A048E13DBCAD1BDF6C7365851BD190',
            privateKey: privateKey as string,
          },
        },
      }
      console.log('Configuration created')

      console.log('Creating ConfidentialClientApplication...')
      const cca = new ConfidentialClientApplication(config)
      console.log('ConfidentialClientApplication created')

      console.log('Acquiring token with scope:', `${tenantUrl}/.default`)
      const result = await cca.acquireTokenByClientCredential({
        scopes: [`${tenantUrl}/.default`],
      })

      if (!result) {
        console.error('No result returned from acquireTokenByClientCredential')
        return null
      }

      console.log('Token acquired successfully')
      return result.accessToken
    } catch (cryptoError) {
      console.error('Error in crypto operations:', cryptoError)
      throw cryptoError
    }
  } catch (error) {
    console.error('Error in getAccessToken:', error)
    throw error
  }
}
