export type CoreSiteColumn = {
  ColumnType: string;
  DisplayName: string;
  InternalName: string;
  id: number;
};

export type CoreList = {
  Template: CoreListTemplate;
  DisplayName: string;
  InternalName: string;
  SiteColumns?: CoreSiteColumn[];
  id: number;
};

export enum CoreListTemplate
{
  GENERIC_LIST = "GenericList",
  DOCUMENT_LIBRARY = "DocumentLibrary",
  RECORD_LIBRARY = "RecordLibrary",
}

export type CoreListSetting = {
  lists: CoreList[];
};

export type Core = CoreListSetting & {
  key: string;
  name: string;
  siteColumns: CoreSiteColumn[];
};

export type CoreData = Record<string, Core>;
