import { CoreListTemplate } from '~/types/core'
import { CustomOptionType, type CustomOptionData, type TableInputVariantOption } from '~/types/form'
import { PermissionType } from '~/types/permission'
import {
  RegisterColumnOrder,
  RegisterColumnType,
  type Register,
  type RegisterColumn,
  type RegisterFilter,
} from '~/types/register'
import { CustomListType } from '~/types/sharepointConfig'
import { ActionStatus, ActionType } from '~/types/workflow'

export const CONFIG_PERMISSION_NEW_PERMISSION = {
  name: PermissionType.CANCELPROCESS,
  groups: [],
}

export const CONFIG_REGISTER_NEW_COLUMN_TO_SHOW = {
  type: RegisterColumnType.TEXT,
  span: 0,
  sortable: false,
  order: RegisterColumnOrder.NA,
} as RegisterColumn

export const CONFIG_REGISTER_NEW_FILTER = {
  fieldName: '',
  value: '',
} as RegisterFilter

export const CONFIG_REGISTER_NEW_REGISTER = {
  name: 'New Register',
  columns: [] as RegisterColumn[],
  selectedFilters: [] as RegisterFilter[],
} as Register

export const CONFIG_SHAREPOINT_NEW_CUSTOM_LIST = {
  type: CustomListType.CONFIG,
  fieldName: '',
  listname: '',
}

export const SHOW_DEPLOYMENT_LIST_ITEMS = 10

export const MAINREGISTER_REQUIRED_FIELD = [
  {
    ColumnType: 'Text',
    DisplayName: 'Reject Comment',
    InternalName: 'RejectComment',
    fieldName: 'RejectComment',
  },
  {
    ColumnType: 'Text',
    DisplayName: 'Ibiqs Status',
    InternalName: 'IbiqsStatus',
    fieldName: 'IbiqsStatus',
  },
  {
    ColumnType: 'Text',
    DisplayName: 'Stage',
    InternalName: 'Stage',
    fieldName: 'Stage',
  },
  {
    ColumnType: 'Text',
    DisplayName: 'Module',
    InternalName: 'Module',
    fieldName: 'Module',
  },
  {
    ColumnType: 'Text',
    DisplayName: 'Module Item ID',
    InternalName: 'ModuleItemID',
    fieldName: 'ModuleItemID',
  },
  {
    ColumnType: 'Text',
    DisplayName: 'Completed Date',
    InternalName: 'CompletedDate',
    fieldName: 'CompletedDate',
  },
]

export const TABLEINPUT_REQUIRED_FIELD = [
  {
    ColumnType: 'Text',
    DisplayName: 'Reject Comment',
    InternalName: 'RejectComment',
    fieldName: 'RejectComment',
  },
  {
    ColumnType: 'Text',
    DisplayName: 'Ibiqs Status',
    InternalName: 'IbiqsStatus',
    fieldName: 'IbiqsStatus',
  },
  {
    ColumnType: 'Text',
    DisplayName: 'Stage',
    InternalName: 'Stage',
    fieldName: 'Stage',
  },
  {
    ColumnType: 'Text',
    DisplayName: 'Module',
    InternalName: 'Module',
    fieldName: 'Module',
  },
  {
    ColumnType: 'Text',
    DisplayName: 'Module Item ID',
    InternalName: 'ModuleItemID',
    fieldName: 'ModuleItemID',
  },
  {
    ColumnType: 'Text',
    DisplayName: 'Completed Date',
    InternalName: 'CompletedDate',
    fieldName: 'CompletedDate',
  },
]

export const CONFIG_SHAREPOINT_NEW_LIST = { DisplayName: '', InternalName: '' }

export const CONFIG_SHAREPOINT_REQUIRED_TABLE_LIST = [
  {
    name: 'Module',
    fieldName: 'Module',
    type: CustomOptionType.TEXT,
  },
  {
    name: 'Module Item ID',
    fieldName: 'ModuleItemID',
    type: CustomOptionType.TEXT,
  },
  {
    name: 'Ibiqs Active',
    fieldName: 'IbiqsActive',
    type: CustomOptionType.TEXT,
  },
] as CustomOptionData[]

export const CORE_NEW_LIST = {
  DisplayName: '',
  InternalName: '',
  Template: CoreListTemplate.GENERIC_LIST,
}

export const CORE_NEW_SITECOLUMN = {
  ColumnType: '',
  DisplayName: '',
  InternalName: '',
}

export const FORM_NEW_TABLE_INPUT_VARIANT_OPTION = {
  name: 'New Column',
  customColSpan: false,
  colSpan: 0,
} as TableInputVariantOption

export const FORM_NEW_CUSTOM_OPTION = {
  name: 'New Column',
  fieldName: '',
  type: CustomOptionType.INPUT,
  customColSpan: false,
  colSpan: 0,
} as CustomOptionData

export const FORM_NEW_MULTIFORM = {
  formKey: '',
  actionDescription: 'New Action',
  groups: '',
}

export const FORM_NEW_OPTION = {
  name: '',
}

export const MODULE_NEW_ACTION_CONFIG = {
  ActionDescription: 'New Action',
  AssignedToId: [],
  Status: ActionStatus.NOTSTARTED,
  Type: ActionType.GROUP,
}

export const SITECOLLECTION_NEW_CUSTOM_NAVIGATION = {
  title: '',
  link: '',
  icon: '',
  iframe: true,
}

export const SITECOLLECTION_PERMISSIONS = [
  { name: 'showConfig', desc: 'To show config menu' },
  { name: 'showChangeType', desc: 'To show change type templates menu item' },
  {
    name: 'showAssessmentTemplate',
    desc: 'To show assessment template menu item',
  },
]

export const SITECOLLECTION_NEW_PERMISSION = {
  name: '',
  groups: [],
}

export const DEFAULT_THEME = {
  primaryColor: '#ffffff',
  secondaryColor: '#ffffff',
  accentColor: '#ffffff',
  buttonColor: '#ffffff',
  buttonHoverColor: '#ffffff',
  linkColor: '#ffffff',
  linkBackgroundColor: '#ffffff',
  linkActiveTextColor: '#ffffff',
  menuGroupTitleColor: '#ffffff',
  submenuTitleColor: '#ffffff',
  dashboardCardH1Color: '#ffffff',
  dashboardCardH4Color: '#ffffff',
}
