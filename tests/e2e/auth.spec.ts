import { test, expect } from '@playwright/test'

test.describe('Authentication Flow', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the login page
    await page.goto('/')
  })

  test('should display login form', async ({ page }) => {
    await expect(page.locator('input[type="email"]')).toBeVisible()
    await expect(page.locator('input[type="password"]')).toBeVisible()
    await expect(page.locator('button[type="submit"]')).toBeVisible()
  })

  test('should show validation errors for empty fields', async ({ page }) => {
    // Click submit without filling fields
    await page.click('button[type="submit"]')
    
    // Check for validation messages
    await expect(page.locator('.error-message')).toBeVisible()
  })

  test('should show error for invalid credentials', async ({ page }) => {
    // Fill in invalid credentials
    await page.fill('input[type="email"]', '<EMAIL>')
    await page.fill('input[type="password"]', 'wrongpassword')
    await page.click('button[type="submit"]')
    
    // Wait for error message
    await expect(page.locator('.error-message')).toContainText('Login Failed')
  })

  test('should redirect to tenants page on successful login', async ({ page }) => {
    // Mock successful login response
    await page.route('**/api/auth/login', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          user: {
            id: 'test-user-id',
            email: '<EMAIL>',
          },
          customToken: 'mock-custom-token',
        }),
      })
    })

    // Fill in valid credentials
    await page.fill('input[type="email"]', '<EMAIL>')
    await page.fill('input[type="password"]', 'password123')
    await page.click('button[type="submit"]')
    
    // Wait for redirect
    await expect(page).toHaveURL('/tenants')
  })

  test('should logout successfully', async ({ page }) => {
    // First login
    await page.route('**/api/auth/login', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          user: {
            id: 'test-user-id',
            email: '<EMAIL>',
          },
          customToken: 'mock-custom-token',
        }),
      })
    })

    await page.fill('input[type="email"]', '<EMAIL>')
    await page.fill('input[type="password"]', 'password123')
    await page.click('button[type="submit"]')
    
    await expect(page).toHaveURL('/tenants')
    
    // Mock logout response
    await page.route('**/api/auth/logout', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ ok: true }),
      })
    })
    
    // Click logout
    await page.click('[data-testid="user-avatar"]')
    await page.click('[data-testid="logout-button"]')
    
    // Should redirect to login page
    await expect(page).toHaveURL('/')
  })

  test('should persist session across page reloads', async ({ page }) => {
    // Mock auth check
    await page.route('**/api/auth/me', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          user: {
            id: 'test-user-id',
            email: '<EMAIL>',
          },
        }),
      })
    })

    // Set session cookie
    await page.context().addCookies([{
      name: 'session',
      value: 'valid-session-token',
      domain: 'localhost',
      path: '/',
    }])

    // Reload page
    await page.reload()
    
    // Should stay on authenticated page
    await expect(page).toHaveURL('/tenants')
  })

  test('should redirect to login when session expires', async ({ page }) => {
    // Mock expired session
    await page.route('**/api/auth/me', async route => {
      await route.fulfill({
        status: 401,
        contentType: 'application/json',
        body: JSON.stringify({ user: null }),
      })
    })

    // Try to access protected page
    await page.goto('/tenants')
    
    // Should redirect to login
    await expect(page).toHaveURL('/')
  })
})

test.describe('Protected Routes', () => {
  test('should redirect unauthenticated users to login', async ({ page }) => {
    // Try to access protected routes without authentication
    const protectedRoutes = ['/tenants', '/cores', '/modules']
    
    for (const route of protectedRoutes) {
      await page.goto(route)
      await expect(page).toHaveURL('/')
    }
  })

  test('should allow authenticated users to access protected routes', async ({ page }) => {
    // Mock authenticated user
    await page.route('**/api/auth/me', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          user: {
            id: 'test-user-id',
            email: '<EMAIL>',
          },
        }),
      })
    })

    // Set session cookie
    await page.context().addCookies([{
      name: 'session',
      value: 'valid-session-token',
      domain: 'localhost',
      path: '/',
    }])

    // Access protected route
    await page.goto('/tenants')
    await expect(page).toHaveURL('/tenants')
  })
})

test.describe('User Interface', () => {
  test('should be responsive on mobile devices', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 })
    
    await page.goto('/')
    
    // Check if login form is properly displayed on mobile
    await expect(page.locator('input[type="email"]')).toBeVisible()
    await expect(page.locator('input[type="password"]')).toBeVisible()
    await expect(page.locator('button[type="submit"]')).toBeVisible()
  })

  test('should have proper accessibility attributes', async ({ page }) => {
    await page.goto('/')
    
    // Check for proper labels and ARIA attributes
    await expect(page.locator('input[type="email"]')).toHaveAttribute('aria-label')
    await expect(page.locator('input[type="password"]')).toHaveAttribute('aria-label')
    await expect(page.locator('button[type="submit"]')).toHaveAttribute('aria-label')
  })

  test('should support keyboard navigation', async ({ page }) => {
    await page.goto('/')
    
    // Tab through form elements
    await page.keyboard.press('Tab')
    await expect(page.locator('input[type="email"]')).toBeFocused()
    
    await page.keyboard.press('Tab')
    await expect(page.locator('input[type="password"]')).toBeFocused()
    
    await page.keyboard.press('Tab')
    await expect(page.locator('button[type="submit"]')).toBeFocused()
  })
})
