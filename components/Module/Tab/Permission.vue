<script setup lang="ts">
import { ModuleTabNames } from '~/constants/tabs'

const permissionStore = useConfigPermissionStore()
const workflowStore = useWorkflowsStore()
const { activeWorkflow } = storeToRefs(workflowStore)

const router = useRouter()
const route = useRoute()
onMounted(() =>
{
    router.replace({
        query: {
            ...route.query,
            tab: ModuleTabNames.PERMISSIONS
        }
    })
    permissionStore.initialisePermissionStoreForWorkflow()
})

watch(activeWorkflow, () =>
{
    permissionStore.initialisePermissionStoreForWorkflow()
})
</script>

<template>
    <ConfigPermissionPage />
</template>
