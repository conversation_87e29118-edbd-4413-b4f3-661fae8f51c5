<script setup lang="ts">
  import { SiteCollectionTabNames } from '~/constants/tabs'
  import { RegisterFor } from '~/stores/configRegister'
  import { useSetRouteTab } from '~/composables/useSetRouteTab'

  const registerStore = useConfigRegisterStore()
  const siteCollectionStore = useSiteCollectionsStore()
  const { activeSiteCollection } = storeToRefs(siteCollectionStore)
  const { setTab } = useSetRouteTab()

  onMounted(() => {
    setTab(SiteCollectionTabNames.DCC)
    registerStore.initialiseRegisterFor(RegisterFor.SITEDCC)
  })

  watch(
    () => activeSiteCollection.value,
    () => {
      registerStore.initialiseRegisterFor(RegisterFor.SITEDCC)
    },
  )
</script>

<template>
  <ConfigRegisterPage />
</template>
